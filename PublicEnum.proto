syntax = "proto3";
option go_package = "liteframe/internal/common/protos/public";

option csharp_namespace = "Game.Core";



//=====================平台能力开始================================
enum LoginByType
{
	LoginByType_Other = 0;
	LoginByType_QQGameCenter = 1;
	LoginByType_WeChatGameCenter = 2;
}
//=====================平台能力结束================================

//货币类型
enum MoneyType
{
	MoneyType_None = 0;				//未知的类型
	MoneyType_Gold = 1;				//金币
	MoneyType_Diamond = 2;			//钻石
	MoneyType_Power = 3;			//体力
	MoneyType_Exp = 4;				//经验
}
//掉落类型
enum AwardItemType
{
    AwardItemType_Money 	= 0;			//货币
	AwardItemType_Item 		= 1;			//普通物品
	AwardItemType_Equip 	= 2;			//装备
	AwardItemType_Skill 	= 3;			//技能
	AwardItemType_Gem 	= 4;			    //宝石
}

//完成枚举

//奖励枚举

//关卡阶段

//关卡结果类型

//功能解锁状态
enum FuncLockType
{
   FuncLockType_Lock=0;    //未解锁
   FuncLockType_UnLock=1;  //解锁
   FuncLockType_Close=2;   //关闭
}

//SKill状态类型

//场景掉落类型
enum SceneDropType
{
    SceneDropType_Monster = 0;                   //小怪
    SceneDropType_Horse= 1;                //彩虹马
}


//红点枚举
enum RedDotType
{
    RedDotType_None=0;				//
	RedDotType_Skill=1;				//技能红点		
	RedDotType_Mail=2;				//邮件红点		
	RedDotType_Friend=3;	        //好友红点
	RedDotType_FuncPreview=4;       //功能预告
	RedDotType_HeavenlyDao=5; 		//天道修为
	RedDotType_DailyPlusGift=6;     //每日特惠礼包
	RedDotType_MAX=7;	            //放在最后
}

//性别枚举
enum EGenderType
{
	Default = 0; //神秘
	Man = 1; //男性
	Woman = 2; //女性
}

//=====================战斗相关开始================================

//技能类型

//技能子类型


// 技能效果 类型


//属性
enum AttributeType
{
	AttributeType_NONE=0;															//空
	AttributeType_Attack = 1;															//基础攻击力
	AttributeType_Hp = 2;															//血量
	AttributeType_Def = 3;															//基础防御
	AttributeType_MoveSpeed = 4;													//移速
	AttributeType_GunAtk = 5;														//枪械攻击力
	AttributeType_PhysicsAtk = 6;													//物理攻击力
	AttributeType_IceAtk = 7;														//冰攻击力
	AttributeType_ElectricityAtk = 8;												//电攻击力
	AttributeType_FireAtk = 9;														//火攻击力
	AttributeType_EnergyAtk=10;														//能量攻击力
	AttributeType_WindAtk =11;														//风攻击力
	AttributeType_Penetrate=12;														//穿透值
	
	AttributeType_HitPre=20;														//命中
	AttributeType_DodgePre=21;														//闪避
	AttributeType_AttackPre = 22;													//基础攻击百分比
	AttributeType_HpPre = 23;														//血量百分比
	AttributeType_DefPre = 24;														//基础抗百分比
	AttributeType_MoveSpeedPre = 25;												//移动速度百分比
	AttributeType_CriticalPre = 26;													//暴击率
	AttributeType_CriticalMultiplePre = 27;											//暴击倍数
	AttributeType_CriticalResistPre = 28;											//暴击抵抗
	AttributeType_BaseDamagePre = 29;												//输出伤害增幅
	AttributeType_GunDamagePre = 30;												//枪械伤害增幅
	AttributeType_PhysicsDamagePre = 31;											//物理伤害增幅
	AttributeType_IceDamagePre = 32;												//冰伤害增幅
	AttributeType_ElectricityDamagePre = 33;										//电伤害增幅
	AttributeType_FireDamagePre = 34;												//火伤害增幅
	AttributeType_EnergyDamagePre = 35;												//能量伤害增幅
	AttributeType_WindDamagePre = 36;												//风伤害增幅
	AttributeType_PenetratePre = 37;												//穿透值百分比
	AttributeType_GunDefPre = 38;													//枪械抵抗
	AttributeType_PhysicsDefPre = 39;												//物理抵抗
	AttributeType_IceDefPre = 40;													//冰抵抗
	AttributeType_ElectricityDefPre = 41;											//电抵抗百分比
	AttributeType_FireDefPre = 42;													//火抵抗百分比
	AttributeType_EnergyDefPre = 43;												//能量抵抗百分比
	AttributeType_WindDefPre = 44;													//风抵抗百分比
	AttributeType_GroundDemageAddPre = 45;											//地面单位伤害增幅
	AttributeType_MidairDamageAddPre = 46;											//空中单位伤害增幅
	AttributeType_NearDamageAddPre = 47;											//近战单位伤害增加
	AttributeType_LongDisDamageAddPre = 48;											//远程单位伤害增幅
	AttributeType_SmallMonsterDamageAddPre = 49;									//小怪的伤害加成
	AttributeType_EliteMonsterDamageAddPre = 50;									//精英伤害加成
	AttributeType_BossMonsterDamageAddPre = 51;										//Boss伤害加成
	AttributeType_DecreaseGunCD = 52;												//减少枪械CD
	AttributeType_DecreaseSkillCD = 53;												//减少技能CD
	
	AttributeType_GoldBonusPre = 80;												//金币加成
	AttributeType_ExpBonusPre = 81;													//经验加成
	AttributeType_WarCoinPre = 82;													//战币加成
	AttributeType_Magazine = 83;													//弹夹数量
	AttributeType_MagazinePre = 84;													//弹夹数量百分比

}

//职业类型  1、物理    2、冰系  3、电系  4、火系  5、能量   6、风系

// 攻击距离类型（1近战   2远程）


//空中类型（ 1、地面 2、空中）


//天气的类型


//正反面的处理和判断


//怪物的类型
enum MonsterType
{
	MonsterType_None = 0;							//无
	MonsterType_Small = 1;							//小怪
	MonsterType_Advanced = 2;						//精英
	MonsterType_Boss = 3;							//Boss
}



//战斗的类型或是副本类型
enum BattleType
{
	BattleType_Common = 0;				//主线的关卡
	BattleType_StandingPile = 100;		//主界面站桩的副本信息
}

// 战斗状态枚举
enum BattleState {
    STATE_NONE = 0;
    STATE_ROUND_START = 1;          // 回合开始（匹配对手）
    STATE_PREPARATION = 2;          // 准备阶段（包含buff选择、英雄生成、自由操作）
    STATE_BATTLE_STARTING = 3;      // 战斗开始（所有玩家准备完毕或超时）
    STATE_BATTLE_IN_PROGRESS = 4;   // 战斗进行中（两场战斗都在进行）
    STATE_ROUND_SETTLEMENT = 5;     // 回合结算（两场战斗都结束）
    STATE_ELIMINATION_CHECK = 6;    // 淘汰检查
    STATE_GAME_OVER = 7;            // 游戏结束
}

enum InstanceState {
    INSTANCE_WAITING = 0;            // 等待战斗开始
    INSTANCE_BATTLE_IN_PROGRESS = 1; // 战斗进行中
    INSTANCE_BATTLE_FINISHED = 2;    // 战斗结束，等待另一场
    INSTANCE_SETTLEMENT = 3;         // 结算中
}

enum PlayerState {
    PLAYER_WAITING = 0;             // 等待中
    PLAYER_SELECTING_BUFF = 1;      // 选择Buff中
    PLAYER_RECEIVING_HEROES = 2;    // 接收英雄中
    PLAYER_FREE_OPERATION = 3;      // 自由操作中
    PLAYER_READY = 4;               // 已准备
    PLAYER_IN_BATTLE = 5;           // 战斗中
    PLAYER_ELIMINATED = 6;          // 已淘汰
}

// 战斗结算额外奖励机制
enum AdRewardType {
	BATTLE_ADREWARD_NONE = 0;
	BATTLE_SUPPLY_DROP = 1;			// 对战补给
	BATTLE_BLESSING = 2;			// 对战庇佑
}
//======================战斗相关结束================================
//抽卡类型
enum GachaType
{
	GachaType_Equip = 0; //装备
	GachaType_Skill = 1; //技能
	GachaType_Hallows = 2; //圣物
	GachaType_Pets = 3; //宠物
}
//装备类型
enum EquipType
{
    Equip_NONE    = 0; //占位
	Equip_Bracer  = 1; //护臂
	Equip_Helmet  = 2; //头盔
	Equip_Shoes   = 3; //鞋子
	Equip_Pants   = 4; //裤子
	Equip_Gloves  = 5; // 手套
	Equip_Clothes = 6; // 衣服
}
//大数值公式类型
enum DropExpressType
{
	Express_Normal = 0; //普通计算公式（只返回base值）
	Express_Stages = 1; //关卡计算公式
}
//龙属性

//统一品质类型
enum QualityType
{
	Quality_Grey 	= 0; //灰
	Quality_Green 	= 1; //绿
	Quality_Blue 	= 2; //蓝
	Quality_Purple 	= 3; //紫
	Quality_Gold 	= 4; //金
	Quality_Orange	= 5; //橙
	Quality_Red 	= 6; //红
	Quality_DeepRed = 7; //深红
	Quality_Args8 	= 8; //预留
}
//龙蛋类型




//邮件的类型
enum MailType
{
	MailType_NonReward = 0;//非奖励类型邮件
	MailType_Reward = 1;    // 有奖励邮件
	MailType_NotifyReward = 2;//通知奖励类型邮件
	MailType_PlayerAuth = 3; //玩家权限控制刷新-全
	MailType_PlayerAuthInfo = 4; //玩家权限控制刷新-单或组
	MailType_PlayerMailRemove = 5; //删除玩家身上的多个邮件
	MailType_GiftRMB = 6; //道具直购（所有付费都走邮件发放）
	MailType_GlobalActivityPlayer = 7; //GlobalServer发送的玩家个人邮件
	MailType_AD = 8;	//后台广告回调邮件
	MailType_GuildOfflineMsg = 9;	//公会操作时对离线玩家通知（写入离线数据）
	MailType_QuestAward= 10;	//问卷奖励
	MailType_Activity_OpenServer_End = 11;	//开服活动结束
}
//邮件的子类型


//邮件的打开类型
enum MailStateType
{
	MailStateType_UnRead = 0;  //未读
	MailStateType_Read = 1;    //已读
	MailStateType_GotAward = 2;//已领取
}


//邮件的打开类型
enum MailReasonType
{
	MailReasonType_TestGM = 0;// 测试
	MailReasonType_GMRemote = 1;//GM管理员
	MailReasonType_System = 2;//系统邮件
}
//邮件模板

//旧版新手引导触发类型
enum GuideTriggerType
{
	None = 0;
	NewPlayer = 1;
	OpenUi = 2;
	CloseUi = 3;
	UnlockFuncId = 4;
	PassedMainLine = 5;
	FinishGuide = 6;
}


//新手引导的触发类型
enum NewGuideTriggerType
{
	NewGuideTriggerType_Normal = 0;		//无效的触发
	NewGuideTriggerType_GetCompleteTask = 1;	//领取主线完成任务
	NewGuideTriggerType_MissionFinish = 2;	//完成任务
	NewGuideTriggerType_NewGuildEnd = 3;	//新手结束触发下一个新手
	NewGuideTriggerType_InstanceEnd = 4;	//金币 经验 主线 成功结束
	NewGuideTriggerType_CreateName= 5;	//起名
	NewGuideTriggerType_CooperationStage= 6;	//玩家首次挑战合作舞台挑战胜利触发引导
	NewGuideTriggerType_LotteryEnd= 7;	//抽卡结束
}
//新手引导的功能类型
enum NewGuideFunType
{
	NewGuideFunType_Normal = 0;				//无效的触发
	NewGuideFunType_ChangeName = 1;			//起名
}

//签到的类型
enum SignInToType
{
	SignInToType_None = 0;				//未知的类型
	SignInToType_SevenDay = 1;			//七天签到
	SignInToType_EveryDay = 2;			//每天签到
	SignInToType_MonthStage = 3;		//每月阶段奖励
}
//奖励状态
enum RewardStatus
{
	RewardStatus_Doing = 0;				//未完成
	RewardStatus_Finish= 1;			//已完成
	RewardStatus_Received = 2;			//已领奖
}
//奖励类型
enum RewardType {
    REWARD_TYPE_UNKNOWN = 0;
    REWARD_TYPE_RANK = 1;         // 普通段位奖励
    REWARD_TYPE_SEASON_RANK = 2;  // 赛季段位奖励
}
//-------------------------------------------------道具类型定义 Begin-------------------------------------------------

//道具类型
enum ItemType
{
	Unavailable = 0;//不可使用类
	Drop = 1;//掉落类型
	HangUpReward = 2; //挂机奖励类型
	ChooseBox = 3;//自选宝箱
	Devris = 4; //碎片类型
}

enum ItemSubType_ChooseBox
{
	ChooseBox_Once = 0; //只能选一个
	ChooseBox_Multi = 1; //可批量多选
}

//道具子类型 (掉落类型)
enum ItemSubType_Drop
{
	Drop_Fixed = 0; //固定类型
	Drop_Random = 1; //随机类
}

//道具子类型 (碎片类型)
enum ItemSubType_Devris
{
	Devris_Skin = 0; //皮肤
	Devris_Firearm = 1; //枪械
	Devris_Wall = 2; //城墙
	Devris_Pet = 3; //宠物
}

//-------------------------------------------------道具类型定义 End-------------------------------------------------

//背包对象结构类型
enum BagItemType
{
	NormalItem = 0; //常规物品类型
}
//召唤宝箱池子类型


//系统提示
enum SystemShowErrorType
{
	SystemShowErrorType_None = 0;				//没有特殊需求的error
	SystemShowErrorType_Kick = 1; 				//踢人
	SystemShowErrorType_Replace = 2;			//顶号
	SystemShowErrorType_ChatBan = 3;			//禁言
	SystemShowErrorType_Tips = 4;				//仅弹窗提示
	SystemShowErrorType_Login = 5;				//登录
}



//礼包类型
enum GiftPacksType
{
	GiftPacksType_None 		= 0;
	GiftPacksType_Limit 	= 1;	//限时礼包
	GiftPacksType_Weekly 	= 2;	//
	GiftPacksType_DailyPlus = 3; 	//每日特惠礼包
	GiftPacksType_WeekCard  = 4; 	//周卡礼包
	GiftPacksType_Recommend = 5; 	//推荐礼包
}

//礼包购买类型
enum GiftPacksBuyType
{
	GiftPacksBuyType_Adv = 0;	//广告购买
	GiftPacksBuyType_Rmb = 1;	//人民币购买
	GiftPacksBuyType_Free = 2;	//免费领取
	GiftPacksBuyType_Diamond = 3;	//钻石购买礼包
}


//礼包刷新类型

//月卡类型
enum ChargeType
{
	ChargeType_None = 0; //
	ChargeType_Diamond = 1; //钻石档位
	ChargeType_MonthCardNormal = 2; //普通
	ChargeType_MonthCardSuper = 3; //超级
	ChargeType_Item = 4; //道具直购
}

//通用阶段宝箱 特殊固定类别
enum CommonBoxRewardType
{
  CommonBoxReward_None 	 = 0; //
  CommonBoxReward_DailyMission 	 = 1; //每日宝箱
  CommonBoxReward_SevenDayActivity 	 = 2; //七日任务活动宝箱
}
//=====================战令开始===================================
// 战令类型
enum BattlePassType
{
	BattlePassType_NONE = 0;	// 未购买
	BattlePassType_BASE = 1;	// 基础战令
	BattlePassType_ADVANCE = 2;	// 进阶战令
}
// 战令任务类型
enum BattlePassMissionType
{
	BattlePassMissionType_NONE = 0;				// 非法
	BattlePassMissionType_DAILY = 1;			// 每日
	BattlePassMissionType_WEEKLY = 2;			// 每周
}
// 战令奖励领取状态
enum AwardState
{
	AwardState_UNAVAILABLE = 0;		// 未领取
	AwardState_AVAILABLE = 1;    	// 可领取
	AwardState_RECEIVED = 2;		// 已领取
}
//=====================战令结束===================================
// 活动类型
enum ActivityType
{
	ActivityType_BattlePass = 0;	//0.战令
	ActivityType_SevenTask	= 1;	//1.七日任务（仅占位）
	ActivityType_SevenSign	= 2;	//2.七日签到（仅占位）
}
// 活动时间计算类型

// 活动状态
enum ActivityStatus
{
	ActivityStatus_NO_OPEN 	= 0;	//0.未开启
	ActivityStatus_START	= 1;	//1.已开始（系统预处理阶段，服务器使用）
	ActivityStatus_END		= 2;	//3.已结束（客户端判断此状态为活动正式结束）
}

//头像类型

//=====================道具前往获取开始===================================
enum ItemAcquisitionState
{
	ItemAcquisitionState_NONE 				= 0;		// 0.表示不可用
	ItemAcquisitionState_Diamond 			= 1;    	// 钻石
	ItemAcquisitionState_Star 				= 2;		// 星星
	ItemAcquisitionState_Essence 			= 3;		// 神之精华
	ItemAcquisitionState_AirPurifier 		= 4;		// 空气净化器
	ItemAcquisitionState_UpgradeTicket 		= 5;		// 建筑升级门票
    ItemAcquisitionState_DuplicateTicket 	= 6;		// 副本门票
    ItemAcquisitionState_Shovel 			= 7;		// 铲子
    ItemAcquisitionState_DragonEgg 			= 8;		// 龙蛋
    ItemAcquisitionState_MagicCloth 		= 9;		// 魔法布料
    ItemAcquisitionState_Chisel 			= 10;		// 凿子
	ItemAcquisitionState_DragonCompoundSlotUpgrade			= 11;		// 龙底座升级材料
	ItemAcquisitionState_SlimeRecurit	    = 12;		// 史莱姆招募道具
	ItemAcquisitionState_DragonFactoryLevelItemRob	= 13;		// 悬空工厂升级道具
	ItemAcquisitionState_DragonBuildResource = 14;		// 其他建筑升级道具
	ItemAcquisitionState_DragonFactoryTech = 15;		// 科技点道具获取
	ItemAcquisitionState_GuildFactoryItem = 16;		// 公会科技道具（远古晶石
	ItemAcquisitionState_FashionBTSItem = 17;		// 技能突破道具获取
}
//=====================道具前往获取结束===================================
//排行榜类型

//排行榜顺序

//时装类型

//=====================视频广告的类型开始==================================
// 激励视频
enum IronSourceADype
{
	IronSourceADype_NONE 							= 0;		// 0.表示不可用
	IronSourceADype_OffLine 						= 1;    	// 挂机奖励X1
}
//=====================广告的类型结束===================================

//=====================推送类型开始==================================
// 推送类型

//=====================推送类型结束===================================



//=====================公会系统开始==================================
//公会成员身份，保持权限递增的顺序
enum GuildPosition
{
	GuildPosition_Normal 	= 0;	//普通成员
	GuildPosition_Elite 	= 1;	//精英成员
	GuildPosition_Vice	 	= 2;	//长老（副会长）
	GuildPosition_President = 3;	//会长
}
//公会捐献类型

//公会商品类型

//公会操作（仅列出需要验证权限的操作）
enum GuildOpt
{
    GuildOpt_None               = 0;        // 0.无操作
    GuildOpt_EditIcon           = 1;        // 1.修改旗帜
    GuildOpt_EditName           = 2;        // 2.修改名字
    GuildOpt_EditNotice         = 3;        // 3.修改宣言
    GuildOpt_EditApply          = 4;        // 4.修改申请条件
    GuildOpt_ApplyMgrList       = 5;        // 5.申请列表查看
    GuildOpt_ApplyMgrAgree      = 6;        // 6.同意申请
    GuildOpt_ApplyMgrRefuse     = 7;        // 7.拒绝申请
    GuildOpt_MemberMgrList      = 8;        // 8.成员管理列表查看
    GuildOpt_MemberMgrPresident = 9;        // 9.任命会长
    GuildOpt_MemberMgrVice      = 10;       // 10.任命副会长
    GuildOpt_MemberMgrElite     = 11;       // 11.认命精英
    GuildOpt_MemberMgrNormal    = 12;       // 12.任命为普通成员
    GuildOpt_MemberMgrKick      = 13;       // 13.踢出公会
    GuildOpt_Quit               = 14;       // 14.退出公会
    GuildOpt_Dismiss            = 15;       // 15.解散公会
    GuildOpt_Impeach            = 16;       // 16.弹劾会长
    GuildOpt_SendWorldInvite    = 17;       // 17.发送世界邀请
    GuildOpt_SendPlayerInvite   = 18;       // 18.发送私聊邀请
    GuildOpt_BargainingNotice   = 19;       // 19.提醒砍价
    GuildOpt_EditAnnouncement   = 20;       // 20.修改公告
    GuildOpt_ApplyMgrRejectAll  = 21;       // 21.一键拒绝所有申请
}
//公会离线消息类型（通知离线玩家写入数据）

//公会日志类型
enum GuildLogType
{
	GuildLogType_Leave			= 0;		//0.离开公会
	GuildLogType_Join			= 1;		//1.加入公会
	GuildLogType_Impeach1		= 2;		//2.弹劾成功
	GuildLogType_Impeach2		= 3;		//3.被弹劾
	GuildLogType_MgrVice		= 4;		//4.任命副会长
	GuildLogType_MgrNormal		= 5;		//5.任命为普通成员
	GuildLogType_Donate			= 6;		//6.捐献增加经验
	GuildLogType_President		= 7;		//7.成为会长
	GuildLogType_AutoChange1	= 8;		//8.转让成功为会长
	GuildLogType_AutoChange2	= 9;		//9.被转让出会长，降为成员
}
//公会事件类型（用于广播给成员处理）

// 公会通知
// 用于 G2PGuildGeneralUpdateNtf，告知 Player Actor 其公会相关状态需要更新的具体类型
enum GuildUpdateType {
    GuildUpdateType_UNKNOWN = 0; 			// 未知或默认类型，不应实际使用
    // --- 玩家与公会的从属关系变化 ---
    GuildUpdateType_JOINED_GUILD = 1;       // 玩家成功加入了一个新的公会 场景: 申请被批准后；快速加入成功后。
    GuildUpdateType_LEFT_GUILD = 2;         // 玩家主动退出了当前公会 场景: 玩家执行退出公会操作成功后。
    GuildUpdateType_KICKED_FROM_GUILD = 3;  // 玩家被从当前公会踢出 场景: 官员执行踢人操作，目标是此玩家。
    GuildUpdateType_GUILD_DISMISSED = 4;    // 玩家所在的公会被解散 (无论是会长主动还是系统自动) 场景: 玩家是某公会成员，该公会被解散。
    // --- 玩家在公会内的状态变化 ---
    GuildUpdateType_POSITION_CHANGED = 5;   // 玩家在当前公会的职位发生了变化 场景: 玩家被任命为新的职位 (会长、副会长、精英、普通成员)。
    // --- 公会本身的状态变化 (通知成员) ---
    GuildUpdateType_GUILD_LEVEL_UP = 6;     // 玩家所在的公会等级提升 场景: 公会经验达到升级条件，或GM指令提升等级。
    // --- 玩家申请列表状态变化 ---
    GuildUpdateType_APPLY_PROCESSED_OR_EXPIRED = 7; // 玩家之前对某个公会的申请已被处理(同意/拒绝)或已过期 场景: 官员处理了玩家的申请；或玩家的申请记录因超时被系统清理。
}

// 公会申请记录的状态
enum GuildApplicationStatus {
    APPLICATION_STATUS_UNKNOWN = 0;     // 未知或无效状态。
    APPLICATION_STATUS_PENDING = 1;     // 申请已提交，等待联盟官员审批。
    APPLICATION_STATUS_APPROVED = 2;    // 申请已被联盟官员批准。
    APPLICATION_STATUS_REJECTED = 3;    // 申请已被联盟官员拒绝。
    APPLICATION_STATUS_EXPIRED = 4;     // 申请因超时（例如24小时未被处理）而自动失效。玩家未能加入联盟。
}

// 公会内部通知类型
enum GuildSystemInternalActionType {
    INTERNAL_ACTION_UNKNOWN = 0;
    INTERNAL_ACTION_DISMISS_GUILD_MEMBERS = 1; 			// 公会解散，通知成员
    INTERNAL_ACTION_GUILD_LEVEL_UP_NOTIFY_MEMBERS = 2;  // 联盟升级，通知成员
}
//=====================公会系统结束===================================
//=====================聊天系统结束===================================
//聊天类型
enum ChatType
{
   ChatType_World = 0;                 
   
   ChatType_Guild= 1;              
   ChatType_Private= 2;               
}
//聊天信息类型
enum ChatMsgType
{
   ChatMsgType_Text 		= 0;	//普通文本
   ChatMsgType_GuildInvite	= 1;	//公会邀请信息
   ChatMsgType_System	= 2;	//系统信息
}
//=====================聊天系统结束===================================

//战斗内触发逻辑打开窗口类型
enum InBattleShowWindowType
{
   UI_Select_NormalPop 		= 0;	//三选一buff
   UI_Select_ShopPop		= 1;	//商店购买buff
   UI_Select_LuckyPop		= 2;	//击杀boss或精英怪物掉落buff
}
//全局广播事件类型
enum GlobalBroadcastEventType
{
	GlobalBroadcastEventType_None					= 0;		//默认无处理
	GlobalBroadcastEventType_BanPlayerGuildBossRank = 1;		//全局封禁玩家所在公会的BOSS排行信息，伤害置为0
	GlobalBroadcastEventType_ChgPlayerName			= 2;		//强制修改玩家名称
	GlobalBroadcastEventType_ChgGuildNameAndNotice	= 3;		//强制修改公会名称和公告信息
}

//=====================七日签到开始===================================
enum SignInState
{
    NoSignIn		= 0;		//未签到
    SignedInNoRe	= 1;		//已签到未领取
    SignedInAndReed = 2;		//已签到已领取
}
//=====================七日签到结束===================================
//======================支付开始=========================================
enum PaymentModuleType
{
	Shop = 0;                           // 商店
	FirstCharge = 1;                    // 首充
	MonthlyCard = 2;                    // 月卡
	GradedFund = 3;                     // 基金
	MonthlyCardNew = 4;                 // 月卡2.0
	TimeShop = 5;                       // 限时商店(日、周、月礼包)
}
//======================支付结束=========================================

//======================任务开始=========================================
//任务状态
enum MissionState
{
	MissionState_Unfinished = 0;		//未完成
	MissionState_Finished = 1;			//已完成
	MissionState_Received = 2;			//已领取
}

//任务类型
enum MissionType
{
	MissionType_Main = 0;				//主线任务
	MissionType_Daily = 1;				//日常任务
	MissionType_SevenDay = 2;			//七日任务
	MissionType_Achievement = 3;		//成就任务
	MissionType_HeavenlyDao = 4;		//天道修为
}

//=======================任务结束========================================

//=====================千抽开始===================================
//千抽状态
enum DBGachaBonusState
{
    NoPull		= 0;		//未抽卡
    PulledNoRe	= 1;		//已抽未领取
    PulledAndReed = 2;		//已抽已领取
}
//=====================千抽结束===================================

// 错误码
enum ServerResultCode
{
	ResultCode_SUCCES = 0; // 成功
	ResultCode_Error = 1; //  服务端报错
	ResultCode_Currency_Not_Enough = 2; // 金币不足
	ResultCode_Item_Not_Enough = 3; // 道具不足
	ResultCode_PARAM_ERROR = 4; // 客户端参数错误
	ResultCode_CONFIG_NOT_CONTAINS = 5; // 配置不存在错误
}

//=======================功能预告奖励================================
enum FuncPreviewstate
{
	Lock = 0; // 未解锁
	Award = 1; // 可领奖
	Awarded = 2; //已领奖
}

//=================== 宝物系统开始 ========================
// 宝物抽卡的消耗类型
enum TreasureGachaCostType {
    COST_TYPE_NONE = 0;
    COST_TYPE_ITEM = 1;     // 消耗道具 (例如: 宝物抽卡券)
    COST_TYPE_AD = 2;       // 观看广告
}
//=================== 宝物系统结束 ========================