syntax = "proto3";
option go_package = "liteframe/internal/common/protos/public";

option csharp_namespace = "Game.Core";


import "PublicEnum.proto";

//玩家基础数据
message PBPlayerInfo
{
	int64 platformID = 1;    //id
	string nickName = 2;    //名字
	string headIcon = 3;  //头像
	int32 level = 4;   //等级
	int32 FrameId = 5; //头像框 id
	repeated int32 missionData = 6;  //MissionData
	int32 questionnaireProgress = 7;  // 问卷进度，初始为 0.MSDK 版本该值 0 代表 PublicEnum UserQuestionState
	int64 Exp = 8; //经验（大数值表示）
	PBPlayerMoneyInfo moneyData = 9;  //代币信息
	int64 frameExpireTime = 10; //头像框过期时间
	string worldChatRoom = 11; //世界聊天频道
	int64 pChatCleanTime = 12; //聊天屏蔽时间戳
	int32 PlayerShareTime = 13;// 玩家分享次数
	int32 AreaID = 14;		//玩家大区 ID
	LoginByType gameCenterLoginType = 15;//游戏中心登录类型
	PBPlayerTitleData titleData = 16;//称号信息
	bool showSDK = 17;//是否显示 SDK
	int32 birthday = 18;//生日 格式：月*100 + 日  6 月 1 日：601
	bool showKLCDK = 19;	//是否显示口令 cdk
	int32 modelId = 20;				//模型的 id 信息
	repeated PBAttributeInfo attributeLevelupList = 21;//玩家属性升级系统 使用的 属性集合
	PBTalentData talentData = 22; //天赋系统数据
	repeated int32 equipSlots = 23; //角色槽位信息，下标为装备类型位置 EquipType，值为装备 ID，为 0 表示当前槽位空
	repeated int32 hallowsSlots = 24; //圣物槽位信息，下标为圣物类型位置 HallowsType，值为圣物 ID，为 0 表示当前槽位空
	repeated int32 petSlots = 25; //宠物槽位信息，下标为宠物类型位置 HallowsType，值为宠物 ID，为 0 表示当前槽位空
	PBSettingData settingData = 26;	//玩家设置相关数据
	int32 bornServerId = 27; //出生服 ID，用于支付订单账号校验
	int32 loginServerId = 28; //当前登录服 ID，用于统计
	string serverCodeSvnVersion = 29; //服务器最新版本号（整包维护和热更新都会更改此版本号）
	int64 openServerTime = 30;//开服时间戳
	string sigin = 31; //个性签名
	EGenderType gender = 32; //玩家性别，对应 GenderType 枚举
}
//天赋系统数据（有些字段可能不赋值）
message PBTalentData
{
	int32 resetTalentCount = 1;//天赋系统已重置次数
	int32 spareTalentCount = 2;//天赋系统剩余点数
	int32 totalTalentCount = 3;//天赋系统总点数
	repeated PBAttributeInfo attributeTalentList = 4;//玩家天赋系统 使用的 属性集合
}

//玩家设置相关
message PBSettingData
{
	bool isShowPush = 1;	//true 开启推送，false 关闭推送
}

message PBHeroInfo
{
	int32 heroId = 1;   		//英雄id
	int32 heroLevel = 2;  		//当前等级
	int32 exp = 3;				//经验/碎片数量
	int32 awakeLevel = 4;		//觉醒等级
}

//金钱信息
message PBPlayerMoneyInfo{
	int32 gold = 1;   //金币
	int32 diamon = 2;  //钻石
	int32 power = 3;  //体力
	int32 refined = 4; //洗炼石
	int32 hallows = 5; //圣物币
	int32 guildCoin = 6; //公会币
}

//称号信息
message PBPlayerTitleData
{
	int32 titleId = 1; 			//称号 id
    int64 titleExpireTime = 2;  //称号过期时间
}

//设备信息
message PBDeviceInfo
{
	string ClientVersion = 1;	//客户端版本
	string SystemSoftware = 2;	//移动终端操作系统版本
	string SystemHardware = 3;	//移动终端机型
	string TelecomOper = 4;		//运营商
	string Network = 5;		//3G/WIFI/2G
	int32 ScreenWidth = 6;		//显示屏宽度
	int32 ScreenHight = 7;		//显示屏高度
	float Density = 8;		//像素密度
	string CpuHardware = 9;		//cpu 类型 | 频率 | 核数
	int32 Memory = 10;		//内存信息单位 M
	string GLRender = 11;		//opengl render 信息
	string GLVersion = 12;		//opengl 版本信息
	string DeviceId = 13;		//设备 ID，安卓上报 IMEI,IOS 上报 IDFA(报原始信息，不要加密
	int32 PlatID = 14;		//设备类型 ID：IOS 填 0，安卓填 1，不能写其它值。
	int32 RegChannel = 15;		//注册渠道
	int32 LoginChannel = 16;	//登录渠道可通过 MSDK 获取
	string secReportData = 17; //安全日志
	string deviceUDID = 18;//设备 UDID
	bool isEmulator =  19; //是否是模拟器
	string AndroidId = 20;//ANDROID_ID
	string Idfv = 21;//ios idfv，iOS 6.0 系统新增，用于给应用开发商（Vendor）标识用户
	string MAC = 22;//MAC
	string OAID = 23;//OAID
	string CAID = 24;//CAID
}

//IOS 广告信息
message PBASAIadData
{
	string IadToken = 1;	//IOS14.3 以上
	string IadError = 2;	//IOS14.3 以上
	string IadData = 3;		//IOS14.3 以下 IOS 广告数据
	string UserAgent = 4;	//浏览器用户代理
}

//=====================米大师开始=============================
//米大师：支付信息 [c#]
message PB_MidasInfo
{
	string midasToken			= 1;	//用于米大师接口调用（例如查询余额，系统赠送等）
	string pf					= 2;	//用于米大师接口调用（例如查询余额，系统赠送等）
	string pfkey				= 3;	//用于米大师接口调用（例如查询余额，系统赠送等）
}
//=====================米大师结束=============================
//通用的 key 和 value
message PBCommonKeyValue
{
	int32 key = 1;
	int32 value = 2;
	int32 type = 3;
}
//通用的 key 和 value
message PBCommonLongKeyValue
{
	int64 key = 1;
	int64 value = 2;
	int32 type = 3;
}
//通用的 key 和 value
message PBCommonIntBool
{
	int32 key = 1;
	bool value = 2;
}

//通用的 key 和 value
message PBIntPair
{
	int32 key = 1;
	int32 value = 2;
}
//=====================章节开始=============================
//掉落道具信息
message PBDropItemDataInfo
{
	int32 itemId = 1;    //道具 id
	int32 itemCount = 2;  //道具数量
	AwardItemType itemType = 3;  //道具类型
	bool highEffect = 4; //高级特效 true 表示可以闪屏
}

//道具使用参数
message UseItemParam
{
	int64 guid = 1; //物品唯一标识 ID
	int32 itemId = 2; //物品模版 ID
	int32 useNum = 3; //使用数量
	repeated PBIntPair chooseInfos = 4; //自选信息集（key:选中的 id，val:此选项的次数）
}

//=====================章节结束=============================

//====================================功能解锁====================================
//功能解锁
message PBFunctionData
{
	int32 funcId = 1;  // 功能 ID
	int32 funcStatus = 2;  // 功能状态 0关闭 1开启
}
//===================================功能解锁======================================

//=====================分歧开始=============================
//节点信息
message PBBranchFlagData
{
	int32 branchId = 1;
	int32 branchFlag = 2;
}
//红点
message PBRedDotData
{
	int32 SyncIndex = 1;   	//ID
	int32 SyncData  =2 ;    //true:1    false :0
}
//=====================分歧结束=============================


//=====================属性开始=============================

//属性类
message PBAttributeInfo
{
	AttributeType   attributeType   = 1;		   //属性枚举类型
	int32           type            = 2;           //1 整形类型 2 浮点类型 3 百分比
	int32          value	        = 3;           //值
	int32           level           = 4;           //等级
	bool            unlock          = 5;           //是否解锁
}

//=====================属性结束=============================

//=====================同步消息开始=============================
//道具信息（暂未使用）
message PBItemDataInfo
{
	int32 ItemId = 1;    //道具 id
	int32 ItemCount = 2;  //道具数量
	int32 newFlag = 3;  //新获得
}

message PBBagItemInfo
{
	BagItemType type = 1; //背包数据结构类型，详情参照枚举定义
	PBNormalItemInfo normalItem = 2; //常规物品
}

//常规道具实体
message PBNormalItemInfo
{
	int64 guid = 1; //物品唯一标识
	int32 itemId = 2; //物品表 ID
	int32 value = 3; //物品数量
}
//装备信息
message PBEquipDataInfo
{
	int32 id = 1; //装备 ID
	int32 level = 2; //当前等级
	int32 count = 3; //现有数量
	EquipType type = 4; //装备类型
	int32 OwnPro = 5; //拥有属性数值 第一条值（默认值都填为 0）
	int32 OwnPro1 = 6; //拥有属性数值 第二条值（默认值都填为 0）
	int32 DreesPro = 7; //穿戴属性数值 第一条值（默认值都填为 0）
	int32 DreesPro1 = 8; //穿戴属性数值 第二条值（默认值都填为 0）
}

//装备强化信息
message PBEquipLevelupInfo
{
	int32 id = 1; //装备 ID
	int32 oldLevel = 2; //旧等级
	int32 level = 3; //当前等级
	int32 count = 4; //现有数量
	EquipType type = 5; //装备类型
	int64 extraDiamond = 6; //升满后溢出部分转化为钻石数，0 表示无钻石产出。
	int32 OwnPro = 7; //当前 拥有属性数值 第一条值（默认值都填为 0）
	int32 OwnPro1 = 8; //当前 拥有属性数值 第二条值（默认值都填为 0）
	int32 DreesPro = 9; //当前 穿戴属性数值 第一条值（默认值都填为 0）
	int32 DreesPro1 = 10; //当前 穿戴属性数值 第二条值（默认值都填为 0）
	int32 OldOwnPro = 11; //旧 拥有属性数值 第一条值（默认值都填为 0）
	int32 OldOwnPro1 = 12; //旧 拥有属性数值 第二条值（默认值都填为 0）
	int32 OldDreesPro = 13; //旧 穿戴属性数值 第一条值（默认值都填为 0）
	int32 OldDreesPro1 = 14; //旧 穿戴属性数值 第二条值（默认值都填为 0）
}
//=====================同步消息结束=============================

//=========================邮件相关开始====================
//邮件信息
message PBMail
{
	int64              id             =1;	    //ID
	MailType           type	          =2;      	//邮件类型
	string             title          =3;	    //标题
	string             content 	      =4;	    //内容
	repeated PBCommonKeyValue goods   =5;	    //道具信息
	MailStateType      status		  =6;    	//邮件状态
	MailReasonType 	   reason		  =7;		//邮件的原因
	int64              createTime     =8;	    //发送时间
	int64              expireTime     =9;	    //过期时间
	int64              senderGuild    =10;	    //发送人 GUID
	string             sender         =11;	    //发送人名称
	int64              recerguild     =12;	    //接受者 GUID
	string             recer          =13;	    //接受者
}

//=========================邮件相关结束====================


//=====================社交分裂开始=============================
message PBFriendRasinRewardData
{
	int32 tableId = 1; //进度
	int32 rewardstatus = 2; //阶段性奖励 0 未完成 1 已完成 2 已领奖
}
message PBFriendGrowthRewardData
{
	int32 tableId = 1; //id
	int32 unlock = 2; //是否解锁 0 解锁 1 未解锁
	string playerName = 3; //玩家名称
	int64 playerUID = 4; //玩家 UID
	int32 schedule = 5; //进度
	int32 overallSchedule=6;//总进度
}
message PBFriendGrowthQuestRewardData
{
	int32 tableId = 1; //进度
	int32 rewardstatus = 2; //阶段性奖励 0 未完成 1 已完成 2 已领奖
}
//=====================社交分裂结束=============================

//====================限时礼包开始==============================

//====================限时礼包结束==============================


//=====================通用阶段宝箱相关数据=============================

//阶段宝箱相关数据
message PBCommonExpBoxInfo
{
	 int32 box_id = 1;	//宝箱经验
   int32 box_exp = 2;
   repeated PBCommonKeyValue box_index_list = 3;	    //宝箱信息
}

//======================通用阶段宝箱相关数据=============================

//通用活动信息
message PBActivityInfo
{
	ActivityType activityType			= 1;		// 活动大类型
	ActivityStatus activityStatus		= 2;		// 活动的状态
	int32 curActivityId					= 3;		// 当前活动 ID
	int64 startTime						= 4;		// 活动的开始时间
	int64 endTime 						= 5;		// 活动的结束时间
	int32 preActivityId					= 6;		// 上一期的活动 ID，业务是做判断清理数据使用。默认为 0，根据活动类型取用
	int32 closeTime						= 7;		// 活动的关闭时间
	int32 nextStartTime					= 9;		// 下一期开始时间戮，单位：秒
}

//======================挖宝活动开始=========================================

//其他玩家的基础信息
message PBPlayerBaseInfo
{
	int64 platformID = 1;    //id
	string nickName = 2;    //名字
	string headIcon = 3;  //头像
	int32 level = 4;   //等级
	int64 commValue_0 = 5; //通用的数据信息 目前用于排行的  参数 1
	int64 commValue_1 = 6; //通用的数据信息 目前用于排行的 参数 1
	int32 dragonSkinId 	= 7;	//龙皮肤，默认 0 使用 系统默认
	int32 dressId		= 8;	//时装，默认 0 使用 系统默认
	int32 weaponId		= 9;	//装备武器 ID，默认 0 使用 系统默认
	int32 rank = 10;   //名次
	int32 promotionId = 11; //当前晋升 ID，默认 0 使用 系统默认
	int64 powCombat = 12;//战力
	int32 online = 13;//玩家是否在线 1:在线 0:不在线
	int32 offlineStartTime = 14;//最后一次离线开始时间戮，单位：秒 在线后会重置为 0
	int32 mainStageId = 15;//关卡进度
	int32 dressStar=16; //时装突破
	int32 headFrameId=17; //头像框 id
	int32 titleId=18; 	 //称号 id 0 表示没有称号

}//其他玩家的详细信息（可看玩家详情使用）
message PBPlayerOtherInfo
{
	int64 platformID 					= 1;	//id
	string nickName 					= 2;	//名字
	string headIcon 					= 3;	//头像
	int32 level 						= 4;	//等级
	int32 dragonSkinId 					= 5;	//龙皮肤，默认 0 使用 系统默认
	int32 dressId						= 6;	//时装，默认 0 使用 系统默认
	int32 weaponId						= 7;	//装备武器 ID，默认 0 使用 系统默认
	int32 promotionId 					= 8;	//当前称号 ID，默认 0 使用 系统默认
	int64 powCombat					= 9;	//战力
	int64 exp							= 10;	//经验
	int32 dragonSlotLevel				= 11;	//龙的基座等级
	int32 takeOnDragonId				= 18;	//上阵龙 ID，默认 0 使用 系统默认
	bool hasGuild						= 19;	//是否有公会 true:有，false:没有
	int64 guildId						= 20;	//公会 ID
	string guildName					= 21;	//公会名称
	int32 guildLevel					= 22;	//公会等级
	int32 guildIcon						= 23;	//公会图标
	int32 showInvite					= 24;	//显示邀请按钮 0.不显示 1.发送邀请 2.已发送
	int32 dressStar						= 25; 	//时装突破
	int32 headFrameId					= 26; 	//头像框 id
	int32 titleId						= 27; 	//称号 id 0 表示没有称号
}



//======================问卷开始=========================================
//问卷信息
message PBQuestDataInfo
{
	int32 id = 1; //问卷 ID
	bool received = 2; //是否填写
	PBCommonKeyValue good = 3; //奖励的信息
	bool clicked = 4; //是否点击过
	uint64 playerId = 5;      		// 玩家 ID
}
//======================问卷结束=========================================


//======================聊天=========================================
//聊天信息
message PBChatInfo
{
	int64 platformID = 1;    //id
	string nickName = 2;    //名字
	string headIcon = 3;  //头像
	int32 level = 4;   //等级
	int32 sendTime = 5; // 时间戳
	string content     = 6;
	int64 msgID = 7;   //编号
	int32 read = 8;   //已读 1 未读 0
	ChatMsgType msgType = 9; //信息类型
	int64 guildId = 10; //公会 ID
	string guildName = 11; //公会名称
	int32 guildLevel = 12; //公会等级
	int32 guildIcon = 13; //公会图标
	int32 headFrameId = 14; //头像框 id
	int32 titleId = 15;//称号 ID 0 表示当前没有称号
}
//聊天信息
message PBPrivateChatInfo
{
	int64 chatPlayerId = 1;    //id
    repeated PBChatInfo chatinfo=2;
	string nickName = 3;    //名字
	string headIcon = 4;  //头像
	int32 level = 5;   //等级
	int32 headFrameId = 6; //头像框 id
	int32 titleId = 7;//称号 ID 0 表示当前没有称号
}
//======================聊天=========================================
//======================公会系统开始=========================================
//公会成员信息
message PBGuildMember
{
	int64 platformID 					= 1;	//id
	string nickName 					= 2;	//名字
	string headIcon 					= 3;	//头像
	int32 level 						= 4;	//等级
	int32 dragonSkinId 					= 5;	//龙皮肤，默认 0 使用 系统默认
	int32 dressId						= 6;	//时装，默认 0 使用 系统默认
	int32 weaponId						= 7;	//装备武器 ID，默认 0 使用 系统默认
	int32 promotionId 					= 8;	//当前称号 ID，默认 0 使用 系统默认
	int64 powCombat						= 9;	//战力
	bool online							= 10;	//是否在线 true:在线，false:离线读取下面的离线时间
	int32 offlineTime					= 11;	//累积的离线时长，单位：秒
	int64 contribution					= 12;	//当前贡献度
	GuildPosition gpos					= 13;	//当前身份
	int32 mainStageId					= 14;	//当前主线关卡 ID
	int32 titleId 						= 15;	//称号 id 0 表示没有称号 默认 0 使用 系统默认
	int32 headFrameId 					= 16;	//头像框 id 默认 0 使用 系统默认
}
//公会推荐列表用
message PBGuildRecommend
{
	string name 						= 1;	//名称
	int32 level 						= 2;	//等级
	int32 iconId 						= 3;	//图标
	int32 showId						= 4;	//显示 ID
	int64 id							= 5;	//ID，提交用
	string notice						= 6;	//公会宣言
	string presidentNickName			= 7;	//公会会长名字
	int64 powCombat						= 8;	//总战斗力
	int32 memberCount					= 9;	//当前人数
	int32 memberMaxCount				= 10;	//人数上限
	bool freeJoin						= 11;	//true 自由加入 false 需要审批
	int32 reqStage						= 12;	//审批时需具备的关卡条件：0.无限制 1.困难 2.疯狂 3.地狱
	bool hasApply						= 13;	//true 表示已申请，false 未申请
}
//公会申请信息
message PBGuildApply
{
	int64 platformID 					= 1;	//id
	string nickName 					= 2;	//名字
	string headIcon 					= 3;	//头像
	int32 level 						= 4;	//等级
	int32 dragonSkinId 					= 5;	//龙皮肤，默认 0 使用 系统默认
	int32 dressId						= 6;	//时装，默认 0 使用 系统默认
	int32 weaponId						= 7;	//装备武器 ID，默认 0 使用 系统默认
	int32 promotionId 					= 8;	//当前称号 ID，默认 0 使用 系统默认
	int64 powCombat						= 9;	//战力
	int64 applyTime 					= 10;   // 申请时间戳 (Unix timestamp, 秒)
	int32 mainStageId					= 11;	//当前主线关卡 ID
	int32 titleId 						= 12;	//称号 id 0 表示没有称号 默认 0 使用 系统默认
	int32 headFrameId 					= 13;	//头像框 id 默认 0 使用 系统默认
}

// 公会详细信息
message PBGuildDetailInfo {
    string name = 1;                  // 名称
    int32 level = 2;                  // 等级
    int32 icon_id = 3;                // 徽章 ID
    int32 show_id = 4;                // 显示 ID (联盟的唯一数字ID)
    int64 id = 5;                     // 联盟的数据库唯一ID
    string notice = 6;                // 联盟宣言
    string announcement = 7;          // 联盟公告
    string president_nick_name = 8;   // 公会会长名字 (可从UserSnap获取后填充)
    uint64 president_uid = 20;        // 会长UID (新增，便于客户端跳转会长信息等)
    int64 pow_combat = 9;             // 总战斗力 (需要GuildSystem计算或维护)
    int32 member_count = 10;          // 当前人数
    int32 member_max_count = 11;      // 人数上限
    bool free_join = 12;              // true 自由加入 false 需要审批
    int32 req_stage = 13;             // 审批时需具备的关卡条件
    int64 exp = 14;                   // 当前经验
    int64 max_exp = 15;               // 当前等级升级所需总经验
	int32 today_joined_count = 16;    // 今日已通过申请/快速加入的人数
    int32 daily_max_join_limit = 17;  // 服务器写死的每日最大入盟人数上限
	int64 contribution = 18;		  // 贡献度
    // 可选：其他联盟全局配置或状态
}

//公会排行列表用
message PBGuildRankInfo
{
	string name 						= 1;	//名称
	int32 level 						= 2;	//等级
	int32 iconId 						= 3;	//图标
	int32 showId						= 4;	//显示 ID
	int64 id							= 5;	//ID，提交用
	string presidentNickName			= 6;	//公会会长名字
	int64 powCombat						= 7;	//总战斗力
	bool isSelfGuild					= 8;	//true 表示自己所在的公会，false 不是自己公会
	int32 rank							= 9;	//排名从 1 开始
}
//公会科技
message PBGuildTech
{
	int32 id			= 1;	//科技配表 ID
	int32 level			= 2;	//当前等级
	int32 OwnPro 		= 3;	//拥有属性数值（默认值都填为 0）
}
//公会科技升级信息
message PBGuildTechLevelup
{
	int32 id			= 1;	//科技配表 ID
	int32 level			= 2;	//当前等级
	int32 OwnPro 		= 3;	//拥有属性数值（默认值都填为 0）
	int32 oldLevel		= 4;	//旧的等级
	int32 OldOwnPro	= 5;	//旧的属性值
}
//公会日志
message PBGuildLog
{
	GuildLogType type 					= 1;	//日志类型
	int32 createTime					= 2;	//日志产生开始时间戮，单位：秒
	int64 platformId					= 3;	//玩家 ID
	string nickName						= 4;	//玩家名字
	int32 param1						= 5;	//参数 1：经验
}
//公会砍价礼包详情
message PBBargainingGift
{
	int64 id							= 1;	//唯一 ID，购买和砍价传入
	int32 tableId						= 2;	//商品配表 ID
	int32 curPrice						= 3;	//当前价格
	int32 bargainingTotalPrice			= 4;	//累计砍价
	int32 bargainingPlayerCount			= 5;	//已参与的砍价人数
	int32 bargainingPlayerLimit			= 6;	//砍价上限人数
	repeated PBBargainingPlayer players	= 7;	//已参与人的列表信息，已排好序
	repeated PBBargainingPlayer noBargainingPlayers = 8;	//未参与人的列表信息
	int32 nextNoticeTime				= 9;	//下一次通知未砍价的时间戮，单位：秒，默认为 0，表示可以立即通知。
	bool buyGift						= 10;	//是否已购买过，True：已购买 False:未购买
	bool bargainingGift					= 11;	//是否已砍价过，True：已砍价 False:未砍价
}
//公会砍价人详情
message PBBargainingPlayer
{
	int64 platformId					= 1;	//玩家 ID
	string nickName						= 2;	//玩家名字
	int32 bargainingPrice				= 3;	//砍掉的价格数（根据需求读取对应字段）
	bool online							= 4;	//是否在线 true:在线，false:离线读取下面的离线时间（根据需求读取对应字段）
	int32 offlineTime					= 5;	//累积的离线时长，单位：秒（根据需求读取对应字段）
}
//公会 BOSs 排行成员信息
message PBGuildBossRankPlayer
{
	int64 platformID 					= 1;	//id
	string nickName 					= 2;	//名字
	string headIcon 					= 3;	//头像
	int32 level 						= 4;	//等级
	int32 promotionId 					= 5;	//当前称号 ID，默认 0 使用 系统默认
	int64 powCombat					= 6;	//战力
	int64 totalDamage					= 7;	//总伤害值
	int32 rank							= 8;	//排名从 1 开始
	int32 titleId 						= 9;	//称号 id 0 表示没有称号 默认 0 使用 系统默认
	int32 headFrameId 					= 10;	//头像框 id 默认 0 使用 系统默认
}
//公会购买记录
message PBGuildShopOrder{
    int32 id = 1;
    int32 freeCount = 2;
	int32 freeAdCount = 3;
	int32 buyCount = 4;
}
//======================公会系统结束=========================================

//======================好友=========================================
// 领取礼物界面的每条信息
message PBGiftItemData
{
	int64 playerUid = 1; 			//玩家 Uid
	string name = 2;		// 玩家名字
	int32 playerLv = 3;		// 玩家等级
	int64 time = 4;			//送礼时间戳
	string headIcon = 5;		//头像
	int32 itemId = 6;		//领取礼物道具 id（策划说现在是钻石，这个字段可以留以后用）
	int32 itemNum = 7;		//领取礼物的数量
}
//======================好友=========================================
//======================头像开始==========================================
//头像信息
message PBHeadIconDataInfo
{
	int32 	headIconId = 1; 	//头像 id
	int32  	status = 2;			//拥有状态：0:等待解锁 1：已解锁，可正常使用。
}
//======================头像结束==========================================
//======================头像框开始==========================================
//头像框信息
message PBHeadFrameInfo
{
	int32 	headFrameId = 1; 		//头像 id
	int32  	status = 2;				//拥有状态：0:等待解锁 1：已解锁，可正常使用。
	int64   frameExpireTime = 3; 	//到期时间戳  0 代表不过期
}
//======================头像框结束==========================================

//======================七日签到开始==========================================
// 七日签到信息
message PBSevenSignInfo
{
	int32 	signInDay = 1; 		// 签到天数
	int32 	signInState = 2; 	// 签到状态 [0-未签到; 1-已签到未领取; 2-已签到已领取]
}
//======================七日签到结束==========================================

//======================首冲礼包=========================================
message PBFirstChargeGiftData
{
	int32 days = 1;//天数ID
	int32 giftState = 2;//领取状态 0不能领取（未购买或领取时间未到），1 可领取，2已领取
}
message PBFirstChargeData
{
	int32 giftId = 1;
	bool isBuy = 2;
	repeated PBFirstChargeGiftData giftInfos= 3;
}
//======================首冲礼包结束=========================================

//======================充值返利开始==========================================
// 充值返利信息
message PBTopupRebateInfo
{
    int32 taskId = 1; // 任务Id
    int32 taskState = 2; // 任务状态[-1-未解锁;0-未领取; 1-已领取]
}
//======================充值返利结束==========================================

//======================月卡开始==========================================
message PBMonthlyCardInfo
{
    int32 expirationTime = 1; // 过期时间
}
//======================月卡结束==========================================

//======================月卡2.0开始==========================================
message PBMonthlyCardNewInfo
{
  int32 monthlyCardId = 1; // 月卡2.0类型[0-超值月卡; 1-至尊月卡]
  int32 expirationTime = 2; // 过期时间
}
//======================月卡2.0结束==========================================

//======================等级基金开始==========================================
message PBGradedFunds
{
    int32 levelStageIdx = 1; // 等级阶段索引
    int32 comWealState = 2; // 普通福利状态[0-未领取; 1-已领取]
	int32 superWealState = 3; // 超级福利状态[-1-未解锁;0-未领取; 1-已领取]
}
message PBGradedFundInfo
{
    int32 gradedFundIdx = 1; // 基金阶段索引
	int32 gradedFundState = 2; // 基金阶段状态[0-未购买; 1-已购买]
	repeated PBGradedFunds gradedFundInfo = 3;	// 基金阶段信息
}
//======================等级基金结束==========================================

//======================任务开始==========================================
message PBMissionInfo
{
	int32 missionId = 1; // 任务Id
	MissionState missionState = 2; // 任务状态
	repeated int32 missionParams = 3; // 任务参数列表，（第一个默认是进度）
}
//======================任务结束==========================================


//======================商城礼包=========================================
message PBShopGiftData
{
	int32 giftId = 1;//礼包ID
	bool giftState = 2;//购买状态（可购买/不可购买）
	int32 buyCount = 3;//已购买次数
	int32 giftType = 4;//礼包类型
}
//======================商城礼包结束=========================================

//======================限时商城礼包=========================================
message PBTimeGiftData
{
	int32 timeGiftId = 1;//TimeGiftPacks礼包ID
	bool giftState = 2;//购买状态（可购买/不可购买）
	int32 buyCount = 3;//已购买次数
	int32 lifeBuyCount = 4;//累计购买次数
}


//======================限时礼包结束=========================================

//======================奖励通用展示开始==========================================
message PBCommonAwardDisplay
{
    int32 itemId = 1; // 道具Id
	int32 itemNum = 2; // 道具Num
}
//======================奖励通用展示结束==========================================

//==========================测试===================
message PBTest
{
	int32 id = 1;
	int32 num = 2;
}

//======================支付=========================================
// 订单信息
message PBOrderData {
    string orderKey = 1; 			// 订单 ID，主键
    string goodsRegisterId = 2; 	// 商品注册ID
	int32 moduleType = 3;	   		// 支付对应模块
	int32 goodsPrice = 4;			// 产品实际支付价格（RMB级别为元，此参数不带引号，请用数字类型处理）
	string channelId = 5;			// 渠道标识
	string gameGoodsId = 6;			// 游戏商品ID
	uint64 playerId = 7;      		// 玩家 ID
}

// 商品信息
message PBPayGoodsInfo
{
	uint64 playerId = 1;      		// 玩家 ID
	int32 moduleType = 2;			// 支付对应模块类型
	string gameGoodsId = 3;			// 游戏商品ID
	int32 goodsPrice = 4;			// 产品实际支付价格
	int64 time = 5;					// 时间
	string orderId = 6;				// 订单编号
	string channelId = 7;			// 渠道id
	string userId = 8;				// 用户标识
	int32 state = 9;				// 订单状态
}
//======================支付=========================================

//======================监测玩家精要信息=========================================

message UserSnapUserInfo
{
	uint64 uid = 1; //玩家 ID
	int32 head_icon = 2; //头像
	bool is_online = 3; //是否在线
	string name = 4; //名字
	int32 level = 5; //等级
	int64 last_login_time = 6; //最后登录时间
	int32 last_logout_time = 7; //最后登出时间
}

message UserSnapUsers
{
	repeated UserSnapUserInfo users = 1;
}

//======================监测玩家精要信息=========================================


//================================功能预告====================================
message PBFunctionPreview
{
	int32 FuncId = 1;  // 功能 ID
	FuncPreviewstate RewardState = 2;  // 奖励状态 未解锁 未领取 已领取
}
//===============================功能预告======================================
//================================好友邀请====================================
message PBInviteTask
{
	int32 id = 1;				// 任务id
	int32 cnt = 2;				// 当前进度
	RewardStatus status = 3;	// 领奖状态
}
//===============================好友邀请======================================
//====================竞技场开始=======================
message PBArenaRivalInfo
{
	string name = 1; //名字
	uint32 level = 2; //等级
	uint32 score = 3; //积分
	int32 headIcon = 4; 	// 头像
	int32 headFrame = 5; 	// 头像框
	uint64 player_id = 6;       // 玩家 ID
}
//===================竞技场结束========================

//===============================体力开始======================================
// 一个可领取的体力奖励包
message PBPowerRewardDataInfo 
{
    int64 startTime = 1; // 体力奖励包生成的时间（Unix 时间戳，单位：秒）
    int32 powerCount = 2; // 该奖励包包含的体力数量
}
//===============================体力结束======================================
//=================== 赛季buff开始 ========================
//赛季buff信息
message PBSeasonBuff
{
	int32 buffId = 1;  //buffId
	int64 endTime = 2; //结束时间
}
//=================== 赛季buff结束 ========================


//=================== 战斗开始 ========================
//玩家战斗信息
message PBBattleHeroInfo
{
	int32 id = 1; // 英雄资源ID
	int32 level = 2; // 英雄等级(战斗外)
	int32 starLevel = 3; // 星级(战斗内)
	int32 awakeLevel = 4;// 英雄觉醒等级(战斗外)
}

//玩家战斗信息
message PBBattleTeamInfo
{
	repeated PBBattleHeroInfo heros = 1;
}

//玩家战斗信息
message PBBattlePlayerInfo
{
	uint64 uid = 1; // 玩家ID
	int32 level = 2; // 玩家等级
	string name = 3; // 玩家名字
	repeated int32 buffers = 4; //buffer
	int32 kills = 5; // 击杀数
	int32 winCount = 6; // 连胜数
	string serverId = 7;
	int32 throphy = 8; // 奖杯
	int32 hp = 9; // 血量
	repeated PBTreasureInfo treasureList = 10; // 玩家拥有的宝物列表
}

//战斗棋盘信息
message PBCheckerBoard
{
	int32 gridID = 1; // 棋盘格子ID
	PBBattleHeroInfo hero = 2; // 英雄
}

//玩家战斗状态
message PBBattlePlayerState
{
	uint64 uid = 1; // 玩家ID
	int32 state = 2; // 玩家状态，0 空闲 1 以准备 2 战斗中
}

//阵容数据
message PBBattleCampInfo
{
	repeated PBCheckerBoard boardInfo = 1; //英雄数据
	PBBattlePlayerInfo player = 2; //玩家数据
}

//单次移动操作
message PBMoveOperation {
    int32 fromGridId = 1; // 移动起始格子ID
    int32 toGridId = 2;   // 移动目标格子ID
}

// 单个玩家的棋盘信息
message PBPlayerBoard {
    uint64 uid = 1;                         // 玩家ID
    repeated PBCheckerBoard boardInfo = 2;  // 该玩家的棋盘格子信息
}
//=================== 战斗结束 ========================

//=================== 阵容开始 ========================
// 单个阵容的完整信息
message PBLineupInfo {
	int32 id = 1;              		// 阵容槽位id
	string name = 2;              // 阵容名称, e.g., "自定义1"
	repeated int32 hero_ids = 3;  // 阵容中的英雄ID列表
}
//=================== 阵容结束 ========================

//=================== 赛季开始 ========================
// 段位奖励状态信息
message PBRankRewardState {
    int32 rank_id = 1;     // 段位ID
    bool is_claimed = 2;   // 是否已领取
}

// 历史赛季的奖杯信息
message SeasonTrophyInfo {
    int32 season_id = 1;
    int32 final_trophy = 2;
}
//=================== 赛季结束 ========================

//=================== 宝物系统开始 ========================
// 单个宝物的信息
message PBTreasureInfo {
	int32 treasureId = 1;   // 宝物ID
	int32 level = 2;  		// 当前等级
	int32 star = 3;		    // 当前星级
    int32 count = 4;        // 当前拥有的该宝物【数量】(用于升星消耗)
}
//=================== 宝物系统结束 ========================