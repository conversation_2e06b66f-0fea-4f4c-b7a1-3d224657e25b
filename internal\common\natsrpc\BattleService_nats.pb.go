// Code generated by protoc-gen-rpc-wrap. DO NOT EDIT.
// versions:
// - protoc-gen-rpc-wrap v1.0.0
// - protoc             v3.16.0
// source: BattleService.proto

package natsrpc

import (
	context "context"
	fmt "fmt"
	nats_go "github.com/nats-io/nats.go"
	proto "google.golang.org/protobuf/proto"
	log "liteframe/pkg/log"
	znats "liteframe/pkg/znats"
	debug "runtime/debug"
)

var _ = new(context.Context)
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = znats.Version
var _ = nats_go.Version
var _ = log.Version
var _ = debug.Stack

// battleServiceClient is the client API for BattleService service.
type battleServiceClient struct {
	client *znats.Client
}

func NewNatsRpcBattleServiceClient(client *znats.Client) *battleServiceClient {
	return &battleServiceClient{client: client}
}

func (c *battleServiceClient) CreateBattle(ctx context.Context, req *CreateBattleReq) (*CreateBattleResp, error) {
	resp := &CreateBattleResp{}
	err := c.client.Request(ctx, "/natsrpc.BattleService/CreateBattle", req, resp)
	return resp, err
}
func (c *battleServiceClient) AsyncCreateBattle(ctx context.Context, req *CreateBattleReq, cb func(*CreateBattleResp, error)) {
	go func() {
		defer func() {
			if e := recover(); e != nil {
				trace := string(debug.Stack())
				debug.PrintStack()
				log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
			}
		}()
		resp := &CreateBattleResp{}
		err := c.client.Request(ctx, "/natsrpc.BattleService/CreateBattle", req, resp)
		cb(resp, err)
	}()
}

func (c *battleServiceClient) EnterBattle(ctx context.Context, req *EnterBattleReq, serverId string) (*EnterBattleResp, error) {
	resp := &EnterBattleResp{}
	err := c.client.Request(ctx, "/"+serverId+"/natsrpc.BattleService/EnterBattle", req, resp)
	return resp, err
}
func (c *battleServiceClient) AsyncEnterBattle(ctx context.Context, req *EnterBattleReq, serverId string, cb func(*EnterBattleResp, error)) {
	go func() {
		defer func() {
			if e := recover(); e != nil {
				trace := string(debug.Stack())
				debug.PrintStack()
				log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
			}
		}()
		resp := &EnterBattleResp{}
		err := c.client.Request(ctx, "/"+serverId+"/natsrpc.BattleService/EnterBattle", req, resp)
		cb(resp, err)
	}()
}

func (c *battleServiceClient) SelectBuffer(ctx context.Context, req *SelectBufferReq, serverId string) (*SelectBufferResp, error) {
	resp := &SelectBufferResp{}
	err := c.client.Request(ctx, "/"+serverId+"/natsrpc.BattleService/SelectBuffer", req, resp)
	return resp, err
}
func (c *battleServiceClient) AsyncSelectBuffer(ctx context.Context, req *SelectBufferReq, serverId string, cb func(*SelectBufferResp, error)) {
	go func() {
		defer func() {
			if e := recover(); e != nil {
				trace := string(debug.Stack())
				debug.PrintStack()
				log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
			}
		}()
		resp := &SelectBufferResp{}
		err := c.client.Request(ctx, "/"+serverId+"/natsrpc.BattleService/SelectBuffer", req, resp)
		cb(resp, err)
	}()
}

func (c *battleServiceClient) MergeHero(ctx context.Context, req *MergeHeroReq, serverId string) (*MergeHeroResp, error) {
	resp := &MergeHeroResp{}
	err := c.client.Request(ctx, "/"+serverId+"/natsrpc.BattleService/MergeHero", req, resp)
	return resp, err
}
func (c *battleServiceClient) AsyncMergeHero(ctx context.Context, req *MergeHeroReq, serverId string, cb func(*MergeHeroResp, error)) {
	go func() {
		defer func() {
			if e := recover(); e != nil {
				trace := string(debug.Stack())
				debug.PrintStack()
				log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
			}
		}()
		resp := &MergeHeroResp{}
		err := c.client.Request(ctx, "/"+serverId+"/natsrpc.BattleService/MergeHero", req, resp)
		cb(resp, err)
	}()
}

func (c *battleServiceClient) BattleReady(ctx context.Context, req *ReadyBattleReq, serverId string) (*ReadyBattleResp, error) {
	resp := &ReadyBattleResp{}
	err := c.client.Request(ctx, "/"+serverId+"/natsrpc.BattleService/BattleReady", req, resp)
	return resp, err
}
func (c *battleServiceClient) AsyncBattleReady(ctx context.Context, req *ReadyBattleReq, serverId string, cb func(*ReadyBattleResp, error)) {
	go func() {
		defer func() {
			if e := recover(); e != nil {
				trace := string(debug.Stack())
				debug.PrintStack()
				log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
			}
		}()
		resp := &ReadyBattleResp{}
		err := c.client.Request(ctx, "/"+serverId+"/natsrpc.BattleService/BattleReady", req, resp)
		cb(resp, err)
	}()
}

func (c *battleServiceClient) EndBattle(ctx context.Context, req *EndBattleReq, serverId string) (*EndBattleResp, error) {
	resp := &EndBattleResp{}
	err := c.client.Request(ctx, "/"+serverId+"/natsrpc.BattleService/EndBattle", req, resp)
	return resp, err
}
func (c *battleServiceClient) AsyncEndBattle(ctx context.Context, req *EndBattleReq, serverId string, cb func(*EndBattleResp, error)) {
	go func() {
		defer func() {
			if e := recover(); e != nil {
				trace := string(debug.Stack())
				debug.PrintStack()
				log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
			}
		}()
		resp := &EndBattleResp{}
		err := c.client.Request(ctx, "/"+serverId+"/natsrpc.BattleService/EndBattle", req, resp)
		cb(resp, err)
	}()
}

func (c *battleServiceClient) LeaveBattle(ctx context.Context, req *LeaveBattleReq, serverId string) (*LeaveBattleResp, error) {
	resp := &LeaveBattleResp{}
	err := c.client.Request(ctx, "/"+serverId+"/natsrpc.BattleService/LeaveBattle", req, resp)
	return resp, err
}
func (c *battleServiceClient) AsyncLeaveBattle(ctx context.Context, req *LeaveBattleReq, serverId string, cb func(*LeaveBattleResp, error)) {
	go func() {
		defer func() {
			if e := recover(); e != nil {
				trace := string(debug.Stack())
				debug.PrintStack()
				log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
			}
		}()
		resp := &LeaveBattleResp{}
		err := c.client.Request(ctx, "/"+serverId+"/natsrpc.BattleService/LeaveBattle", req, resp)
		cb(resp, err)
	}()
}

type BattleServiceServerNatsRpcServer struct {
	server *znats.Server
	BattleServiceServer
	serverId string
}

func NewBattleServiceServerNatsRpcServer(s *znats.Server, impl BattleServiceServer, serverId string) *BattleServiceServerNatsRpcServer {
	server := &BattleServiceServerNatsRpcServer{
		server:              s,
		BattleServiceServer: impl,
		serverId:            serverId,
	}
	s.QueueSubscribe("/natsrpc.BattleService/CreateBattle", "BattleService", server.CreateBattleHandlerWrap)
	s.QueueSubscribe("/"+serverId+"/natsrpc.BattleService/EnterBattle", "BattleService", server.EnterBattleHandlerWrap)
	s.QueueSubscribe("/"+serverId+"/natsrpc.BattleService/SelectBuffer", "BattleService", server.SelectBufferHandlerWrap)
	s.QueueSubscribe("/"+serverId+"/natsrpc.BattleService/MergeHero", "BattleService", server.MergeHeroHandlerWrap)
	s.QueueSubscribe("/"+serverId+"/natsrpc.BattleService/BattleReady", "BattleService", server.BattleReadyHandlerWrap)
	s.QueueSubscribe("/"+serverId+"/natsrpc.BattleService/EndBattle", "BattleService", server.EndBattleHandlerWrap)
	s.QueueSubscribe("/"+serverId+"/natsrpc.BattleService/LeaveBattle", "BattleService", server.LeaveBattleHandlerWrap)
	return server
}

func (s *BattleServiceServerNatsRpcServer) CreateBattleHandlerWrap(msg *nats_go.Msg) {
	defer func() {
		if e := recover(); e != nil {
			trace := string(debug.Stack())
			debug.PrintStack()
			log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
		}
	}()

	req := &CreateBattleReq{}
	ret := &nats_go.Msg{Header: make(nats_go.Header)}
	defer msg.RespondMsg(ret)

	err := proto.Unmarshal(msg.Data, req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("unmarshal request error:%s", err.Error()))
		return
	}
	resp, err := s.CreateBattle(context.Background(), req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("response error:%s", err.Error()))
		return
	}
	ds, err := proto.Marshal(resp)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("marshal error:%s", err.Error()))
		return
	}
	ret.Data = ds
}

func (s *BattleServiceServerNatsRpcServer) EnterBattleHandlerWrap(msg *nats_go.Msg) {
	defer func() {
		if e := recover(); e != nil {
			trace := string(debug.Stack())
			debug.PrintStack()
			log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
		}
	}()

	req := &EnterBattleReq{}
	ret := &nats_go.Msg{Header: make(nats_go.Header)}
	defer msg.RespondMsg(ret)

	err := proto.Unmarshal(msg.Data, req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("unmarshal request error:%s", err.Error()))
		return
	}
	resp, err := s.EnterBattle(context.Background(), req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("response error:%s", err.Error()))
		return
	}
	ds, err := proto.Marshal(resp)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("marshal error:%s", err.Error()))
		return
	}
	ret.Data = ds
}

func (s *BattleServiceServerNatsRpcServer) SelectBufferHandlerWrap(msg *nats_go.Msg) {
	defer func() {
		if e := recover(); e != nil {
			trace := string(debug.Stack())
			debug.PrintStack()
			log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
		}
	}()

	req := &SelectBufferReq{}
	ret := &nats_go.Msg{Header: make(nats_go.Header)}
	defer msg.RespondMsg(ret)

	err := proto.Unmarshal(msg.Data, req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("unmarshal request error:%s", err.Error()))
		return
	}
	resp, err := s.SelectBuffer(context.Background(), req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("response error:%s", err.Error()))
		return
	}
	ds, err := proto.Marshal(resp)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("marshal error:%s", err.Error()))
		return
	}
	ret.Data = ds
}

func (s *BattleServiceServerNatsRpcServer) MergeHeroHandlerWrap(msg *nats_go.Msg) {
	defer func() {
		if e := recover(); e != nil {
			trace := string(debug.Stack())
			debug.PrintStack()
			log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
		}
	}()

	req := &MergeHeroReq{}
	ret := &nats_go.Msg{Header: make(nats_go.Header)}
	defer msg.RespondMsg(ret)

	err := proto.Unmarshal(msg.Data, req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("unmarshal request error:%s", err.Error()))
		return
	}
	resp, err := s.MergeHero(context.Background(), req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("response error:%s", err.Error()))
		return
	}
	ds, err := proto.Marshal(resp)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("marshal error:%s", err.Error()))
		return
	}
	ret.Data = ds
}

func (s *BattleServiceServerNatsRpcServer) BattleReadyHandlerWrap(msg *nats_go.Msg) {
	defer func() {
		if e := recover(); e != nil {
			trace := string(debug.Stack())
			debug.PrintStack()
			log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
		}
	}()

	req := &ReadyBattleReq{}
	ret := &nats_go.Msg{Header: make(nats_go.Header)}
	defer msg.RespondMsg(ret)

	err := proto.Unmarshal(msg.Data, req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("unmarshal request error:%s", err.Error()))
		return
	}
	resp, err := s.BattleReady(context.Background(), req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("response error:%s", err.Error()))
		return
	}
	ds, err := proto.Marshal(resp)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("marshal error:%s", err.Error()))
		return
	}
	ret.Data = ds
}

func (s *BattleServiceServerNatsRpcServer) EndBattleHandlerWrap(msg *nats_go.Msg) {
	defer func() {
		if e := recover(); e != nil {
			trace := string(debug.Stack())
			debug.PrintStack()
			log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
		}
	}()

	req := &EndBattleReq{}
	ret := &nats_go.Msg{Header: make(nats_go.Header)}
	defer msg.RespondMsg(ret)

	err := proto.Unmarshal(msg.Data, req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("unmarshal request error:%s", err.Error()))
		return
	}
	resp, err := s.EndBattle(context.Background(), req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("response error:%s", err.Error()))
		return
	}
	ds, err := proto.Marshal(resp)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("marshal error:%s", err.Error()))
		return
	}
	ret.Data = ds
}

func (s *BattleServiceServerNatsRpcServer) LeaveBattleHandlerWrap(msg *nats_go.Msg) {
	defer func() {
		if e := recover(); e != nil {
			trace := string(debug.Stack())
			debug.PrintStack()
			log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
		}
	}()

	req := &LeaveBattleReq{}
	ret := &nats_go.Msg{Header: make(nats_go.Header)}
	defer msg.RespondMsg(ret)

	err := proto.Unmarshal(msg.Data, req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("unmarshal request error:%s", err.Error()))
		return
	}
	resp, err := s.LeaveBattle(context.Background(), req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("response error:%s", err.Error()))
		return
	}
	ds, err := proto.Marshal(resp)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("marshal error:%s", err.Error()))
		return
	}
	ret.Data = ds
}
