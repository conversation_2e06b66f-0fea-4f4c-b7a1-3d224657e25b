// Code generated by protoc-gen-rpc-wrap. DO NOT EDIT.
// versions:
// - protoc-gen-rpc-wrap v1.0.0
// - protoc             v3.16.0
// source: GameService.proto

package natsrpc

import (
	context "context"
)

// GameServiceClient is the client API for GameService service.
type GameServiceClient interface {
	Auth(ctx context.Context, req *AuthReq, serverId string) (*AuthResp, error)
	AsyncAuth(ctx context.Context, req *AuthReq, serverId string, cb func(*AuthResp, error))
	MatchResult(ctx context.Context, req *MatchResultRequest, serverId string) error
	RoundStart(ctx context.Context, req *RoundStartReq, serverId string) error
	RoundBattleStart(ctx context.Context, req *RoundBattleStartReq, serverId string) error
	RoundBattleEnd(ctx context.Context, req *RoundBattleEndReq, serverId string) error
	BattleEnd(ctx context.Context, req *BattleEndReq, serverId string) error
	OnBattleStateChanged(ctx context.Context, req *BattleStateChangeReq, serverId string) error
}

// GameServiceServer is the server API for GameService service.
type GameServiceServer interface {
	Auth(context.Context, *AuthReq) (*AuthResp, error)
	MatchResult(context.Context, *MatchResultRequest) (*MatchResultResponse, error)
	RoundStart(context.Context, *RoundStartReq) (*RoundStartResp, error)
	RoundBattleStart(context.Context, *RoundBattleStartReq) (*RoundBattleStartResp, error)
	RoundBattleEnd(context.Context, *RoundBattleEndReq) (*RoundBattleEndResp, error)
	BattleEnd(context.Context, *BattleEndReq) (*BattleEndResp, error)
	OnBattleStateChanged(context.Context, *BattleStateChangeReq) (*BattleStateChangeResp, error)
}
