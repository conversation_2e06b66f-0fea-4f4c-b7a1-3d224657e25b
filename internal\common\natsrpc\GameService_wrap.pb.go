// Code generated by protoc-gen-rpc-wrap. DO NOT EDIT.
// versions:
// - protoc-gen-rpc-wrap v1.0.0
// - protoc             v3.16.0
// source: GameService.proto

package natsrpc

type gameserviceClientWrap struct {
	GameServiceClient
}

func NewGameserviceClientWrap(gameserviceclient GameServiceClient) *gameserviceClientWrap {
	return &gameserviceClientWrap{
		GameServiceClient: gameserviceclient,
	}
}
