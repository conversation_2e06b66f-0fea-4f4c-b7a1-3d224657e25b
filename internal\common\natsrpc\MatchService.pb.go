// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.16.0
// source: MatchService.proto

package natsrpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	public "liteframe/internal/common/protos/public"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Player *public.PBBattlePlayerInfo `protobuf:"bytes,1,opt,name=player,proto3" json:"player,omitempty"` // 玩家信息
	Team   *public.PBBattleTeamInfo   `protobuf:"bytes,2,opt,name=team,proto3" json:"team,omitempty"`
}

func (x *MatchRequest) Reset() {
	*x = MatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MatchService_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchRequest) ProtoMessage() {}

func (x *MatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MatchService_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchRequest.ProtoReflect.Descriptor instead.
func (*MatchRequest) Descriptor() ([]byte, []int) {
	return file_MatchService_proto_rawDescGZIP(), []int{0}
}

func (x *MatchRequest) GetPlayer() *public.PBBattlePlayerInfo {
	if x != nil {
		return x.Player
	}
	return nil
}

func (x *MatchRequest) GetTeam() *public.PBBattleTeamInfo {
	if x != nil {
		return x.Team
	}
	return nil
}

type MatchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 1成功，other 失败
}

func (x *MatchResponse) Reset() {
	*x = MatchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MatchService_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchResponse) ProtoMessage() {}

func (x *MatchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MatchService_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchResponse.ProtoReflect.Descriptor instead.
func (*MatchResponse) Descriptor() ([]byte, []int) {
	return file_MatchService_proto_rawDescGZIP(), []int{1}
}

func (x *MatchResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type CancelMatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid     uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`                        // 玩家id
	StageId int32  `protobuf:"varint,3,opt,name=stage_id,json=stageId,proto3" json:"stage_id,omitempty"` //补充参数
}

func (x *CancelMatchRequest) Reset() {
	*x = CancelMatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MatchService_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelMatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelMatchRequest) ProtoMessage() {}

func (x *CancelMatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MatchService_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelMatchRequest.ProtoReflect.Descriptor instead.
func (*CancelMatchRequest) Descriptor() ([]byte, []int) {
	return file_MatchService_proto_rawDescGZIP(), []int{2}
}

func (x *CancelMatchRequest) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *CancelMatchRequest) GetStageId() int32 {
	if x != nil {
		return x.StageId
	}
	return 0
}

type CancelMatchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 1成功，other 失败
}

func (x *CancelMatchResponse) Reset() {
	*x = CancelMatchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MatchService_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelMatchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelMatchResponse) ProtoMessage() {}

func (x *CancelMatchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MatchService_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelMatchResponse.ProtoReflect.Descriptor instead.
func (*CancelMatchResponse) Descriptor() ([]byte, []int) {
	return file_MatchService_proto_rawDescGZIP(), []int{3}
}

func (x *CancelMatchResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_MatchService_proto protoreflect.FileDescriptor

var file_MatchService_proto_rawDesc = []byte{
	0x0a, 0x12, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x1a, 0x13, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x10, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x62, 0x0a, 0x0c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x06, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x50, 0x42, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x12, 0x25, 0x0a, 0x04, 0x74, 0x65, 0x61, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x50, 0x42, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x74, 0x65, 0x61, 0x6d, 0x22, 0x23, 0x0a, 0x0d, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x41, 0x0a,
	0x12, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64,
	0x22, 0x29, 0x0a, 0x13, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x32, 0x94, 0x01, 0x0a, 0x0c,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x38, 0x0a, 0x05,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x15, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x6e,
	0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4a, 0x0a, 0x0b, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x1b, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e,
	0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x42, 0x23, 0x5a, 0x21, 0x6c, 0x69, 0x74, 0x65, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x2f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MatchService_proto_rawDescOnce sync.Once
	file_MatchService_proto_rawDescData = file_MatchService_proto_rawDesc
)

func file_MatchService_proto_rawDescGZIP() []byte {
	file_MatchService_proto_rawDescOnce.Do(func() {
		file_MatchService_proto_rawDescData = protoimpl.X.CompressGZIP(file_MatchService_proto_rawDescData)
	})
	return file_MatchService_proto_rawDescData
}

var file_MatchService_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_MatchService_proto_goTypes = []interface{}{
	(*MatchRequest)(nil),              // 0: natsrpc.MatchRequest
	(*MatchResponse)(nil),             // 1: natsrpc.MatchResponse
	(*CancelMatchRequest)(nil),        // 2: natsrpc.CancelMatchRequest
	(*CancelMatchResponse)(nil),       // 3: natsrpc.CancelMatchResponse
	(*public.PBBattlePlayerInfo)(nil), // 4: PBBattlePlayerInfo
	(*public.PBBattleTeamInfo)(nil),   // 5: PBBattleTeamInfo
}
var file_MatchService_proto_depIdxs = []int32{
	4, // 0: natsrpc.MatchRequest.player:type_name -> PBBattlePlayerInfo
	5, // 1: natsrpc.MatchRequest.team:type_name -> PBBattleTeamInfo
	0, // 2: natsrpc.MatchService.Match:input_type -> natsrpc.MatchRequest
	2, // 3: natsrpc.MatchService.CancelMatch:input_type -> natsrpc.CancelMatchRequest
	1, // 4: natsrpc.MatchService.Match:output_type -> natsrpc.MatchResponse
	3, // 5: natsrpc.MatchService.CancelMatch:output_type -> natsrpc.CancelMatchResponse
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_MatchService_proto_init() }
func file_MatchService_proto_init() {
	if File_MatchService_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_MatchService_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MatchService_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MatchService_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelMatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MatchService_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelMatchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MatchService_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_MatchService_proto_goTypes,
		DependencyIndexes: file_MatchService_proto_depIdxs,
		MessageInfos:      file_MatchService_proto_msgTypes,
	}.Build()
	File_MatchService_proto = out.File
	file_MatchService_proto_rawDesc = nil
	file_MatchService_proto_goTypes = nil
	file_MatchService_proto_depIdxs = nil
}
