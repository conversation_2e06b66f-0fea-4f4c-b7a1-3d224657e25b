// Code generated by protoc-gen-rpc-wrap. DO NOT EDIT.
// versions:
// - protoc-gen-rpc-wrap v1.0.0
// - protoc             v3.16.0
// source: MatchService.proto

package natsrpc

import (
	context "context"
	fmt "fmt"
	nats_go "github.com/nats-io/nats.go"
	proto "google.golang.org/protobuf/proto"
	log "liteframe/pkg/log"
	znats "liteframe/pkg/znats"
	debug "runtime/debug"
)

var _ = new(context.Context)
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = znats.Version
var _ = nats_go.Version
var _ = log.Version
var _ = debug.Stack

// matchServiceClient is the client API for MatchService service.
type matchServiceClient struct {
	client *znats.Client
}

func NewNatsRpcMatchServiceClient(client *znats.Client) *matchServiceClient {
	return &matchServiceClient{client: client}
}

func (c *matchServiceClient) Match(ctx context.Context, req *MatchRequest) (*MatchResponse, error) {
	resp := &MatchResponse{}
	err := c.client.Request(ctx, "/natsrpc.MatchService/Match", req, resp)
	return resp, err
}
func (c *matchServiceClient) AsyncMatch(ctx context.Context, req *MatchRequest, cb func(*MatchResponse, error)) {
	go func() {
		defer func() {
			if e := recover(); e != nil {
				trace := string(debug.Stack())
				debug.PrintStack()
				log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
			}
		}()
		resp := &MatchResponse{}
		err := c.client.Request(ctx, "/natsrpc.MatchService/Match", req, resp)
		cb(resp, err)
	}()
}

func (c *matchServiceClient) CancelMatch(ctx context.Context, req *CancelMatchRequest) (*CancelMatchResponse, error) {
	resp := &CancelMatchResponse{}
	err := c.client.Request(ctx, "/natsrpc.MatchService/CancelMatch", req, resp)
	return resp, err
}
func (c *matchServiceClient) AsyncCancelMatch(ctx context.Context, req *CancelMatchRequest, cb func(*CancelMatchResponse, error)) {
	go func() {
		defer func() {
			if e := recover(); e != nil {
				trace := string(debug.Stack())
				debug.PrintStack()
				log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
			}
		}()
		resp := &CancelMatchResponse{}
		err := c.client.Request(ctx, "/natsrpc.MatchService/CancelMatch", req, resp)
		cb(resp, err)
	}()
}

type MatchServiceServerNatsRpcServer struct {
	server *znats.Server
	MatchServiceServer
	serverId string
}

func NewMatchServiceServerNatsRpcServer(s *znats.Server, impl MatchServiceServer, serverId string) *MatchServiceServerNatsRpcServer {
	server := &MatchServiceServerNatsRpcServer{
		server:             s,
		MatchServiceServer: impl,
		serverId:           serverId,
	}
	s.QueueSubscribe("/natsrpc.MatchService/Match", "MatchService", server.MatchHandlerWrap)
	s.QueueSubscribe("/natsrpc.MatchService/CancelMatch", "MatchService", server.CancelMatchHandlerWrap)
	return server
}

func (s *MatchServiceServerNatsRpcServer) MatchHandlerWrap(msg *nats_go.Msg) {
	defer func() {
		if e := recover(); e != nil {
			trace := string(debug.Stack())
			debug.PrintStack()
			log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
		}
	}()

	req := &MatchRequest{}
	ret := &nats_go.Msg{Header: make(nats_go.Header)}
	defer msg.RespondMsg(ret)

	err := proto.Unmarshal(msg.Data, req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("unmarshal request error:%s", err.Error()))
		return
	}
	resp, err := s.Match(context.Background(), req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("response error:%s", err.Error()))
		return
	}
	ds, err := proto.Marshal(resp)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("marshal error:%s", err.Error()))
		return
	}
	ret.Data = ds
}

func (s *MatchServiceServerNatsRpcServer) CancelMatchHandlerWrap(msg *nats_go.Msg) {
	defer func() {
		if e := recover(); e != nil {
			trace := string(debug.Stack())
			debug.PrintStack()
			log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
		}
	}()

	req := &CancelMatchRequest{}
	ret := &nats_go.Msg{Header: make(nats_go.Header)}
	defer msg.RespondMsg(ret)

	err := proto.Unmarshal(msg.Data, req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("unmarshal request error:%s", err.Error()))
		return
	}
	resp, err := s.CancelMatch(context.Background(), req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("response error:%s", err.Error()))
		return
	}
	ds, err := proto.Marshal(resp)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("marshal error:%s", err.Error()))
		return
	}
	ret.Data = ds
}
