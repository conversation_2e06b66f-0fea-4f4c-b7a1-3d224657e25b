// 协议 ID 类型为 short，-32767 到 32767
//StartMessageID = 1000; // 必须以;分号结束
//MaxMessageID = 1999; // 必须以;分号结束

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.16.0
// source: CLProtocol.proto

package cs

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	public "liteframe/internal/common/protos/public"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//客户端登录 Logic 服请求
type CL_LOGIN_REQ struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlatformID          int64                `protobuf:"varint,1,opt,name=platformID,proto3" json:"platformID,omitempty"`
	Token               string               `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	NickName            string               `protobuf:"bytes,3,opt,name=nickName,proto3" json:"nickName,omitempty"`
	HeadUrl             string               `protobuf:"bytes,4,opt,name=headUrl,proto3" json:"headUrl,omitempty"`
	Version             string               `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`
	DriverId            string               `protobuf:"bytes,6,opt,name=driverId,proto3" json:"driverId,omitempty"`              //设备 id
	LoginState          int32                `protobuf:"varint,7,opt,name=loginState,proto3" json:"loginState,omitempty"`         //登陆的状态  0  登陆  1 重连
	Playerage           int32                `protobuf:"varint,8,opt,name=playerage,proto3" json:"playerage,omitempty"`           //年龄
	Playerbirthday      int32                `protobuf:"varint,9,opt,name=playerbirthday,proto3" json:"playerbirthday,omitempty"` //生日（19901010）
	DeviceInfo          *public.PBDeviceInfo `protobuf:"bytes,10,opt,name=deviceInfo,proto3" json:"deviceInfo,omitempty"`         //设备信息
	SdkToken            string               `protobuf:"bytes,11,opt,name=sdkToken,proto3" json:"sdkToken,omitempty"`             //第三方 Token
	MidasInfo           *public.PB_MidasInfo `protobuf:"bytes,12,opt,name=midasInfo,proto3" json:"midasInfo,omitempty"`
	GameCenterLoginType public.LoginByType   `protobuf:"varint,13,opt,name=gameCenterLoginType,proto3,enum=LoginByType" json:"gameCenterLoginType,omitempty"` //游戏中心登录类型
	ChannelOpenid       string               `protobuf:"bytes,14,opt,name=channelOpenid,proto3" json:"channelOpenid,omitempty"`                               //渠道的 openid
	ASAIadData          *public.PBASAIadData `protobuf:"bytes,15,opt,name=ASAIadData,proto3" json:"ASAIadData,omitempty"`                                     //IOS 广告信息
	SdkVersion          string               `protobuf:"bytes,16,opt,name=sdkVersion,proto3" json:"sdkVersion,omitempty"`                                     //海外版本 SDK 版本号
	SdkUserId           string               `protobuf:"bytes,17,opt,name=sdkUserId,proto3" json:"sdkUserId,omitempty"`                                       //sdk UserId
	SdkGameChannel      string               `protobuf:"bytes,18,opt,name=sdkGameChannel,proto3" json:"sdkGameChannel,omitempty"`                             //Sdk GameChannel
}

func (x *CL_LOGIN_REQ) Reset() {
	*x = CL_LOGIN_REQ{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CL_LOGIN_REQ) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CL_LOGIN_REQ) ProtoMessage() {}

func (x *CL_LOGIN_REQ) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CL_LOGIN_REQ.ProtoReflect.Descriptor instead.
func (*CL_LOGIN_REQ) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{0}
}

func (x *CL_LOGIN_REQ) GetPlatformID() int64 {
	if x != nil {
		return x.PlatformID
	}
	return 0
}

func (x *CL_LOGIN_REQ) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *CL_LOGIN_REQ) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *CL_LOGIN_REQ) GetHeadUrl() string {
	if x != nil {
		return x.HeadUrl
	}
	return ""
}

func (x *CL_LOGIN_REQ) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CL_LOGIN_REQ) GetDriverId() string {
	if x != nil {
		return x.DriverId
	}
	return ""
}

func (x *CL_LOGIN_REQ) GetLoginState() int32 {
	if x != nil {
		return x.LoginState
	}
	return 0
}

func (x *CL_LOGIN_REQ) GetPlayerage() int32 {
	if x != nil {
		return x.Playerage
	}
	return 0
}

func (x *CL_LOGIN_REQ) GetPlayerbirthday() int32 {
	if x != nil {
		return x.Playerbirthday
	}
	return 0
}

func (x *CL_LOGIN_REQ) GetDeviceInfo() *public.PBDeviceInfo {
	if x != nil {
		return x.DeviceInfo
	}
	return nil
}

func (x *CL_LOGIN_REQ) GetSdkToken() string {
	if x != nil {
		return x.SdkToken
	}
	return ""
}

func (x *CL_LOGIN_REQ) GetMidasInfo() *public.PB_MidasInfo {
	if x != nil {
		return x.MidasInfo
	}
	return nil
}

func (x *CL_LOGIN_REQ) GetGameCenterLoginType() public.LoginByType {
	if x != nil {
		return x.GameCenterLoginType
	}
	return public.LoginByType_LoginByType_Other
}

func (x *CL_LOGIN_REQ) GetChannelOpenid() string {
	if x != nil {
		return x.ChannelOpenid
	}
	return ""
}

func (x *CL_LOGIN_REQ) GetASAIadData() *public.PBASAIadData {
	if x != nil {
		return x.ASAIadData
	}
	return nil
}

func (x *CL_LOGIN_REQ) GetSdkVersion() string {
	if x != nil {
		return x.SdkVersion
	}
	return ""
}

func (x *CL_LOGIN_REQ) GetSdkUserId() string {
	if x != nil {
		return x.SdkUserId
	}
	return ""
}

func (x *CL_LOGIN_REQ) GetSdkGameChannel() string {
	if x != nil {
		return x.SdkGameChannel
	}
	return ""
}

//玩家数据请求
type CL_PlayerData_REQ struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuideOpenState bool `protobuf:"varint,1,opt,name=guideOpenState,proto3" json:"guideOpenState,omitempty"` //新手的状态是否开启
}

func (x *CL_PlayerData_REQ) Reset() {
	*x = CL_PlayerData_REQ{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CL_PlayerData_REQ) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CL_PlayerData_REQ) ProtoMessage() {}

func (x *CL_PlayerData_REQ) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CL_PlayerData_REQ.ProtoReflect.Descriptor instead.
func (*CL_PlayerData_REQ) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{1}
}

func (x *CL_PlayerData_REQ) GetGuideOpenState() bool {
	if x != nil {
		return x.GuideOpenState
	}
	return false
}

//客户端到服务器的 heartbeat    消息 的 id 必须是固定的 1002
type CLHeartBeat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeSpentLastRequest int32 `protobuf:"varint,1,opt,name=timeSpentLastRequest,proto3" json:"timeSpentLastRequest,omitempty"` //上次返回 请求和返回的时差 来判断网络耗时
}

func (x *CLHeartBeat) Reset() {
	*x = CLHeartBeat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLHeartBeat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLHeartBeat) ProtoMessage() {}

func (x *CLHeartBeat) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLHeartBeat.ProtoReflect.Descriptor instead.
func (*CLHeartBeat) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{2}
}

func (x *CLHeartBeat) GetTimeSpentLastRequest() int32 {
	if x != nil {
		return x.TimeSpentLastRequest
	}
	return 0
}

//请求服务器的时间
type CLUpdateServerTime struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLUpdateServerTime) Reset() {
	*x = CLUpdateServerTime{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLUpdateServerTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLUpdateServerTime) ProtoMessage() {}

func (x *CLUpdateServerTime) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLUpdateServerTime.ProtoReflect.Descriptor instead.
func (*CLUpdateServerTime) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{3}
}

//gm 指令
type CLGmReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *CLGmReq) Reset() {
	*x = CLGmReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGmReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGmReq) ProtoMessage() {}

func (x *CLGmReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGmReq.ProtoReflect.Descriptor instead.
func (*CLGmReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{4}
}

func (x *CLGmReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

//玩家注销登出
type CL_PlayerData_LoginOut struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CL_PlayerData_LoginOut) Reset() {
	*x = CL_PlayerData_LoginOut{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CL_PlayerData_LoginOut) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CL_PlayerData_LoginOut) ProtoMessage() {}

func (x *CL_PlayerData_LoginOut) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CL_PlayerData_LoginOut.ProtoReflect.Descriptor instead.
func (*CL_PlayerData_LoginOut) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{5}
}

//物品使用
type CLUseItemReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UseParam *public.UseItemParam `protobuf:"bytes,1,opt,name=useParam,proto3" json:"useParam,omitempty"` //使用参数
}

func (x *CLUseItemReq) Reset() {
	*x = CLUseItemReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLUseItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLUseItemReq) ProtoMessage() {}

func (x *CLUseItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLUseItemReq.ProtoReflect.Descriptor instead.
func (*CLUseItemReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{6}
}

func (x *CLUseItemReq) GetUseParam() *public.UseItemParam {
	if x != nil {
		return x.UseParam
	}
	return nil
}

//同步功能解锁
type CLFuntionUnLockReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FuntionID      int32 `protobuf:"varint,1,opt,name=funtionID,proto3" json:"funtionID,omitempty"`
	SinglePartcial int32 `protobuf:"varint,2,opt,name=singlePartcial,proto3" json:"singlePartcial,omitempty"`
	Partcial       int32 `protobuf:"varint,3,opt,name=Partcial,proto3" json:"Partcial,omitempty"`
}

func (x *CLFuntionUnLockReq) Reset() {
	*x = CLFuntionUnLockReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLFuntionUnLockReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLFuntionUnLockReq) ProtoMessage() {}

func (x *CLFuntionUnLockReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLFuntionUnLockReq.ProtoReflect.Descriptor instead.
func (*CLFuntionUnLockReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{7}
}

func (x *CLFuntionUnLockReq) GetFuntionID() int32 {
	if x != nil {
		return x.FuntionID
	}
	return 0
}

func (x *CLFuntionUnLockReq) GetSinglePartcial() int32 {
	if x != nil {
		return x.SinglePartcial
	}
	return 0
}

func (x *CLFuntionUnLockReq) GetPartcial() int32 {
	if x != nil {
		return x.Partcial
	}
	return 0
}

//====================邮件相关开始=======================
//请求邮件信息
type CLMailAllListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLMailAllListReq) Reset() {
	*x = CLMailAllListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLMailAllListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLMailAllListReq) ProtoMessage() {}

func (x *CLMailAllListReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLMailAllListReq.ProtoReflect.Descriptor instead.
func (*CLMailAllListReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{8}
}

//玩家读取一封邮件的
type CLReadMailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MailId int64 `protobuf:"varint,1,opt,name=mailId,proto3" json:"mailId,omitempty"` //邮件 ID
}

func (x *CLReadMailReq) Reset() {
	*x = CLReadMailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLReadMailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLReadMailReq) ProtoMessage() {}

func (x *CLReadMailReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLReadMailReq.ProtoReflect.Descriptor instead.
func (*CLReadMailReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{9}
}

func (x *CLReadMailReq) GetMailId() int64 {
	if x != nil {
		return x.MailId
	}
	return 0
}

//玩家领取东西
type CLReceiveMailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MailId int64 `protobuf:"varint,1,opt,name=mailId,proto3" json:"mailId,omitempty"` //想领取的邮件 ID
}

func (x *CLReceiveMailReq) Reset() {
	*x = CLReceiveMailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLReceiveMailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLReceiveMailReq) ProtoMessage() {}

func (x *CLReceiveMailReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLReceiveMailReq.ProtoReflect.Descriptor instead.
func (*CLReceiveMailReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{10}
}

func (x *CLReceiveMailReq) GetMailId() int64 {
	if x != nil {
		return x.MailId
	}
	return 0
}

//玩家领取所有邮件
type CLReceiveAllMailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLReceiveAllMailReq) Reset() {
	*x = CLReceiveAllMailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLReceiveAllMailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLReceiveAllMailReq) ProtoMessage() {}

func (x *CLReceiveAllMailReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLReceiveAllMailReq.ProtoReflect.Descriptor instead.
func (*CLReceiveAllMailReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{11}
}

//玩家删除邮件
type CLDelMailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MailId int64 `protobuf:"varint,1,opt,name=mailId,proto3" json:"mailId,omitempty"` //想删除邮件 id
}

func (x *CLDelMailReq) Reset() {
	*x = CLDelMailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLDelMailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLDelMailReq) ProtoMessage() {}

func (x *CLDelMailReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLDelMailReq.ProtoReflect.Descriptor instead.
func (*CLDelMailReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{12}
}

func (x *CLDelMailReq) GetMailId() int64 {
	if x != nil {
		return x.MailId
	}
	return 0
}

//一键删除邮件
type CLDelAllReadMailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLDelAllReadMailReq) Reset() {
	*x = CLDelAllReadMailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLDelAllReadMailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLDelAllReadMailReq) ProtoMessage() {}

func (x *CLDelAllReadMailReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLDelAllReadMailReq.ProtoReflect.Descriptor instead.
func (*CLDelAllReadMailReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{13}
}

//问卷奖励邮件
type CLMailQuestionAwardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLMailQuestionAwardReq) Reset() {
	*x = CLMailQuestionAwardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLMailQuestionAwardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLMailQuestionAwardReq) ProtoMessage() {}

func (x *CLMailQuestionAwardReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLMailQuestionAwardReq.ProtoReflect.Descriptor instead.
func (*CLMailQuestionAwardReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{14}
}

//=====================账号信息开始=============================
//玩家修改名字
type CLChangeNameReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`  //名字
	Type int32  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"` //起名的方式  0 起名界面 1 玩家改名
}

func (x *CLChangeNameReq) Reset() {
	*x = CLChangeNameReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLChangeNameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLChangeNameReq) ProtoMessage() {}

func (x *CLChangeNameReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLChangeNameReq.ProtoReflect.Descriptor instead.
func (*CLChangeNameReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{15}
}

func (x *CLChangeNameReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CLChangeNameReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

//修改个性签名
type CLChangeSignReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SignContent string `protobuf:"bytes,1,opt,name=signContent,proto3" json:"signContent,omitempty"` //签名内容
}

func (x *CLChangeSignReq) Reset() {
	*x = CLChangeSignReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLChangeSignReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLChangeSignReq) ProtoMessage() {}

func (x *CLChangeSignReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLChangeSignReq.ProtoReflect.Descriptor instead.
func (*CLChangeSignReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{16}
}

func (x *CLChangeSignReq) GetSignContent() string {
	if x != nil {
		return x.SignContent
	}
	return ""
}

//修改玩家性别
type CLChangeGenderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gender public.EGenderType `protobuf:"varint,1,opt,name=gender,proto3,enum=EGenderType" json:"gender,omitempty"` //枚举性别
}

func (x *CLChangeGenderReq) Reset() {
	*x = CLChangeGenderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLChangeGenderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLChangeGenderReq) ProtoMessage() {}

func (x *CLChangeGenderReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLChangeGenderReq.ProtoReflect.Descriptor instead.
func (*CLChangeGenderReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{17}
}

func (x *CLChangeGenderReq) GetGender() public.EGenderType {
	if x != nil {
		return x.Gender
	}
	return public.EGenderType_Default
}

//=====================账号信息结束=============================
//===================新版新手引导开始==========================
//新手的下一步操作信息
type CLNewGuideStepInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurNewGuideSetpInfo *public.PBCommonKeyValue `protobuf:"bytes,1,opt,name=curNewGuideSetpInfo,proto3" json:"curNewGuideSetpInfo,omitempty"`
}

func (x *CLNewGuideStepInfoReq) Reset() {
	*x = CLNewGuideStepInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLNewGuideStepInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLNewGuideStepInfoReq) ProtoMessage() {}

func (x *CLNewGuideStepInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLNewGuideStepInfoReq.ProtoReflect.Descriptor instead.
func (*CLNewGuideStepInfoReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{18}
}

func (x *CLNewGuideStepInfoReq) GetCurNewGuideSetpInfo() *public.PBCommonKeyValue {
	if x != nil {
		return x.CurNewGuideSetpInfo
	}
	return nil
}

//新手客户端触发的新手信息
type CLNewGuideClientStartReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartNewGuideSetpInfo *public.PBCommonKeyValue `protobuf:"bytes,1,opt,name=startNewGuideSetpInfo,proto3" json:"startNewGuideSetpInfo,omitempty"`
}

func (x *CLNewGuideClientStartReq) Reset() {
	*x = CLNewGuideClientStartReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLNewGuideClientStartReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLNewGuideClientStartReq) ProtoMessage() {}

func (x *CLNewGuideClientStartReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLNewGuideClientStartReq.ProtoReflect.Descriptor instead.
func (*CLNewGuideClientStartReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{19}
}

func (x *CLNewGuideClientStartReq) GetStartNewGuideSetpInfo() *public.PBCommonKeyValue {
	if x != nil {
		return x.StartNewGuideSetpInfo
	}
	return nil
}

//客户端触发新手引导组
type CLClientTriggerGuideReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId int32 `protobuf:"varint,1,opt,name=groupId,proto3" json:"groupId,omitempty"` //触发的组ID
}

func (x *CLClientTriggerGuideReq) Reset() {
	*x = CLClientTriggerGuideReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLClientTriggerGuideReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLClientTriggerGuideReq) ProtoMessage() {}

func (x *CLClientTriggerGuideReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLClientTriggerGuideReq.ProtoReflect.Descriptor instead.
func (*CLClientTriggerGuideReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{20}
}

func (x *CLClientTriggerGuideReq) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

//引导步骤完成
type CLGuideStepFinishReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Guide *public.PBIntPair `protobuf:"bytes,1,opt,name=guide,proto3" json:"guide,omitempty"` //完成的引导信息(key:组ID,val:子步骤ID)
}

func (x *CLGuideStepFinishReq) Reset() {
	*x = CLGuideStepFinishReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuideStepFinishReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuideStepFinishReq) ProtoMessage() {}

func (x *CLGuideStepFinishReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuideStepFinishReq.ProtoReflect.Descriptor instead.
func (*CLGuideStepFinishReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{21}
}

func (x *CLGuideStepFinishReq) GetGuide() *public.PBIntPair {
	if x != nil {
		return x.Guide
	}
	return nil
}

//强制引导组完成(非正常情况调用)
type CLGuideGroupFinishReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId int32 `protobuf:"varint,1,opt,name=groupId,proto3" json:"groupId,omitempty"` //引导组ID
}

func (x *CLGuideGroupFinishReq) Reset() {
	*x = CLGuideGroupFinishReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuideGroupFinishReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuideGroupFinishReq) ProtoMessage() {}

func (x *CLGuideGroupFinishReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuideGroupFinishReq.ProtoReflect.Descriptor instead.
func (*CLGuideGroupFinishReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{22}
}

func (x *CLGuideGroupFinishReq) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

//======================签到开始=========================================
//请求签到消息
type CLSignInInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SignInType public.SignInToType `protobuf:"varint,1,opt,name=SignInType,proto3,enum=SignInToType" json:"SignInType,omitempty"` //签到的类型
}

func (x *CLSignInInfoRequest) Reset() {
	*x = CLSignInInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLSignInInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLSignInInfoRequest) ProtoMessage() {}

func (x *CLSignInInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLSignInInfoRequest.ProtoReflect.Descriptor instead.
func (*CLSignInInfoRequest) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{23}
}

func (x *CLSignInInfoRequest) GetSignInType() public.SignInToType {
	if x != nil {
		return x.SignInType
	}
	return public.SignInToType_SignInToType_None
}

//领取签到奖励信息
type CLSignInRewardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SignInType public.SignInToType `protobuf:"varint,1,opt,name=SignInType,proto3,enum=SignInToType" json:"SignInType,omitempty"` //签到的类型
	DayNum     int32               `protobuf:"varint,2,opt,name=DayNum,proto3" json:"DayNum,omitempty"`                           //签到的天数
}

func (x *CLSignInRewardRequest) Reset() {
	*x = CLSignInRewardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLSignInRewardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLSignInRewardRequest) ProtoMessage() {}

func (x *CLSignInRewardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLSignInRewardRequest.ProtoReflect.Descriptor instead.
func (*CLSignInRewardRequest) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{24}
}

func (x *CLSignInRewardRequest) GetSignInType() public.SignInToType {
	if x != nil {
		return x.SignInType
	}
	return public.SignInToType_SignInToType_None
}

func (x *CLSignInRewardRequest) GetDayNum() int32 {
	if x != nil {
		return x.DayNum
	}
	return 0
}

//自动领取七日签到奖励信息
type CLSevenDaySignInRewardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLSevenDaySignInRewardRequest) Reset() {
	*x = CLSevenDaySignInRewardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLSevenDaySignInRewardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLSevenDaySignInRewardRequest) ProtoMessage() {}

func (x *CLSevenDaySignInRewardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLSevenDaySignInRewardRequest.ProtoReflect.Descriptor instead.
func (*CLSevenDaySignInRewardRequest) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{25}
}

//一键领取签到奖励信息
type CLSignMonthRewardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLSignMonthRewardRequest) Reset() {
	*x = CLSignMonthRewardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLSignMonthRewardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLSignMonthRewardRequest) ProtoMessage() {}

func (x *CLSignMonthRewardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLSignMonthRewardRequest.ProtoReflect.Descriptor instead.
func (*CLSignMonthRewardRequest) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{26}
}

//=====================玩家设置相关开始=============================
//向服务端同步设置数据
type CLSyncSettingReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data *public.PBSettingData `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"` //设置相关结构体
}

func (x *CLSyncSettingReq) Reset() {
	*x = CLSyncSettingReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLSyncSettingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLSyncSettingReq) ProtoMessage() {}

func (x *CLSyncSettingReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLSyncSettingReq.ProtoReflect.Descriptor instead.
func (*CLSyncSettingReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{27}
}

func (x *CLSyncSettingReq) GetData() *public.PBSettingData {
	if x != nil {
		return x.Data
	}
	return nil
}

//======================社交分裂开始=========================================
//同步  每次打开界面在同步一下
type CLAllFriendInvitationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLAllFriendInvitationReq) Reset() {
	*x = CLAllFriendInvitationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLAllFriendInvitationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLAllFriendInvitationReq) ProtoMessage() {}

func (x *CLAllFriendInvitationReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLAllFriendInvitationReq.ProtoReflect.Descriptor instead.
func (*CLAllFriendInvitationReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{28}
}

//领取拉新奖励
type CLFriendRasinRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TableID int32 `protobuf:"varint,1,opt,name=tableID,proto3" json:"tableID,omitempty"`
}

func (x *CLFriendRasinRewardReq) Reset() {
	*x = CLFriendRasinRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLFriendRasinRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLFriendRasinRewardReq) ProtoMessage() {}

func (x *CLFriendRasinRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLFriendRasinRewardReq.ProtoReflect.Descriptor instead.
func (*CLFriendRasinRewardReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{29}
}

func (x *CLFriendRasinRewardReq) GetTableID() int32 {
	if x != nil {
		return x.TableID
	}
	return 0
}

//打开对应玩家任务
type CLFriendGrowthQuestRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerUID int64 `protobuf:"varint,1,opt,name=playerUID,proto3" json:"playerUID,omitempty"`
}

func (x *CLFriendGrowthQuestRewardReq) Reset() {
	*x = CLFriendGrowthQuestRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLFriendGrowthQuestRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLFriendGrowthQuestRewardReq) ProtoMessage() {}

func (x *CLFriendGrowthQuestRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLFriendGrowthQuestRewardReq.ProtoReflect.Descriptor instead.
func (*CLFriendGrowthQuestRewardReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{30}
}

func (x *CLFriendGrowthQuestRewardReq) GetPlayerUID() int64 {
	if x != nil {
		return x.PlayerUID
	}
	return 0
}

//领取对应玩家任务奖励
type CLFriendGrowthQuestReceiveRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerUID int64 `protobuf:"varint,1,opt,name=playerUID,proto3" json:"playerUID,omitempty"`
	TableID   int32 `protobuf:"varint,2,opt,name=tableID,proto3" json:"tableID,omitempty"`
}

func (x *CLFriendGrowthQuestReceiveRewardReq) Reset() {
	*x = CLFriendGrowthQuestReceiveRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLFriendGrowthQuestReceiveRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLFriendGrowthQuestReceiveRewardReq) ProtoMessage() {}

func (x *CLFriendGrowthQuestReceiveRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLFriendGrowthQuestReceiveRewardReq.ProtoReflect.Descriptor instead.
func (*CLFriendGrowthQuestReceiveRewardReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{31}
}

func (x *CLFriendGrowthQuestReceiveRewardReq) GetPlayerUID() int64 {
	if x != nil {
		return x.PlayerUID
	}
	return 0
}

func (x *CLFriendGrowthQuestReceiveRewardReq) GetTableID() int32 {
	if x != nil {
		return x.TableID
	}
	return 0
}

//领取对应玩家所有任务奖励
type CLFriendGrowthQuestReceiveAllRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerUID int64 `protobuf:"varint,1,opt,name=playerUID,proto3" json:"playerUID,omitempty"`
}

func (x *CLFriendGrowthQuestReceiveAllRewardReq) Reset() {
	*x = CLFriendGrowthQuestReceiveAllRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLFriendGrowthQuestReceiveAllRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLFriendGrowthQuestReceiveAllRewardReq) ProtoMessage() {}

func (x *CLFriendGrowthQuestReceiveAllRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLFriendGrowthQuestReceiveAllRewardReq.ProtoReflect.Descriptor instead.
func (*CLFriendGrowthQuestReceiveAllRewardReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{32}
}

func (x *CLFriendGrowthQuestReceiveAllRewardReq) GetPlayerUID() int64 {
	if x != nil {
		return x.PlayerUID
	}
	return 0
}

//分享
type CLSharePosterReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurPlayerUID int64 `protobuf:"varint,1,opt,name=curPlayerUID,proto3" json:"curPlayerUID,omitempty"`
}

func (x *CLSharePosterReq) Reset() {
	*x = CLSharePosterReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLSharePosterReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLSharePosterReq) ProtoMessage() {}

func (x *CLSharePosterReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLSharePosterReq.ProtoReflect.Descriptor instead.
func (*CLSharePosterReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{33}
}

func (x *CLSharePosterReq) GetCurPlayerUID() int64 {
	if x != nil {
		return x.CurPlayerUID
	}
	return 0
}

//分享
type CLEnterInvitationCodeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvitationCode int64 `protobuf:"zigzag64,1,opt,name=invitationCode,proto3" json:"invitationCode,omitempty"` //邀请码
}

func (x *CLEnterInvitationCodeReq) Reset() {
	*x = CLEnterInvitationCodeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLEnterInvitationCodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLEnterInvitationCodeReq) ProtoMessage() {}

func (x *CLEnterInvitationCodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLEnterInvitationCodeReq.ProtoReflect.Descriptor instead.
func (*CLEnterInvitationCodeReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{34}
}

func (x *CLEnterInvitationCodeReq) GetInvitationCode() int64 {
	if x != nil {
		return x.InvitationCode
	}
	return 0
}

//完成阶段宝箱
type CLFinishCommonExpBoxData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BoxId    int32   `protobuf:"varint,1,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"`
	BoxIndex []int32 `protobuf:"varint,2,rep,packed,name=box_index,json=boxIndex,proto3" json:"box_index,omitempty"`
}

func (x *CLFinishCommonExpBoxData) Reset() {
	*x = CLFinishCommonExpBoxData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLFinishCommonExpBoxData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLFinishCommonExpBoxData) ProtoMessage() {}

func (x *CLFinishCommonExpBoxData) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLFinishCommonExpBoxData.ProtoReflect.Descriptor instead.
func (*CLFinishCommonExpBoxData) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{35}
}

func (x *CLFinishCommonExpBoxData) GetBoxId() int32 {
	if x != nil {
		return x.BoxId
	}
	return 0
}

func (x *CLFinishCommonExpBoxData) GetBoxIndex() []int32 {
	if x != nil {
		return x.BoxIndex
	}
	return nil
}

//======================通用阶段宝箱相关数据=============================
//======================问卷  Start=====================================
//打开问卷页面
type CLQuestReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLQuestReq) Reset() {
	*x = CLQuestReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLQuestReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLQuestReq) ProtoMessage() {}

func (x *CLQuestReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLQuestReq.ProtoReflect.Descriptor instead.
func (*CLQuestReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{36}
}

//问卷奖励邮件
type CLQuestAwardToMailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestId int32 `protobuf:"varint,1,opt,name=questId,proto3" json:"questId,omitempty"` //问卷 id
}

func (x *CLQuestAwardToMailReq) Reset() {
	*x = CLQuestAwardToMailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLQuestAwardToMailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLQuestAwardToMailReq) ProtoMessage() {}

func (x *CLQuestAwardToMailReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLQuestAwardToMailReq.ProtoReflect.Descriptor instead.
func (*CLQuestAwardToMailReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{37}
}

func (x *CLQuestAwardToMailReq) GetQuestId() int32 {
	if x != nil {
		return x.QuestId
	}
	return 0
}

//请求其他玩家信息
type CLPlayerOtherInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OtherPlayerId int64 `protobuf:"varint,1,opt,name=otherPlayerId,proto3" json:"otherPlayerId,omitempty"` //其他玩家游戏内 ID
}

func (x *CLPlayerOtherInfo) Reset() {
	*x = CLPlayerOtherInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLPlayerOtherInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLPlayerOtherInfo) ProtoMessage() {}

func (x *CLPlayerOtherInfo) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLPlayerOtherInfo.ProtoReflect.Descriptor instead.
func (*CLPlayerOtherInfo) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{38}
}

func (x *CLPlayerOtherInfo) GetOtherPlayerId() int64 {
	if x != nil {
		return x.OtherPlayerId
	}
	return 0
}

//=====================公会系统开始==================================
//创建公会
type CLGuildCreate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                 //名称
	Notice       string `protobuf:"bytes,2,opt,name=notice,proto3" json:"notice,omitempty"`             //宣言
	IconId       int32  `protobuf:"varint,3,opt,name=iconId,proto3" json:"iconId,omitempty"`            //图标
	FreeJoin     bool   `protobuf:"varint,4,opt,name=freeJoin,proto3" json:"freeJoin,omitempty"`        //true 自由加入 false 需要审批，
	ReqStage     int32  `protobuf:"varint,5,opt,name=reqStage,proto3" json:"reqStage,omitempty"`        //审批时需具备的关卡条件：0.无限制 1.困难 2.疯狂 3.地狱
	Announcement string `protobuf:"bytes,6,opt,name=announcement,proto3" json:"announcement,omitempty"` // 联盟公告
}

func (x *CLGuildCreate) Reset() {
	*x = CLGuildCreate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildCreate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildCreate) ProtoMessage() {}

func (x *CLGuildCreate) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildCreate.ProtoReflect.Descriptor instead.
func (*CLGuildCreate) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{39}
}

func (x *CLGuildCreate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CLGuildCreate) GetNotice() string {
	if x != nil {
		return x.Notice
	}
	return ""
}

func (x *CLGuildCreate) GetIconId() int32 {
	if x != nil {
		return x.IconId
	}
	return 0
}

func (x *CLGuildCreate) GetFreeJoin() bool {
	if x != nil {
		return x.FreeJoin
	}
	return false
}

func (x *CLGuildCreate) GetReqStage() int32 {
	if x != nil {
		return x.ReqStage
	}
	return 0
}

func (x *CLGuildCreate) GetAnnouncement() string {
	if x != nil {
		return x.Announcement
	}
	return ""
}

//主按钮请求
type CLGuildMain struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildMain) Reset() {
	*x = CLGuildMain{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildMain) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildMain) ProtoMessage() {}

func (x *CLGuildMain) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildMain.ProtoReflect.Descriptor instead.
func (*CLGuildMain) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{40}
}

//一键加入
type CLGuildFastJoin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildFastJoin) Reset() {
	*x = CLGuildFastJoin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildFastJoin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildFastJoin) ProtoMessage() {}

func (x *CLGuildFastJoin) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildFastJoin.ProtoReflect.Descriptor instead.
func (*CLGuildFastJoin) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{41}
}

//公会搜索
type CLGuildSearch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"` //公会名称或 ID
}

func (x *CLGuildSearch) Reset() {
	*x = CLGuildSearch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildSearch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildSearch) ProtoMessage() {}

func (x *CLGuildSearch) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildSearch.ProtoReflect.Descriptor instead.
func (*CLGuildSearch) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{42}
}

func (x *CLGuildSearch) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

//公会申请
type CLGuildApply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //公会 ID，提交用
}

func (x *CLGuildApply) Reset() {
	*x = CLGuildApply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildApply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildApply) ProtoMessage() {}

func (x *CLGuildApply) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildApply.ProtoReflect.Descriptor instead.
func (*CLGuildApply) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{43}
}

func (x *CLGuildApply) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

//公会大厅
type CLGuildHall struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildHall) Reset() {
	*x = CLGuildHall{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildHall) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildHall) ProtoMessage() {}

func (x *CLGuildHall) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildHall.ProtoReflect.Descriptor instead.
func (*CLGuildHall) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{44}
}

//公会科技
type CLGuildTech struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildTech) Reset() {
	*x = CLGuildTech{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildTech) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildTech) ProtoMessage() {}

func (x *CLGuildTech) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildTech.ProtoReflect.Descriptor instead.
func (*CLGuildTech) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{45}
}

//公会科技强化
type CLGuildTechLevelup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //科技 ID
}

func (x *CLGuildTechLevelup) Reset() {
	*x = CLGuildTechLevelup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildTechLevelup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildTechLevelup) ProtoMessage() {}

func (x *CLGuildTechLevelup) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildTechLevelup.ProtoReflect.Descriptor instead.
func (*CLGuildTechLevelup) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{46}
}

func (x *CLGuildTechLevelup) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

//公会科技重置
type CLGuildTechReset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildTechReset) Reset() {
	*x = CLGuildTechReset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildTechReset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildTechReset) ProtoMessage() {}

func (x *CLGuildTechReset) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildTechReset.ProtoReflect.Descriptor instead.
func (*CLGuildTechReset) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{47}
}

//公会商店
type CLGuildShop struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildShop) Reset() {
	*x = CLGuildShop{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildShop) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildShop) ProtoMessage() {}

func (x *CLGuildShop) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildShop.ProtoReflect.Descriptor instead.
func (*CLGuildShop) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{48}
}

//公会商店购买
type CLGuildShopBuy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`       //商品 ID
	Type  int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`   //购买类型 1.free 2.adfree 3.normal
	Count int32 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"` //购买次数
}

func (x *CLGuildShopBuy) Reset() {
	*x = CLGuildShopBuy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildShopBuy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildShopBuy) ProtoMessage() {}

func (x *CLGuildShopBuy) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildShopBuy.ProtoReflect.Descriptor instead.
func (*CLGuildShopBuy) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{49}
}

func (x *CLGuildShopBuy) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CLGuildShopBuy) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *CLGuildShopBuy) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

//修改公会信息
type CLGuildEdit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Opt          public.GuildOpt `protobuf:"varint,1,opt,name=opt,proto3,enum=GuildOpt" json:"opt,omitempty"`    //具体操作 0.修改旗帜 1.修改名字 2.修改公告 3.修改申请条件 下面的字段根据此操作提交对应值。
	Name         string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                 //名称
	Notice       string          `protobuf:"bytes,3,opt,name=notice,proto3" json:"notice,omitempty"`             //宣言
	IconId       int32           `protobuf:"varint,4,opt,name=iconId,proto3" json:"iconId,omitempty"`            //图标
	FreeJoin     bool            `protobuf:"varint,5,opt,name=freeJoin,proto3" json:"freeJoin,omitempty"`        //true 自由加入 false 需要审批，
	ReqStage     int32           `protobuf:"varint,6,opt,name=reqStage,proto3" json:"reqStage,omitempty"`        //审批时需具备的关卡条件：0.无限制 1.困难 2.疯狂 3.地狱   （先按等级开发）
	Announcement string          `protobuf:"bytes,7,opt,name=announcement,proto3" json:"announcement,omitempty"` // 联盟公告 (opt = GuildOpt_EditAnnouncement (新增) 时使用)
}

func (x *CLGuildEdit) Reset() {
	*x = CLGuildEdit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildEdit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildEdit) ProtoMessage() {}

func (x *CLGuildEdit) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildEdit.ProtoReflect.Descriptor instead.
func (*CLGuildEdit) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{50}
}

func (x *CLGuildEdit) GetOpt() public.GuildOpt {
	if x != nil {
		return x.Opt
	}
	return public.GuildOpt_GuildOpt_None
}

func (x *CLGuildEdit) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CLGuildEdit) GetNotice() string {
	if x != nil {
		return x.Notice
	}
	return ""
}

func (x *CLGuildEdit) GetIconId() int32 {
	if x != nil {
		return x.IconId
	}
	return 0
}

func (x *CLGuildEdit) GetFreeJoin() bool {
	if x != nil {
		return x.FreeJoin
	}
	return false
}

func (x *CLGuildEdit) GetReqStage() int32 {
	if x != nil {
		return x.ReqStage
	}
	return 0
}

func (x *CLGuildEdit) GetAnnouncement() string {
	if x != nil {
		return x.Announcement
	}
	return ""
}

//公会申请列表
type CLGuildApplyMgrList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildApplyMgrList) Reset() {
	*x = CLGuildApplyMgrList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildApplyMgrList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildApplyMgrList) ProtoMessage() {}

func (x *CLGuildApplyMgrList) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildApplyMgrList.ProtoReflect.Descriptor instead.
func (*CLGuildApplyMgrList) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{51}
}

//公会成员管理
type CLGuildMemberMgrList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildMemberMgrList) Reset() {
	*x = CLGuildMemberMgrList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildMemberMgrList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildMemberMgrList) ProtoMessage() {}

func (x *CLGuildMemberMgrList) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildMemberMgrList.ProtoReflect.Descriptor instead.
func (*CLGuildMemberMgrList) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{52}
}

//公会申请列表操作
type CLGuildApplyMgr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Opt        public.GuildOpt `protobuf:"varint,1,opt,name=opt,proto3,enum=GuildOpt" json:"opt,omitempty"` //具体操作 5.批准 6.拒绝
	PlatformID int64           `protobuf:"varint,2,opt,name=platformID,proto3" json:"platformID,omitempty"` //操作的玩家 id
}

func (x *CLGuildApplyMgr) Reset() {
	*x = CLGuildApplyMgr{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildApplyMgr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildApplyMgr) ProtoMessage() {}

func (x *CLGuildApplyMgr) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildApplyMgr.ProtoReflect.Descriptor instead.
func (*CLGuildApplyMgr) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{53}
}

func (x *CLGuildApplyMgr) GetOpt() public.GuildOpt {
	if x != nil {
		return x.Opt
	}
	return public.GuildOpt_GuildOpt_None
}

func (x *CLGuildApplyMgr) GetPlatformID() int64 {
	if x != nil {
		return x.PlatformID
	}
	return 0
}

//公会成员管理
type CLGuildMemberMgr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Opt        public.GuildOpt `protobuf:"varint,1,opt,name=opt,proto3,enum=GuildOpt" json:"opt,omitempty"` //具体操作 8.任命会长 9.任命副会长 10.任命为普通成员 11.踢人
	PlatformID int64           `protobuf:"varint,2,opt,name=platformID,proto3" json:"platformID,omitempty"` //操作的玩家 id
}

func (x *CLGuildMemberMgr) Reset() {
	*x = CLGuildMemberMgr{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildMemberMgr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildMemberMgr) ProtoMessage() {}

func (x *CLGuildMemberMgr) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildMemberMgr.ProtoReflect.Descriptor instead.
func (*CLGuildMemberMgr) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{54}
}

func (x *CLGuildMemberMgr) GetOpt() public.GuildOpt {
	if x != nil {
		return x.Opt
	}
	return public.GuildOpt_GuildOpt_None
}

func (x *CLGuildMemberMgr) GetPlatformID() int64 {
	if x != nil {
		return x.PlatformID
	}
	return 0
}

//退出公会
type CLGuildQuit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildQuit) Reset() {
	*x = CLGuildQuit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildQuit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildQuit) ProtoMessage() {}

func (x *CLGuildQuit) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildQuit.ProtoReflect.Descriptor instead.
func (*CLGuildQuit) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{55}
}

//解散公会
type CLGuildDismiss struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildDismiss) Reset() {
	*x = CLGuildDismiss{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildDismiss) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildDismiss) ProtoMessage() {}

func (x *CLGuildDismiss) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildDismiss.ProtoReflect.Descriptor instead.
func (*CLGuildDismiss) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{56}
}

//弹劾会长
type CLGuildImpeach struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildImpeach) Reset() {
	*x = CLGuildImpeach{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildImpeach) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildImpeach) ProtoMessage() {}

func (x *CLGuildImpeach) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildImpeach.ProtoReflect.Descriptor instead.
func (*CLGuildImpeach) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{57}
}

//发送世界邀请
type CLGuildSendWorldInvite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"` //邀请内容
}

func (x *CLGuildSendWorldInvite) Reset() {
	*x = CLGuildSendWorldInvite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildSendWorldInvite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildSendWorldInvite) ProtoMessage() {}

func (x *CLGuildSendWorldInvite) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildSendWorldInvite.ProtoReflect.Descriptor instead.
func (*CLGuildSendWorldInvite) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{58}
}

func (x *CLGuildSendWorldInvite) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

//发送私聊邀请
type CLGuildSendPlayerInvite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text       string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`              //邀请内容
	ToPlayerId int64  `protobuf:"varint,2,opt,name=toPlayerId,proto3" json:"toPlayerId,omitempty"` //私聊的对象 ID
}

func (x *CLGuildSendPlayerInvite) Reset() {
	*x = CLGuildSendPlayerInvite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildSendPlayerInvite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildSendPlayerInvite) ProtoMessage() {}

func (x *CLGuildSendPlayerInvite) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildSendPlayerInvite.ProtoReflect.Descriptor instead.
func (*CLGuildSendPlayerInvite) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{59}
}

func (x *CLGuildSendPlayerInvite) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *CLGuildSendPlayerInvite) GetToPlayerId() int64 {
	if x != nil {
		return x.ToPlayerId
	}
	return 0
}

//公会邀请加入
type CLGuildInviteJoin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //公会 ID，提交用
}

func (x *CLGuildInviteJoin) Reset() {
	*x = CLGuildInviteJoin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildInviteJoin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildInviteJoin) ProtoMessage() {}

func (x *CLGuildInviteJoin) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildInviteJoin.ProtoReflect.Descriptor instead.
func (*CLGuildInviteJoin) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{60}
}

func (x *CLGuildInviteJoin) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

//公会捐献
type CLGuildDonate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Opt int32 `protobuf:"varint,1,opt,name=opt,proto3" json:"opt,omitempty"` //捐献操作：1.免费 2.金币 3.钻石
}

func (x *CLGuildDonate) Reset() {
	*x = CLGuildDonate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildDonate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildDonate) ProtoMessage() {}

func (x *CLGuildDonate) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildDonate.ProtoReflect.Descriptor instead.
func (*CLGuildDonate) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{61}
}

func (x *CLGuildDonate) GetOpt() int32 {
	if x != nil {
		return x.Opt
	}
	return 0
}

//公会排行榜
type CLGuildRank struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildRank) Reset() {
	*x = CLGuildRank{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildRank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildRank) ProtoMessage() {}

func (x *CLGuildRank) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildRank.ProtoReflect.Descriptor instead.
func (*CLGuildRank) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{62}
}

//公会日志
type CLGuildLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildLog) Reset() {
	*x = CLGuildLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildLog) ProtoMessage() {}

func (x *CLGuildLog) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildLog.ProtoReflect.Descriptor instead.
func (*CLGuildLog) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{63}
}

//公会砍价礼包点击
type CLGuildBargainingInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //礼包唯一 ID
}

func (x *CLGuildBargainingInfo) Reset() {
	*x = CLGuildBargainingInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildBargainingInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildBargainingInfo) ProtoMessage() {}

func (x *CLGuildBargainingInfo) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildBargainingInfo.ProtoReflect.Descriptor instead.
func (*CLGuildBargainingInfo) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{64}
}

func (x *CLGuildBargainingInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

//公会砍价礼包提醒砍价
type CLGuildBargainingNotice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //礼包唯一 ID
}

func (x *CLGuildBargainingNotice) Reset() {
	*x = CLGuildBargainingNotice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildBargainingNotice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildBargainingNotice) ProtoMessage() {}

func (x *CLGuildBargainingNotice) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildBargainingNotice.ProtoReflect.Descriptor instead.
func (*CLGuildBargainingNotice) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{65}
}

func (x *CLGuildBargainingNotice) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

//公会砍价礼包砍价
type CLGuildBargaining struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //礼包唯一 ID
}

func (x *CLGuildBargaining) Reset() {
	*x = CLGuildBargaining{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildBargaining) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildBargaining) ProtoMessage() {}

func (x *CLGuildBargaining) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildBargaining.ProtoReflect.Descriptor instead.
func (*CLGuildBargaining) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{66}
}

func (x *CLGuildBargaining) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

//公会砍价礼包购买
type CLGuildBargainingBuy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //礼包唯一 ID
}

func (x *CLGuildBargainingBuy) Reset() {
	*x = CLGuildBargainingBuy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildBargainingBuy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildBargainingBuy) ProtoMessage() {}

func (x *CLGuildBargainingBuy) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildBargainingBuy.ProtoReflect.Descriptor instead.
func (*CLGuildBargainingBuy) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{67}
}

func (x *CLGuildBargainingBuy) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

//公会 BOSS 进入
type CLGuildBossEnter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildBossEnter) Reset() {
	*x = CLGuildBossEnter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildBossEnter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildBossEnter) ProtoMessage() {}

func (x *CLGuildBossEnter) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildBossEnter.ProtoReflect.Descriptor instead.
func (*CLGuildBossEnter) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{68}
}

//公会 BOSS 挑战
type CLGuildBossAtkBegin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildBossAtkBegin) Reset() {
	*x = CLGuildBossAtkBegin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildBossAtkBegin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildBossAtkBegin) ProtoMessage() {}

func (x *CLGuildBossAtkBegin) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildBossAtkBegin.ProtoReflect.Descriptor instead.
func (*CLGuildBossAtkBegin) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{69}
}

//公会 BOSS 扫荡
type CLGuildBossSweep struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildBossSweep) Reset() {
	*x = CLGuildBossSweep{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildBossSweep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildBossSweep) ProtoMessage() {}

func (x *CLGuildBossSweep) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildBossSweep.ProtoReflect.Descriptor instead.
func (*CLGuildBossSweep) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{70}
}

//公会 BOSS 次数购买
type CLGuildBossBuyCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BuyCount int32 `protobuf:"varint,1,opt,name=buyCount,proto3" json:"buyCount,omitempty"` //购买次数
}

func (x *CLGuildBossBuyCount) Reset() {
	*x = CLGuildBossBuyCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildBossBuyCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildBossBuyCount) ProtoMessage() {}

func (x *CLGuildBossBuyCount) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildBossBuyCount.ProtoReflect.Descriptor instead.
func (*CLGuildBossBuyCount) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{71}
}

func (x *CLGuildBossBuyCount) GetBuyCount() int32 {
	if x != nil {
		return x.BuyCount
	}
	return 0
}

//公会 BOSS 总排行
type CLGuildBossRank struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildBossRank) Reset() {
	*x = CLGuildBossRank{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildBossRank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildBossRank) ProtoMessage() {}

func (x *CLGuildBossRank) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildBossRank.ProtoReflect.Descriptor instead.
func (*CLGuildBossRank) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{72}
}

//=====================公会系统结束===================================
//=====================聊天开始===================================
// 聊天
type CLSendChatInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Chattype public.ChatType `protobuf:"varint,1,opt,name=chattype,proto3,enum=ChatType" json:"chattype,omitempty"`
	Content  string          `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`    //内容
	ToPlayer int64           `protobuf:"varint,3,opt,name=toPlayer,proto3" json:"toPlayer,omitempty"` //私聊的对象
}

func (x *CLSendChatInfo) Reset() {
	*x = CLSendChatInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLSendChatInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLSendChatInfo) ProtoMessage() {}

func (x *CLSendChatInfo) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLSendChatInfo.ProtoReflect.Descriptor instead.
func (*CLSendChatInfo) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{73}
}

func (x *CLSendChatInfo) GetChattype() public.ChatType {
	if x != nil {
		return x.Chattype
	}
	return public.ChatType_ChatType_World
}

func (x *CLSendChatInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *CLSendChatInfo) GetToPlayer() int64 {
	if x != nil {
		return x.ToPlayer
	}
	return 0
}

// 聊天去掉私聊红点
type CLPrivateRed struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ToPlayer int64 `protobuf:"varint,1,opt,name=toPlayer,proto3" json:"toPlayer,omitempty"` //私聊的对象
}

func (x *CLPrivateRed) Reset() {
	*x = CLPrivateRed{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLPrivateRed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLPrivateRed) ProtoMessage() {}

func (x *CLPrivateRed) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLPrivateRed.ProtoReflect.Descriptor instead.
func (*CLPrivateRed) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{74}
}

func (x *CLPrivateRed) GetToPlayer() int64 {
	if x != nil {
		return x.ToPlayer
	}
	return 0
}

// 聊天拉取
type CLWorldMsgGet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Chattype public.ChatType `protobuf:"varint,1,opt,name=chattype,proto3,enum=ChatType" json:"chattype,omitempty"`
	MsgId    int64           `protobuf:"varint,2,opt,name=msgId,proto3" json:"msgId,omitempty"` //聊天编号  从这个编号往后发 100
}

func (x *CLWorldMsgGet) Reset() {
	*x = CLWorldMsgGet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLWorldMsgGet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLWorldMsgGet) ProtoMessage() {}

func (x *CLWorldMsgGet) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLWorldMsgGet.ProtoReflect.Descriptor instead.
func (*CLWorldMsgGet) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{75}
}

func (x *CLWorldMsgGet) GetChattype() public.ChatType {
	if x != nil {
		return x.Chattype
	}
	return public.ChatType_ChatType_World
}

func (x *CLWorldMsgGet) GetMsgId() int64 {
	if x != nil {
		return x.MsgId
	}
	return 0
}

//=====================好友开始===================================
//请求推荐好友
type CLRecommendFriendReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChangeBatch bool `protobuf:"varint,1,opt,name=changeBatch,proto3" json:"changeBatch,omitempty"` //是否是换一批请求 true:是 false:否
}

func (x *CLRecommendFriendReq) Reset() {
	*x = CLRecommendFriendReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLRecommendFriendReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLRecommendFriendReq) ProtoMessage() {}

func (x *CLRecommendFriendReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLRecommendFriendReq.ProtoReflect.Descriptor instead.
func (*CLRecommendFriendReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{76}
}

func (x *CLRecommendFriendReq) GetChangeBatch() bool {
	if x != nil {
		return x.ChangeBatch
	}
	return false
}

//请求好友信息 (会同步好友列表，好友申请列表和黑名单)
type CLSyncAllFriendsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLSyncAllFriendsReq) Reset() {
	*x = CLSyncAllFriendsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLSyncAllFriendsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLSyncAllFriendsReq) ProtoMessage() {}

func (x *CLSyncAllFriendsReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLSyncAllFriendsReq.ProtoReflect.Descriptor instead.
func (*CLSyncAllFriendsReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{77}
}

//请求好友礼物
type CLAllGiftReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLAllGiftReq) Reset() {
	*x = CLAllGiftReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLAllGiftReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLAllGiftReq) ProtoMessage() {}

func (x *CLAllGiftReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLAllGiftReq.ProtoReflect.Descriptor instead.
func (*CLAllGiftReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{78}
}

//添加好友
type CLAddOneFriendReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerUid int64 `protobuf:"varint,1,opt,name=playerUid,proto3" json:"playerUid,omitempty"` //添加的好友 id
}

func (x *CLAddOneFriendReq) Reset() {
	*x = CLAddOneFriendReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLAddOneFriendReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLAddOneFriendReq) ProtoMessage() {}

func (x *CLAddOneFriendReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLAddOneFriendReq.ProtoReflect.Descriptor instead.
func (*CLAddOneFriendReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{79}
}

func (x *CLAddOneFriendReq) GetPlayerUid() int64 {
	if x != nil {
		return x.PlayerUid
	}
	return 0
}

//同意好友
type CLAgreeFriendReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerUid int64 `protobuf:"varint,1,opt,name=playerUid,proto3" json:"playerUid,omitempty"` //同意的好友 id(0 表示一键同意)
}

func (x *CLAgreeFriendReq) Reset() {
	*x = CLAgreeFriendReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLAgreeFriendReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLAgreeFriendReq) ProtoMessage() {}

func (x *CLAgreeFriendReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLAgreeFriendReq.ProtoReflect.Descriptor instead.
func (*CLAgreeFriendReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{80}
}

func (x *CLAgreeFriendReq) GetPlayerUid() int64 {
	if x != nil {
		return x.PlayerUid
	}
	return 0
}

//拒绝好友
type CLRefuseFriendReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerUid int64 `protobuf:"varint,1,opt,name=playerUid,proto3" json:"playerUid,omitempty"` //拒绝的好友 id(0 表示一键拒绝)
}

func (x *CLRefuseFriendReq) Reset() {
	*x = CLRefuseFriendReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLRefuseFriendReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLRefuseFriendReq) ProtoMessage() {}

func (x *CLRefuseFriendReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLRefuseFriendReq.ProtoReflect.Descriptor instead.
func (*CLRefuseFriendReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{81}
}

func (x *CLRefuseFriendReq) GetPlayerUid() int64 {
	if x != nil {
		return x.PlayerUid
	}
	return 0
}

//删除一个好友
type CLDelOneFriendReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerUid int64 `protobuf:"varint,1,opt,name=playerUid,proto3" json:"playerUid,omitempty"` //删除的好友 id
}

func (x *CLDelOneFriendReq) Reset() {
	*x = CLDelOneFriendReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLDelOneFriendReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLDelOneFriendReq) ProtoMessage() {}

func (x *CLDelOneFriendReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLDelOneFriendReq.ProtoReflect.Descriptor instead.
func (*CLDelOneFriendReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{82}
}

func (x *CLDelOneFriendReq) GetPlayerUid() int64 {
	if x != nil {
		return x.PlayerUid
	}
	return 0
}

//操作黑名单
type CLblacklistOperationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId int64 `protobuf:"varint,1,opt,name=playerId,proto3" json:"playerId,omitempty"` //要操作黑名单的 id
	Type     int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`         //1:添加，2：移除
}

func (x *CLblacklistOperationReq) Reset() {
	*x = CLblacklistOperationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLblacklistOperationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLblacklistOperationReq) ProtoMessage() {}

func (x *CLblacklistOperationReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLblacklistOperationReq.ProtoReflect.Descriptor instead.
func (*CLblacklistOperationReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{83}
}

func (x *CLblacklistOperationReq) GetPlayerId() int64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *CLblacklistOperationReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

//======================问卷红点========================================
// 问卷去掉红点
type CLQuestRed struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestId int32 `protobuf:"varint,1,opt,name=questId,proto3" json:"questId,omitempty"` //问卷 id
}

func (x *CLQuestRed) Reset() {
	*x = CLQuestRed{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLQuestRed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLQuestRed) ProtoMessage() {}

func (x *CLQuestRed) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLQuestRed.ProtoReflect.Descriptor instead.
func (*CLQuestRed) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{84}
}

func (x *CLQuestRed) GetQuestId() int32 {
	if x != nil {
		return x.QuestId
	}
	return 0
}

//======================BI=========================================
//BI
type CLBILog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Params []string `protobuf:"bytes,1,rep,name=params,proto3" json:"params,omitempty"` //数据
	Type   int32    `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`    //类型
}

func (x *CLBILog) Reset() {
	*x = CLBILog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLBILog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLBILog) ProtoMessage() {}

func (x *CLBILog) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLBILog.ProtoReflect.Descriptor instead.
func (*CLBILog) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{85}
}

func (x *CLBILog) GetParams() []string {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *CLBILog) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

//客户端领取广告奖励
type CLClientAdReceive struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Adtype int32 `protobuf:"varint,1,opt,name=Adtype,proto3" json:"Adtype,omitempty"` //  激励视频类型 IronSourceADype
}

func (x *CLClientAdReceive) Reset() {
	*x = CLClientAdReceive{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLClientAdReceive) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLClientAdReceive) ProtoMessage() {}

func (x *CLClientAdReceive) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLClientAdReceive.ProtoReflect.Descriptor instead.
func (*CLClientAdReceive) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{86}
}

func (x *CLClientAdReceive) GetAdtype() int32 {
	if x != nil {
		return x.Adtype
	}
	return 0
}

//======================BI=========================================
//公会 TOP 请求
type CLGuildTopList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGuildTopList) Reset() {
	*x = CLGuildTopList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGuildTopList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGuildTopList) ProtoMessage() {}

func (x *CLGuildTopList) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGuildTopList.ProtoReflect.Descriptor instead.
func (*CLGuildTopList) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{87}
}

//======================头像开始=========================================
// 请求头像信息
type CLHeadIconReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLHeadIconReq) Reset() {
	*x = CLHeadIconReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLHeadIconReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLHeadIconReq) ProtoMessage() {}

func (x *CLHeadIconReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLHeadIconReq.ProtoReflect.Descriptor instead.
func (*CLHeadIconReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{88}
}

//替换头像
type CLReplaceHeadIconReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeadIconId int32 `protobuf:"varint,1,opt,name=headIconId,proto3" json:"headIconId,omitempty"` //头像 id
}

func (x *CLReplaceHeadIconReq) Reset() {
	*x = CLReplaceHeadIconReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLReplaceHeadIconReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLReplaceHeadIconReq) ProtoMessage() {}

func (x *CLReplaceHeadIconReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLReplaceHeadIconReq.ProtoReflect.Descriptor instead.
func (*CLReplaceHeadIconReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{89}
}

func (x *CLReplaceHeadIconReq) GetHeadIconId() int32 {
	if x != nil {
		return x.HeadIconId
	}
	return 0
}

//手动解锁头像，前提是已拥有。
type CLUnlockHeadIcon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeadIconId int32 `protobuf:"varint,1,opt,name=headIconId,proto3" json:"headIconId,omitempty"`
}

func (x *CLUnlockHeadIcon) Reset() {
	*x = CLUnlockHeadIcon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLUnlockHeadIcon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLUnlockHeadIcon) ProtoMessage() {}

func (x *CLUnlockHeadIcon) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLUnlockHeadIcon.ProtoReflect.Descriptor instead.
func (*CLUnlockHeadIcon) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{90}
}

func (x *CLUnlockHeadIcon) GetHeadIconId() int32 {
	if x != nil {
		return x.HeadIconId
	}
	return 0
}

//======================头像框开始=========================================
// 请求头像框信息
type CLHeadFrame struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLHeadFrame) Reset() {
	*x = CLHeadFrame{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLHeadFrame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLHeadFrame) ProtoMessage() {}

func (x *CLHeadFrame) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLHeadFrame.ProtoReflect.Descriptor instead.
func (*CLHeadFrame) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{91}
}

//替换头像框
type CLReplaceHeadFrame struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeadFrameId int32 `protobuf:"varint,1,opt,name=headFrameId,proto3" json:"headFrameId,omitempty"` //头像框 id
}

func (x *CLReplaceHeadFrame) Reset() {
	*x = CLReplaceHeadFrame{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLReplaceHeadFrame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLReplaceHeadFrame) ProtoMessage() {}

func (x *CLReplaceHeadFrame) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLReplaceHeadFrame.ProtoReflect.Descriptor instead.
func (*CLReplaceHeadFrame) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{92}
}

func (x *CLReplaceHeadFrame) GetHeadFrameId() int32 {
	if x != nil {
		return x.HeadFrameId
	}
	return 0
}

//手动解锁头像框，前提是已拥有。
type CLUnlockHeadFrame struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeadFrameId int32 `protobuf:"varint,1,opt,name=headFrameId,proto3" json:"headFrameId,omitempty"`
}

func (x *CLUnlockHeadFrame) Reset() {
	*x = CLUnlockHeadFrame{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLUnlockHeadFrame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLUnlockHeadFrame) ProtoMessage() {}

func (x *CLUnlockHeadFrame) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLUnlockHeadFrame.ProtoReflect.Descriptor instead.
func (*CLUnlockHeadFrame) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{93}
}

func (x *CLUnlockHeadFrame) GetHeadFrameId() int32 {
	if x != nil {
		return x.HeadFrameId
	}
	return 0
}

//======================商城购买礼包=========================================
type CLGiftBuy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GiftId int32 `protobuf:"varint,1,opt,name=giftId,proto3" json:"giftId,omitempty"` //礼包ID
}

func (x *CLGiftBuy) Reset() {
	*x = CLGiftBuy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGiftBuy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGiftBuy) ProtoMessage() {}

func (x *CLGiftBuy) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGiftBuy.ProtoReflect.Descriptor instead.
func (*CLGiftBuy) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{94}
}

func (x *CLGiftBuy) GetGiftId() int32 {
	if x != nil {
		return x.GiftId
	}
	return 0
}

//======================限时商城购买礼包=========================================
type CLTimeGiftBuy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeGiftId int32 `protobuf:"varint,1,opt,name=timeGiftId,proto3" json:"timeGiftId,omitempty"` //TimeGiftPacks礼包ID
}

func (x *CLTimeGiftBuy) Reset() {
	*x = CLTimeGiftBuy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLTimeGiftBuy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLTimeGiftBuy) ProtoMessage() {}

func (x *CLTimeGiftBuy) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLTimeGiftBuy.ProtoReflect.Descriptor instead.
func (*CLTimeGiftBuy) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{95}
}

func (x *CLTimeGiftBuy) GetTimeGiftId() int32 {
	if x != nil {
		return x.TimeGiftId
	}
	return 0
}

//======================七日签到开始=========================================
// 请求七日签到信息
type CLSevenSignInGetData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLSevenSignInGetData) Reset() {
	*x = CLSevenSignInGetData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[96]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLSevenSignInGetData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLSevenSignInGetData) ProtoMessage() {}

func (x *CLSevenSignInGetData) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[96]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLSevenSignInGetData.ProtoReflect.Descriptor instead.
func (*CLSevenSignInGetData) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{96}
}

// 请求领取七日签到奖励
type CLSevenSignInGetAward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SignInDay int32 `protobuf:"varint,1,opt,name=signInDay,proto3" json:"signInDay,omitempty"` // 签到天数
}

func (x *CLSevenSignInGetAward) Reset() {
	*x = CLSevenSignInGetAward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[97]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLSevenSignInGetAward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLSevenSignInGetAward) ProtoMessage() {}

func (x *CLSevenSignInGetAward) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[97]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLSevenSignInGetAward.ProtoReflect.Descriptor instead.
func (*CLSevenSignInGetAward) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{97}
}

func (x *CLSevenSignInGetAward) GetSignInDay() int32 {
	if x != nil {
		return x.SignInDay
	}
	return 0
}

//======================每日签到开始=========================================
// 请求每日签到信息
type CLDailySignInGetData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLDailySignInGetData) Reset() {
	*x = CLDailySignInGetData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[98]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLDailySignInGetData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLDailySignInGetData) ProtoMessage() {}

func (x *CLDailySignInGetData) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[98]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLDailySignInGetData.ProtoReflect.Descriptor instead.
func (*CLDailySignInGetData) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{98}
}

// 请求领取每日签到奖励
type CLDailySignInGetAward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SignInDay int32 `protobuf:"varint,1,opt,name=signInDay,proto3" json:"signInDay,omitempty"` // 领取（第几）天奖励
}

func (x *CLDailySignInGetAward) Reset() {
	*x = CLDailySignInGetAward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[99]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLDailySignInGetAward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLDailySignInGetAward) ProtoMessage() {}

func (x *CLDailySignInGetAward) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[99]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLDailySignInGetAward.ProtoReflect.Descriptor instead.
func (*CLDailySignInGetAward) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{99}
}

func (x *CLDailySignInGetAward) GetSignInDay() int32 {
	if x != nil {
		return x.SignInDay
	}
	return 0
}

// 请求领取每日签到累签奖励
type CLDailySignInGetAccruedAward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` // 累签id
}

func (x *CLDailySignInGetAccruedAward) Reset() {
	*x = CLDailySignInGetAccruedAward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[100]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLDailySignInGetAccruedAward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLDailySignInGetAccruedAward) ProtoMessage() {}

func (x *CLDailySignInGetAccruedAward) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[100]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLDailySignInGetAccruedAward.ProtoReflect.Descriptor instead.
func (*CLDailySignInGetAccruedAward) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{100}
}

func (x *CLDailySignInGetAccruedAward) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

//======================首冲礼包=========================================
type CLFirstChargeGetReward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GiftId int32 `protobuf:"varint,1,opt,name=giftId,proto3" json:"giftId,omitempty"` //档位
	Days   int32 `protobuf:"varint,2,opt,name=days,proto3" json:"days,omitempty"`     //第几天的
}

func (x *CLFirstChargeGetReward) Reset() {
	*x = CLFirstChargeGetReward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[101]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLFirstChargeGetReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLFirstChargeGetReward) ProtoMessage() {}

func (x *CLFirstChargeGetReward) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[101]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLFirstChargeGetReward.ProtoReflect.Descriptor instead.
func (*CLFirstChargeGetReward) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{101}
}

func (x *CLFirstChargeGetReward) GetGiftId() int32 {
	if x != nil {
		return x.GiftId
	}
	return 0
}

func (x *CLFirstChargeGetReward) GetDays() int32 {
	if x != nil {
		return x.Days
	}
	return 0
}

//购买首冲礼包
type CLBuyFirstChargeGift struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GiftId int32 `protobuf:"varint,1,opt,name=giftId,proto3" json:"giftId,omitempty"` //礼包档位ID
}

func (x *CLBuyFirstChargeGift) Reset() {
	*x = CLBuyFirstChargeGift{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[102]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLBuyFirstChargeGift) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLBuyFirstChargeGift) ProtoMessage() {}

func (x *CLBuyFirstChargeGift) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[102]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLBuyFirstChargeGift.ProtoReflect.Descriptor instead.
func (*CLBuyFirstChargeGift) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{102}
}

func (x *CLBuyFirstChargeGift) GetGiftId() int32 {
	if x != nil {
		return x.GiftId
	}
	return 0
}

//======================充值返利开始==========================================
//请求充值返利数据
type CLTopupRebateGetData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLTopupRebateGetData) Reset() {
	*x = CLTopupRebateGetData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[103]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLTopupRebateGetData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLTopupRebateGetData) ProtoMessage() {}

func (x *CLTopupRebateGetData) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[103]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLTopupRebateGetData.ProtoReflect.Descriptor instead.
func (*CLTopupRebateGetData) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{103}
}

//请求领取充值返利奖励
type CLTopupRebateGetAward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TopupTaskId int32 `protobuf:"varint,1,opt,name=topupTaskId,proto3" json:"topupTaskId,omitempty"` // 充值返利任务Id
}

func (x *CLTopupRebateGetAward) Reset() {
	*x = CLTopupRebateGetAward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[104]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLTopupRebateGetAward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLTopupRebateGetAward) ProtoMessage() {}

func (x *CLTopupRebateGetAward) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[104]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLTopupRebateGetAward.ProtoReflect.Descriptor instead.
func (*CLTopupRebateGetAward) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{104}
}

func (x *CLTopupRebateGetAward) GetTopupTaskId() int32 {
	if x != nil {
		return x.TopupTaskId
	}
	return 0
}

//======================月卡开始==========================================
//请求月卡数据
type CLMonthlyCardGetData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLMonthlyCardGetData) Reset() {
	*x = CLMonthlyCardGetData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[105]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLMonthlyCardGetData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLMonthlyCardGetData) ProtoMessage() {}

func (x *CLMonthlyCardGetData) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[105]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLMonthlyCardGetData.ProtoReflect.Descriptor instead.
func (*CLMonthlyCardGetData) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{105}
}

//请求购买月卡数据
type CLMonthlyCardBuyCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardId int32 `protobuf:"varint,1,opt,name=cardId,proto3" json:"cardId,omitempty"` // 月卡Id
}

func (x *CLMonthlyCardBuyCard) Reset() {
	*x = CLMonthlyCardBuyCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[106]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLMonthlyCardBuyCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLMonthlyCardBuyCard) ProtoMessage() {}

func (x *CLMonthlyCardBuyCard) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[106]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLMonthlyCardBuyCard.ProtoReflect.Descriptor instead.
func (*CLMonthlyCardBuyCard) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{106}
}

func (x *CLMonthlyCardBuyCard) GetCardId() int32 {
	if x != nil {
		return x.CardId
	}
	return 0
}

//======================月卡2.0开始==========================================
//请求月卡2.0数据
type CLMonthlyCardNewGetData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLMonthlyCardNewGetData) Reset() {
	*x = CLMonthlyCardNewGetData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[107]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLMonthlyCardNewGetData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLMonthlyCardNewGetData) ProtoMessage() {}

func (x *CLMonthlyCardNewGetData) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[107]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLMonthlyCardNewGetData.ProtoReflect.Descriptor instead.
func (*CLMonthlyCardNewGetData) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{107}
}

//请求月卡2.0额外奖励
type CLMonthlyCardNewGetExtraReward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLMonthlyCardNewGetExtraReward) Reset() {
	*x = CLMonthlyCardNewGetExtraReward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[108]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLMonthlyCardNewGetExtraReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLMonthlyCardNewGetExtraReward) ProtoMessage() {}

func (x *CLMonthlyCardNewGetExtraReward) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[108]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLMonthlyCardNewGetExtraReward.ProtoReflect.Descriptor instead.
func (*CLMonthlyCardNewGetExtraReward) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{108}
}

//======================等级基金开始==========================================
//请求充值返利数据
type CLGradedFundGetData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGradedFundGetData) Reset() {
	*x = CLGradedFundGetData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[109]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGradedFundGetData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGradedFundGetData) ProtoMessage() {}

func (x *CLGradedFundGetData) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[109]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGradedFundGetData.ProtoReflect.Descriptor instead.
func (*CLGradedFundGetData) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{109}
}

//请求购买等级基金
type CLGradedFundBuyFund struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GradedFundIdx int32 `protobuf:"varint,1,opt,name=gradedFundIdx,proto3" json:"gradedFundIdx,omitempty"` // 基金阶段索引[表格Id]
}

func (x *CLGradedFundBuyFund) Reset() {
	*x = CLGradedFundBuyFund{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[110]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGradedFundBuyFund) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGradedFundBuyFund) ProtoMessage() {}

func (x *CLGradedFundBuyFund) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[110]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGradedFundBuyFund.ProtoReflect.Descriptor instead.
func (*CLGradedFundBuyFund) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{110}
}

func (x *CLGradedFundBuyFund) GetGradedFundIdx() int32 {
	if x != nil {
		return x.GradedFundIdx
	}
	return 0
}

//请求领取普通等级基金
type CLGradedFundGetComWeal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LevelStageIdx int32 `protobuf:"varint,1,opt,name=levelStageIdx,proto3" json:"levelStageIdx,omitempty"` // 等级阶段索引[表格Id]
}

func (x *CLGradedFundGetComWeal) Reset() {
	*x = CLGradedFundGetComWeal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[111]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGradedFundGetComWeal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGradedFundGetComWeal) ProtoMessage() {}

func (x *CLGradedFundGetComWeal) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[111]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGradedFundGetComWeal.ProtoReflect.Descriptor instead.
func (*CLGradedFundGetComWeal) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{111}
}

func (x *CLGradedFundGetComWeal) GetLevelStageIdx() int32 {
	if x != nil {
		return x.LevelStageIdx
	}
	return 0
}

//请求领取超级等级基金
type CLGradedFundGetSuperWeal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LevelStageIdx int32 `protobuf:"varint,1,opt,name=levelStageIdx,proto3" json:"levelStageIdx,omitempty"` // 等级阶段索引[表格Id]
}

func (x *CLGradedFundGetSuperWeal) Reset() {
	*x = CLGradedFundGetSuperWeal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[112]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGradedFundGetSuperWeal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGradedFundGetSuperWeal) ProtoMessage() {}

func (x *CLGradedFundGetSuperWeal) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[112]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGradedFundGetSuperWeal.ProtoReflect.Descriptor instead.
func (*CLGradedFundGetSuperWeal) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{112}
}

func (x *CLGradedFundGetSuperWeal) GetLevelStageIdx() int32 {
	if x != nil {
		return x.LevelStageIdx
	}
	return 0
}

//======================任务开始==========================================
//请求提交任务 （领取任务奖励）
type CLMissionSubmit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MissionType int32 `protobuf:"varint,1,opt,name=mission_type,json=missionType,proto3" json:"mission_type,omitempty"` // 任务类型
}

func (x *CLMissionSubmit) Reset() {
	*x = CLMissionSubmit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[113]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLMissionSubmit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLMissionSubmit) ProtoMessage() {}

func (x *CLMissionSubmit) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[113]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLMissionSubmit.ProtoReflect.Descriptor instead.
func (*CLMissionSubmit) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{113}
}

func (x *CLMissionSubmit) GetMissionType() int32 {
	if x != nil {
		return x.MissionType
	}
	return 0
}

//请求提交任务 （领取任务奖励）
type CLMissionSubmitById struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MissionIdList []int32 `protobuf:"varint,1,rep,packed,name=mission_id_list,json=missionIdList,proto3" json:"mission_id_list,omitempty"` //任务ID list
}

func (x *CLMissionSubmitById) Reset() {
	*x = CLMissionSubmitById{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[114]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLMissionSubmitById) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLMissionSubmitById) ProtoMessage() {}

func (x *CLMissionSubmitById) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[114]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLMissionSubmitById.ProtoReflect.Descriptor instead.
func (*CLMissionSubmitById) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{114}
}

func (x *CLMissionSubmitById) GetMissionIdList() []int32 {
	if x != nil {
		return x.MissionIdList
	}
	return nil
}

//=====================支付开始===================================
// 付款预请求
type CLPaymentPreRequestReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GoodsRegisterId string `protobuf:"bytes,1,opt,name=goodsRegisterId,proto3" json:"goodsRegisterId,omitempty"` // 商品注册ID
	ModuleType      int32  `protobuf:"varint,2,opt,name=moduleType,proto3" json:"moduleType,omitempty"`          // 支付对应模块
	GoodsPrice      int32  `protobuf:"varint,3,opt,name=goodsPrice,proto3" json:"goodsPrice,omitempty"`          // 产品实际支付价格（RMB级别为元，此参数不带引号，请用数字类型处理）
	ChannelId       string `protobuf:"bytes,4,opt,name=channelId,proto3" json:"channelId,omitempty"`             // 渠道标识
	GameGoodsId     string `protobuf:"bytes,5,opt,name=gameGoodsId,proto3" json:"gameGoodsId,omitempty"`         // 游戏商品ID
}

func (x *CLPaymentPreRequestReq) Reset() {
	*x = CLPaymentPreRequestReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[115]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLPaymentPreRequestReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLPaymentPreRequestReq) ProtoMessage() {}

func (x *CLPaymentPreRequestReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[115]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLPaymentPreRequestReq.ProtoReflect.Descriptor instead.
func (*CLPaymentPreRequestReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{115}
}

func (x *CLPaymentPreRequestReq) GetGoodsRegisterId() string {
	if x != nil {
		return x.GoodsRegisterId
	}
	return ""
}

func (x *CLPaymentPreRequestReq) GetModuleType() int32 {
	if x != nil {
		return x.ModuleType
	}
	return 0
}

func (x *CLPaymentPreRequestReq) GetGoodsPrice() int32 {
	if x != nil {
		return x.GoodsPrice
	}
	return 0
}

func (x *CLPaymentPreRequestReq) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *CLPaymentPreRequestReq) GetGameGoodsId() string {
	if x != nil {
		return x.GameGoodsId
	}
	return ""
}

//=====================活动开始===================================
// 领取活动奖励
type CLGetActivityReward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId  int32   `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`           // 活动ID
	RewardParam []int32 `protobuf:"varint,2,rep,packed,name=reward_param,json=rewardParam,proto3" json:"reward_param,omitempty"` // 奖励参数
}

func (x *CLGetActivityReward) Reset() {
	*x = CLGetActivityReward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[116]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGetActivityReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGetActivityReward) ProtoMessage() {}

func (x *CLGetActivityReward) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[116]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGetActivityReward.ProtoReflect.Descriptor instead.
func (*CLGetActivityReward) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{116}
}

func (x *CLGetActivityReward) GetActivityId() int32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *CLGetActivityReward) GetRewardParam() []int32 {
	if x != nil {
		return x.RewardParam
	}
	return nil
}

// 请求七日活动数据
type CLGetSevenDayActivityData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId int32 `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"` // 活动ID
}

func (x *CLGetSevenDayActivityData) Reset() {
	*x = CLGetSevenDayActivityData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[117]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGetSevenDayActivityData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGetSevenDayActivityData) ProtoMessage() {}

func (x *CLGetSevenDayActivityData) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[117]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGetSevenDayActivityData.ProtoReflect.Descriptor instead.
func (*CLGetSevenDayActivityData) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{117}
}

func (x *CLGetSevenDayActivityData) GetActivityId() int32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

//=====================删除账号开始===================================
// 删除账号
type CLDeleteAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceId string `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"` // 用户设备ID
	Token    string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`       // 橙柚账号用户登录token，cysid
	Cyid     string `protobuf:"bytes,3,opt,name=cyid,proto3" json:"cyid,omitempty"`         // 橙柚账号用户ID,cyid
}

func (x *CLDeleteAccount) Reset() {
	*x = CLDeleteAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[118]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLDeleteAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLDeleteAccount) ProtoMessage() {}

func (x *CLDeleteAccount) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[118]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLDeleteAccount.ProtoReflect.Descriptor instead.
func (*CLDeleteAccount) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{118}
}

func (x *CLDeleteAccount) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *CLDeleteAccount) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *CLDeleteAccount) GetCyid() string {
	if x != nil {
		return x.Cyid
	}
	return ""
}

//=====================礼包码开始===================================
// 领取礼包码对应道具奖励
type CLRedeemCodeRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RedeemCodeId string `protobuf:"bytes,1,opt,name=RedeemCodeId,proto3" json:"RedeemCodeId,omitempty"` // 礼包码ID
}

func (x *CLRedeemCodeRewardReq) Reset() {
	*x = CLRedeemCodeRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[119]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLRedeemCodeRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLRedeemCodeRewardReq) ProtoMessage() {}

func (x *CLRedeemCodeRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[119]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLRedeemCodeRewardReq.ProtoReflect.Descriptor instead.
func (*CLRedeemCodeRewardReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{119}
}

func (x *CLRedeemCodeRewardReq) GetRedeemCodeId() string {
	if x != nil {
		return x.RedeemCodeId
	}
	return ""
}

//======================千抽开始=========================================
//请求千抽信息
type CLGachaBonusGetData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGachaBonusGetData) Reset() {
	*x = CLGachaBonusGetData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[120]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGachaBonusGetData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGachaBonusGetData) ProtoMessage() {}

func (x *CLGachaBonusGetData) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[120]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGachaBonusGetData.ProtoReflect.Descriptor instead.
func (*CLGachaBonusGetData) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{120}
}

//请求千抽奖励
type CLGachaBonusGetAward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Stage int32 `protobuf:"varint,1,opt,name=stage,proto3" json:"stage,omitempty"` // 关卡id
}

func (x *CLGachaBonusGetAward) Reset() {
	*x = CLGachaBonusGetAward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[121]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGachaBonusGetAward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGachaBonusGetAward) ProtoMessage() {}

func (x *CLGachaBonusGetAward) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[121]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGachaBonusGetAward.ProtoReflect.Descriptor instead.
func (*CLGachaBonusGetAward) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{121}
}

func (x *CLGachaBonusGetAward) GetStage() int32 {
	if x != nil {
		return x.Stage
	}
	return 0
}

//=====================功能预告开始=================================
// 领取功能预告对应奖励
type CLGetFuncPrevReward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FuncId int32 `protobuf:"varint,1,opt,name=FuncId,proto3" json:"FuncId,omitempty"` // 功能预告 Id
}

func (x *CLGetFuncPrevReward) Reset() {
	*x = CLGetFuncPrevReward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[122]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGetFuncPrevReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGetFuncPrevReward) ProtoMessage() {}

func (x *CLGetFuncPrevReward) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[122]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGetFuncPrevReward.ProtoReflect.Descriptor instead.
func (*CLGetFuncPrevReward) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{122}
}

func (x *CLGetFuncPrevReward) GetFuncId() int32 {
	if x != nil {
		return x.FuncId
	}
	return 0
}

//=====================问卷开始=================================
// 领取问卷奖励
type CLGetQuestionReward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` // 问卷id
}

func (x *CLGetQuestionReward) Reset() {
	*x = CLGetQuestionReward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[123]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGetQuestionReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGetQuestionReward) ProtoMessage() {}

func (x *CLGetQuestionReward) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[123]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGetQuestionReward.ProtoReflect.Descriptor instead.
func (*CLGetQuestionReward) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{123}
}

func (x *CLGetQuestionReward) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 轮盘抽奖
type CLGachaWheelReward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGachaWheelReward) Reset() {
	*x = CLGachaWheelReward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[124]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGachaWheelReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGachaWheelReward) ProtoMessage() {}

func (x *CLGachaWheelReward) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[124]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGachaWheelReward.ProtoReflect.Descriptor instead.
func (*CLGachaWheelReward) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{124}
}

//=============爬塔开始=============================
//首页
type CL_TowerMain struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CL_TowerMain) Reset() {
	*x = CL_TowerMain{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[125]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CL_TowerMain) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CL_TowerMain) ProtoMessage() {}

func (x *CL_TowerMain) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[125]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CL_TowerMain.ProtoReflect.Descriptor instead.
func (*CL_TowerMain) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{125}
}

//爬塔开始
type CL_TowerStart struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StageId int32 `protobuf:"varint,1,opt,name=stageId,proto3" json:"stageId,omitempty"` //关卡id
}

func (x *CL_TowerStart) Reset() {
	*x = CL_TowerStart{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[126]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CL_TowerStart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CL_TowerStart) ProtoMessage() {}

func (x *CL_TowerStart) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[126]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CL_TowerStart.ProtoReflect.Descriptor instead.
func (*CL_TowerStart) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{126}
}

func (x *CL_TowerStart) GetStageId() int32 {
	if x != nil {
		return x.StageId
	}
	return 0
}

//=============爬塔结束=============================
//============日进斗金开始=============================
//首页
type CLTotalRechargeGetReward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TableId int32 `protobuf:"varint,1,opt,name=tableId,proto3" json:"tableId,omitempty"` //天ID
}

func (x *CLTotalRechargeGetReward) Reset() {
	*x = CLTotalRechargeGetReward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[127]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLTotalRechargeGetReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLTotalRechargeGetReward) ProtoMessage() {}

func (x *CLTotalRechargeGetReward) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[127]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLTotalRechargeGetReward.ProtoReflect.Descriptor instead.
func (*CLTotalRechargeGetReward) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{127}
}

func (x *CLTotalRechargeGetReward) GetTableId() int32 {
	if x != nil {
		return x.TableId
	}
	return 0
}

//=============日进斗金结束=============================
//============好友邀请开始=============================
//邀请任务列表
type CLInviteTaskList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLInviteTaskList) Reset() {
	*x = CLInviteTaskList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[128]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLInviteTaskList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLInviteTaskList) ProtoMessage() {}

func (x *CLInviteTaskList) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[128]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLInviteTaskList.ProtoReflect.Descriptor instead.
func (*CLInviteTaskList) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{128}
}

//邀请分享
type CLInviteTaskShare struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` // 平台
}

func (x *CLInviteTaskShare) Reset() {
	*x = CLInviteTaskShare{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[129]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLInviteTaskShare) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLInviteTaskShare) ProtoMessage() {}

func (x *CLInviteTaskShare) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[129]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLInviteTaskShare.ProtoReflect.Descriptor instead.
func (*CLInviteTaskShare) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{129}
}

func (x *CLInviteTaskShare) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

//领取邀请奖励
type CLInviteTaskGetReward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` // 任务id
}

func (x *CLInviteTaskGetReward) Reset() {
	*x = CLInviteTaskGetReward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[130]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLInviteTaskGetReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLInviteTaskGetReward) ProtoMessage() {}

func (x *CLInviteTaskGetReward) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[130]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLInviteTaskGetReward.ProtoReflect.Descriptor instead.
func (*CLInviteTaskGetReward) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{130}
}

func (x *CLInviteTaskGetReward) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

//竞技场获取数据
type CLArenaGetData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLArenaGetData) Reset() {
	*x = CLArenaGetData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[131]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLArenaGetData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLArenaGetData) ProtoMessage() {}

func (x *CLArenaGetData) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[131]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLArenaGetData.ProtoReflect.Descriptor instead.
func (*CLArenaGetData) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{131}
}

//竞技场请求挑战对手
type CLArenaReqChallenge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RivalIdx int32 `protobuf:"varint,1,opt,name=rivalIdx,proto3" json:"rivalIdx,omitempty"` // 对手索引 1~3
}

func (x *CLArenaReqChallenge) Reset() {
	*x = CLArenaReqChallenge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[132]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLArenaReqChallenge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLArenaReqChallenge) ProtoMessage() {}

func (x *CLArenaReqChallenge) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[132]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLArenaReqChallenge.ProtoReflect.Descriptor instead.
func (*CLArenaReqChallenge) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{132}
}

func (x *CLArenaReqChallenge) GetRivalIdx() int32 {
	if x != nil {
		return x.RivalIdx
	}
	return 0
}

//=================== 体力开始 ========================
// 请求打开体力界面
type CLOpenPowerBuyingReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLOpenPowerBuyingReq) Reset() {
	*x = CLOpenPowerBuyingReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[133]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLOpenPowerBuyingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLOpenPowerBuyingReq) ProtoMessage() {}

func (x *CLOpenPowerBuyingReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[133]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLOpenPowerBuyingReq.ProtoReflect.Descriptor instead.
func (*CLOpenPowerBuyingReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{133}
}

// 请求购买体力
type CLPowerBuyingReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BuyType int32 `protobuf:"varint,1,opt,name=buyType,proto3" json:"buyType,omitempty"` // 购买类型：普通、补买和看视频
	BuyNum  int32 `protobuf:"varint,2,opt,name=buyNum,proto3" json:"buyNum,omitempty"`   // 购买量
}

func (x *CLPowerBuyingReq) Reset() {
	*x = CLPowerBuyingReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[134]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLPowerBuyingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLPowerBuyingReq) ProtoMessage() {}

func (x *CLPowerBuyingReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[134]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLPowerBuyingReq.ProtoReflect.Descriptor instead.
func (*CLPowerBuyingReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{134}
}

func (x *CLPowerBuyingReq) GetBuyType() int32 {
	if x != nil {
		return x.BuyType
	}
	return 0
}

func (x *CLPowerBuyingReq) GetBuyNum() int32 {
	if x != nil {
		return x.BuyNum
	}
	return 0
}

// 请求打开食堂餐食界面 空字段
type CLPowerRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLPowerRewardReq) Reset() {
	*x = CLPowerRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[135]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLPowerRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLPowerRewardReq) ProtoMessage() {}

func (x *CLPowerRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[135]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLPowerRewardReq.ProtoReflect.Descriptor instead.
func (*CLPowerRewardReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{135}
}

// 请求一键领取食堂餐食 空字段
type CLAllPowerRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLAllPowerRewardReq) Reset() {
	*x = CLAllPowerRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[136]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLAllPowerRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLAllPowerRewardReq) ProtoMessage() {}

func (x *CLAllPowerRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[136]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLAllPowerRewardReq.ProtoReflect.Descriptor instead.
func (*CLAllPowerRewardReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{136}
}

// 请求领取指定体力包
type CLOnePowerRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GenerateTime int64 `protobuf:"varint,1,opt,name=generateTime,proto3" json:"generateTime,omitempty"` // 体力包生成时间
}

func (x *CLOnePowerRewardReq) Reset() {
	*x = CLOnePowerRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[137]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLOnePowerRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLOnePowerRewardReq) ProtoMessage() {}

func (x *CLOnePowerRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[137]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLOnePowerRewardReq.ProtoReflect.Descriptor instead.
func (*CLOnePowerRewardReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{137}
}

func (x *CLOnePowerRewardReq) GetGenerateTime() int64 {
	if x != nil {
		return x.GenerateTime
	}
	return 0
}

//=================== 天道修为开始 ========================
// 请求当前信息
type CLHeavenlyDaoInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLHeavenlyDaoInfoReq) Reset() {
	*x = CLHeavenlyDaoInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[138]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLHeavenlyDaoInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLHeavenlyDaoInfoReq) ProtoMessage() {}

func (x *CLHeavenlyDaoInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[138]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLHeavenlyDaoInfoReq.ProtoReflect.Descriptor instead.
func (*CLHeavenlyDaoInfoReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{138}
}

// 请求晋升
type CLHeavenlyDaoPromoteReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLHeavenlyDaoPromoteReq) Reset() {
	*x = CLHeavenlyDaoPromoteReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[139]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLHeavenlyDaoPromoteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLHeavenlyDaoPromoteReq) ProtoMessage() {}

func (x *CLHeavenlyDaoPromoteReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[139]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLHeavenlyDaoPromoteReq.ProtoReflect.Descriptor instead.
func (*CLHeavenlyDaoPromoteReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{139}
}

//周卡领取
type CLWeekCardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GiftId int32 `protobuf:"varint,1,opt,name=giftId,proto3" json:"giftId,omitempty"` //礼物ID
	Index  int32 `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`   //礼物Index
}

func (x *CLWeekCardReq) Reset() {
	*x = CLWeekCardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[140]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLWeekCardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLWeekCardReq) ProtoMessage() {}

func (x *CLWeekCardReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[140]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLWeekCardReq.ProtoReflect.Descriptor instead.
func (*CLWeekCardReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{140}
}

func (x *CLWeekCardReq) GetGiftId() int32 {
	if x != nil {
		return x.GiftId
	}
	return 0
}

func (x *CLWeekCardReq) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

//	=========================== 挂机奖励开始 ===========================
// 请求打开挂机奖励页面
type CLOpenHookReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLOpenHookReq) Reset() {
	*x = CLOpenHookReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[141]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLOpenHookReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLOpenHookReq) ProtoMessage() {}

func (x *CLOpenHookReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[141]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLOpenHookReq.ProtoReflect.Descriptor instead.
func (*CLOpenHookReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{141}
}

// 点击领取发送领取奖励请求
type CLGetHookRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGetHookRewardReq) Reset() {
	*x = CLGetHookRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[142]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGetHookRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGetHookRewardReq) ProtoMessage() {}

func (x *CLGetHookRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[142]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGetHookRewardReq.ProtoReflect.Descriptor instead.
func (*CLGetHookRewardReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{142}
}

// 点击领取后发送领取额外奖励请求
type CLGetHookExtraRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLGetHookExtraRewardReq) Reset() {
	*x = CLGetHookExtraRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[143]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLGetHookExtraRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLGetHookExtraRewardReq) ProtoMessage() {}

func (x *CLGetHookExtraRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[143]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLGetHookExtraRewardReq.ProtoReflect.Descriptor instead.
func (*CLGetHookExtraRewardReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{143}
}

//	=========================== 挂机奖励结束 ===========================
//=================== 举报开始 ========================
// 举报信息
type CLTipOffReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId      int64  `protobuf:"varint,1,opt,name=playerId,proto3" json:"playerId,omitempty"`          // 被举报者的玩家ID
	TipOffType    int32  `protobuf:"varint,2,opt,name=TipOffType,proto3" json:"TipOffType,omitempty"`      // 举报类型
	TipOffContent string `protobuf:"bytes,3,opt,name=TipOffContent,proto3" json:"TipOffContent,omitempty"` // 举报说明
}

func (x *CLTipOffReq) Reset() {
	*x = CLTipOffReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[144]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLTipOffReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLTipOffReq) ProtoMessage() {}

func (x *CLTipOffReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[144]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLTipOffReq.ProtoReflect.Descriptor instead.
func (*CLTipOffReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{144}
}

func (x *CLTipOffReq) GetPlayerId() int64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *CLTipOffReq) GetTipOffType() int32 {
	if x != nil {
		return x.TipOffType
	}
	return 0
}

func (x *CLTipOffReq) GetTipOffContent() string {
	if x != nil {
		return x.TipOffContent
	}
	return ""
}

//=================== 举报结束 ========================
//=================== 好友开始 ========================
//赠送礼物
type CLSendSingleGiftReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GiftRecipientPlayerId int64 `protobuf:"varint,1,opt,name=giftRecipientPlayerId,proto3" json:"giftRecipientPlayerId,omitempty"` //礼物接收者id
}

func (x *CLSendSingleGiftReq) Reset() {
	*x = CLSendSingleGiftReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[145]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLSendSingleGiftReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLSendSingleGiftReq) ProtoMessage() {}

func (x *CLSendSingleGiftReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[145]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLSendSingleGiftReq.ProtoReflect.Descriptor instead.
func (*CLSendSingleGiftReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{145}
}

func (x *CLSendSingleGiftReq) GetGiftRecipientPlayerId() int64 {
	if x != nil {
		return x.GiftRecipientPlayerId
	}
	return 0
}

//领取单个礼物
type CLReciveSingleGiftReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UidTime string `protobuf:"bytes,1,opt,name=uidTime,proto3" json:"uidTime,omitempty"` //赠送者的id+下划线+赠送时间
}

func (x *CLReciveSingleGiftReq) Reset() {
	*x = CLReciveSingleGiftReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[146]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLReciveSingleGiftReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLReciveSingleGiftReq) ProtoMessage() {}

func (x *CLReciveSingleGiftReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[146]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLReciveSingleGiftReq.ProtoReflect.Descriptor instead.
func (*CLReciveSingleGiftReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{146}
}

func (x *CLReciveSingleGiftReq) GetUidTime() string {
	if x != nil {
		return x.UidTime
	}
	return ""
}

//一键赠送
type CLSendAllGiftReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uids []int64 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"` //当前好友的id列表
}

func (x *CLSendAllGiftReq) Reset() {
	*x = CLSendAllGiftReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[147]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLSendAllGiftReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLSendAllGiftReq) ProtoMessage() {}

func (x *CLSendAllGiftReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[147]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLSendAllGiftReq.ProtoReflect.Descriptor instead.
func (*CLSendAllGiftReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{147}
}

func (x *CLSendAllGiftReq) GetUids() []int64 {
	if x != nil {
		return x.Uids
	}
	return nil
}

//一键领取
type CLReciveAllGiftReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uids []int64 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"` //当前好友的id列表
}

func (x *CLReciveAllGiftReq) Reset() {
	*x = CLReciveAllGiftReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[148]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLReciveAllGiftReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLReciveAllGiftReq) ProtoMessage() {}

func (x *CLReciveAllGiftReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[148]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLReciveAllGiftReq.ProtoReflect.Descriptor instead.
func (*CLReciveAllGiftReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{148}
}

func (x *CLReciveAllGiftReq) GetUids() []int64 {
	if x != nil {
		return x.Uids
	}
	return nil
}

//=================== 英雄开始 ========================
//英雄列表
type CLHeroListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLHeroListReq) Reset() {
	*x = CLHeroListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[149]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLHeroListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLHeroListReq) ProtoMessage() {}

func (x *CLHeroListReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[149]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLHeroListReq.ProtoReflect.Descriptor instead.
func (*CLHeroListReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{149}
}

//英雄升级
type CLHeroLevelUpReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeroId int32 `protobuf:"varint,1,opt,name=heroId,proto3" json:"heroId,omitempty"` // 英雄id
}

func (x *CLHeroLevelUpReq) Reset() {
	*x = CLHeroLevelUpReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[150]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLHeroLevelUpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLHeroLevelUpReq) ProtoMessage() {}

func (x *CLHeroLevelUpReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[150]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLHeroLevelUpReq.ProtoReflect.Descriptor instead.
func (*CLHeroLevelUpReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{150}
}

func (x *CLHeroLevelUpReq) GetHeroId() int32 {
	if x != nil {
		return x.HeroId
	}
	return 0
}

//英雄觉醒等级升级
type CLHeroAwakeLevelUpReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeroId int32 `protobuf:"varint,1,opt,name=heroId,proto3" json:"heroId,omitempty"` // 英雄id
}

func (x *CLHeroAwakeLevelUpReq) Reset() {
	*x = CLHeroAwakeLevelUpReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[151]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLHeroAwakeLevelUpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLHeroAwakeLevelUpReq) ProtoMessage() {}

func (x *CLHeroAwakeLevelUpReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[151]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLHeroAwakeLevelUpReq.ProtoReflect.Descriptor instead.
func (*CLHeroAwakeLevelUpReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{151}
}

func (x *CLHeroAwakeLevelUpReq) GetHeroId() int32 {
	if x != nil {
		return x.HeroId
	}
	return 0
}

//=================== 阵容开始 ========================
//获取所有阵容信息
type CLLineupListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLLineupListReq) Reset() {
	*x = CLLineupListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[152]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLLineupListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLLineupListReq) ProtoMessage() {}

func (x *CLLineupListReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[152]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLLineupListReq.ProtoReflect.Descriptor instead.
func (*CLLineupListReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{152}
}

//阵容槽位解锁
type CLLineupUnlockSlot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` // 槽位id
}

func (x *CLLineupUnlockSlot) Reset() {
	*x = CLLineupUnlockSlot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[153]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLLineupUnlockSlot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLLineupUnlockSlot) ProtoMessage() {}

func (x *CLLineupUnlockSlot) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[153]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLLineupUnlockSlot.ProtoReflect.Descriptor instead.
func (*CLLineupUnlockSlot) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{153}
}

func (x *CLLineupUnlockSlot) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

//切换阵容
type CLLineupSwitchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` // 槽位id
}

func (x *CLLineupSwitchReq) Reset() {
	*x = CLLineupSwitchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[154]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLLineupSwitchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLLineupSwitchReq) ProtoMessage() {}

func (x *CLLineupSwitchReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[154]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLLineupSwitchReq.ProtoReflect.Descriptor instead.
func (*CLLineupSwitchReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{154}
}

func (x *CLLineupSwitchReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

//英雄上阵
type CLLineupSetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeroId        int32 `protobuf:"varint,1,opt,name=heroId,proto3" json:"heroId,omitempty"`               // 上阵英雄id
	ReplaceHeroId int32 `protobuf:"varint,2,opt,name=replaceHeroId,proto3" json:"replaceHeroId,omitempty"` // 被替换的英雄id
}

func (x *CLLineupSetReq) Reset() {
	*x = CLLineupSetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[155]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLLineupSetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLLineupSetReq) ProtoMessage() {}

func (x *CLLineupSetReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[155]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLLineupSetReq.ProtoReflect.Descriptor instead.
func (*CLLineupSetReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{155}
}

func (x *CLLineupSetReq) GetHeroId() int32 {
	if x != nil {
		return x.HeroId
	}
	return 0
}

func (x *CLLineupSetReq) GetReplaceHeroId() int32 {
	if x != nil {
		return x.ReplaceHeroId
	}
	return 0
}

//阵容重命名
type CLLineupRenameReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                         // 槽位id
	NewName string `protobuf:"bytes,2,opt,name=new_name,json=newName,proto3" json:"new_name,omitempty"` // 新的阵容名称
}

func (x *CLLineupRenameReq) Reset() {
	*x = CLLineupRenameReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[156]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLLineupRenameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLLineupRenameReq) ProtoMessage() {}

func (x *CLLineupRenameReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[156]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLLineupRenameReq.ProtoReflect.Descriptor instead.
func (*CLLineupRenameReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{156}
}

func (x *CLLineupRenameReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CLLineupRenameReq) GetNewName() string {
	if x != nil {
		return x.NewName
	}
	return ""
}

//=================== 赛季buff开始 ========================
//赛季buff信息
type CLSeasonBuffReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLSeasonBuffReq) Reset() {
	*x = CLSeasonBuffReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[157]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLSeasonBuffReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLSeasonBuffReq) ProtoMessage() {}

func (x *CLSeasonBuffReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[157]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLSeasonBuffReq.ProtoReflect.Descriptor instead.
func (*CLSeasonBuffReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{157}
}

//=================== 战斗开始 ========================
//开始匹配
type CLMatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLMatchReq) Reset() {
	*x = CLMatchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[158]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLMatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLMatchReq) ProtoMessage() {}

func (x *CLMatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[158]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLMatchReq.ProtoReflect.Descriptor instead.
func (*CLMatchReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{158}
}

//请求开始回合战斗
type CLRoundBattleStartReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLRoundBattleStartReq) Reset() {
	*x = CLRoundBattleStartReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[159]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLRoundBattleStartReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLRoundBattleStartReq) ProtoMessage() {}

func (x *CLRoundBattleStartReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[159]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLRoundBattleStartReq.ProtoReflect.Descriptor instead.
func (*CLRoundBattleStartReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{159}
}

//buffer 选择
type CLSelectBufferReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BufferId int32 `protobuf:"varint,1,opt,name=bufferId,proto3" json:"bufferId,omitempty"`
}

func (x *CLSelectBufferReq) Reset() {
	*x = CLSelectBufferReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[160]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLSelectBufferReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLSelectBufferReq) ProtoMessage() {}

func (x *CLSelectBufferReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[160]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLSelectBufferReq.ProtoReflect.Descriptor instead.
func (*CLSelectBufferReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{160}
}

func (x *CLSelectBufferReq) GetBufferId() int32 {
	if x != nil {
		return x.BufferId
	}
	return 0
}

//进入战场
type CLEnterSceneReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SceneId int32 `protobuf:"varint,1,opt,name=sceneId,proto3" json:"sceneId,omitempty"` //战场ID
}

func (x *CLEnterSceneReq) Reset() {
	*x = CLEnterSceneReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[161]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLEnterSceneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLEnterSceneReq) ProtoMessage() {}

func (x *CLEnterSceneReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[161]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLEnterSceneReq.ProtoReflect.Descriptor instead.
func (*CLEnterSceneReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{161}
}

func (x *CLEnterSceneReq) GetSceneId() int32 {
	if x != nil {
		return x.SceneId
	}
	return 0
}

//英雄合成
type CLMergeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	From  int32                     `protobuf:"varint,1,opt,name=from,proto3" json:"from,omitempty"`  //合成源格子ID
	To    int32                     `protobuf:"varint,2,opt,name=to,proto3" json:"to,omitempty"`      //合成目标格子ID
	Moves []*public.PBMoveOperation `protobuf:"bytes,3,rep,name=moves,proto3" json:"moves,omitempty"` // 本次合成前发生的所有移动操作
}

func (x *CLMergeReq) Reset() {
	*x = CLMergeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[162]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLMergeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLMergeReq) ProtoMessage() {}

func (x *CLMergeReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[162]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLMergeReq.ProtoReflect.Descriptor instead.
func (*CLMergeReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{162}
}

func (x *CLMergeReq) GetFrom() int32 {
	if x != nil {
		return x.From
	}
	return 0
}

func (x *CLMergeReq) GetTo() int32 {
	if x != nil {
		return x.To
	}
	return 0
}

func (x *CLMergeReq) GetMoves() []*public.PBMoveOperation {
	if x != nil {
		return x.Moves
	}
	return nil
}

//准备
type CLReadyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Moves []*public.PBMoveOperation `protobuf:"bytes,3,rep,name=moves,proto3" json:"moves,omitempty"` // 确认准备前要同步的移动操作
}

func (x *CLReadyReq) Reset() {
	*x = CLReadyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[163]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLReadyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLReadyReq) ProtoMessage() {}

func (x *CLReadyReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[163]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLReadyReq.ProtoReflect.Descriptor instead.
func (*CLReadyReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{163}
}

func (x *CLReadyReq) GetMoves() []*public.PBMoveOperation {
	if x != nil {
		return x.Moves
	}
	return nil
}

//战斗结束
type CLRoundBattleEndReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Win   bool  `protobuf:"varint,1,opt,name=win,proto3" json:"win,omitempty"`     // 战斗胜利或者失败
	Kills int32 `protobuf:"varint,2,opt,name=kills,proto3" json:"kills,omitempty"` // 击杀数
}

func (x *CLRoundBattleEndReq) Reset() {
	*x = CLRoundBattleEndReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[164]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLRoundBattleEndReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLRoundBattleEndReq) ProtoMessage() {}

func (x *CLRoundBattleEndReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[164]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLRoundBattleEndReq.ProtoReflect.Descriptor instead.
func (*CLRoundBattleEndReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{164}
}

func (x *CLRoundBattleEndReq) GetWin() bool {
	if x != nil {
		return x.Win
	}
	return false
}

func (x *CLRoundBattleEndReq) GetKills() int32 {
	if x != nil {
		return x.Kills
	}
	return 0
}

//离开战斗
type CLLeaveBattleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLLeaveBattleReq) Reset() {
	*x = CLLeaveBattleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[165]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLLeaveBattleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLLeaveBattleReq) ProtoMessage() {}

func (x *CLLeaveBattleReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[165]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLLeaveBattleReq.ProtoReflect.Descriptor instead.
func (*CLLeaveBattleReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{165}
}

// 结算相关
// 结算额外广告奖励
type CLClaimAdRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type public.AdRewardType `protobuf:"varint,1,opt,name=type,proto3,enum=AdRewardType" json:"type,omitempty"` // BATTLE_SUPPLY_DROP/BATTLE_BLESSING
}

func (x *CLClaimAdRewardReq) Reset() {
	*x = CLClaimAdRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[166]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLClaimAdRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLClaimAdRewardReq) ProtoMessage() {}

func (x *CLClaimAdRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[166]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLClaimAdRewardReq.ProtoReflect.Descriptor instead.
func (*CLClaimAdRewardReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{166}
}

func (x *CLClaimAdRewardReq) GetType() public.AdRewardType {
	if x != nil {
		return x.Type
	}
	return public.AdRewardType_BATTLE_ADREWARD_NONE
}

//=================== 赛季开始 ========================
// 请求领取奖励
type CLClaimSeasonRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type public.RewardType `protobuf:"varint,1,opt,name=type,proto3,enum=RewardType" json:"type,omitempty"` // 奖励类型
	Id   int32             `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`                     // 奖励ID (这里对应 MainRank 的段位ID)
}

func (x *CLClaimSeasonRewardReq) Reset() {
	*x = CLClaimSeasonRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[167]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLClaimSeasonRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLClaimSeasonRewardReq) ProtoMessage() {}

func (x *CLClaimSeasonRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[167]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLClaimSeasonRewardReq.ProtoReflect.Descriptor instead.
func (*CLClaimSeasonRewardReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{167}
}

func (x *CLClaimSeasonRewardReq) GetType() public.RewardType {
	if x != nil {
		return x.Type
	}
	return public.RewardType_REWARD_TYPE_UNKNOWN
}

func (x *CLClaimSeasonRewardReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 请求赛季相关信息
type CLSeasonInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLSeasonInfoReq) Reset() {
	*x = CLSeasonInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[168]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLSeasonInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLSeasonInfoReq) ProtoMessage() {}

func (x *CLSeasonInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[168]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLSeasonInfoReq.ProtoReflect.Descriptor instead.
func (*CLSeasonInfoReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{168}
}

//=================== 宝物系统开始 ========================
// 请求宝物列表
type CLTreasureListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CLTreasureListReq) Reset() {
	*x = CLTreasureListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[169]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLTreasureListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLTreasureListReq) ProtoMessage() {}

func (x *CLTreasureListReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[169]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLTreasureListReq.ProtoReflect.Descriptor instead.
func (*CLTreasureListReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{169}
}

// 请求宝物升级
type CLTreasureLevelUpReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TreasureId int32 `protobuf:"varint,1,opt,name=treasureId,proto3" json:"treasureId,omitempty"` // 要升级的宝物ID
}

func (x *CLTreasureLevelUpReq) Reset() {
	*x = CLTreasureLevelUpReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[170]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLTreasureLevelUpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLTreasureLevelUpReq) ProtoMessage() {}

func (x *CLTreasureLevelUpReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[170]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLTreasureLevelUpReq.ProtoReflect.Descriptor instead.
func (*CLTreasureLevelUpReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{170}
}

func (x *CLTreasureLevelUpReq) GetTreasureId() int32 {
	if x != nil {
		return x.TreasureId
	}
	return 0
}

// 请求宝物升星
type CLTreasureStarUpReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TreasureId int32 `protobuf:"varint,1,opt,name=treasureId,proto3" json:"treasureId,omitempty"` // 要升星的宝物ID
}

func (x *CLTreasureStarUpReq) Reset() {
	*x = CLTreasureStarUpReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[171]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLTreasureStarUpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLTreasureStarUpReq) ProtoMessage() {}

func (x *CLTreasureStarUpReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[171]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLTreasureStarUpReq.ProtoReflect.Descriptor instead.
func (*CLTreasureStarUpReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{171}
}

func (x *CLTreasureStarUpReq) GetTreasureId() int32 {
	if x != nil {
		return x.TreasureId
	}
	return 0
}

// 请求抽取宝物
type CLTreasureGachaReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GachaId   int32                        `protobuf:"varint,1,opt,name=gachaId,proto3" json:"gachaId,omitempty"`                              // 卡池ID (根据 TreasureGacha 表)
	DrawCount int32                        `protobuf:"varint,2,opt,name=drawCount,proto3" json:"drawCount,omitempty"`                          // 抽取次数 (例如 1 或 10)
	CostType  public.TreasureGachaCostType `protobuf:"varint,3,opt,name=costType,proto3,enum=TreasureGachaCostType" json:"costType,omitempty"` // 本次抽卡的消耗类型
}

func (x *CLTreasureGachaReq) Reset() {
	*x = CLTreasureGachaReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_CLProtocol_proto_msgTypes[172]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLTreasureGachaReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLTreasureGachaReq) ProtoMessage() {}

func (x *CLTreasureGachaReq) ProtoReflect() protoreflect.Message {
	mi := &file_CLProtocol_proto_msgTypes[172]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLTreasureGachaReq.ProtoReflect.Descriptor instead.
func (*CLTreasureGachaReq) Descriptor() ([]byte, []int) {
	return file_CLProtocol_proto_rawDescGZIP(), []int{172}
}

func (x *CLTreasureGachaReq) GetGachaId() int32 {
	if x != nil {
		return x.GachaId
	}
	return 0
}

func (x *CLTreasureGachaReq) GetDrawCount() int32 {
	if x != nil {
		return x.DrawCount
	}
	return 0
}

func (x *CLTreasureGachaReq) GetCostType() public.TreasureGachaCostType {
	if x != nil {
		return x.CostType
	}
	return public.TreasureGachaCostType_COST_TYPE_NONE
}

var File_CLProtocol_proto protoreflect.FileDescriptor

var file_CLProtocol_proto_rawDesc = []byte{
	0x0a, 0x10, 0x43, 0x4c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x13, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x45,
	0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x89, 0x05, 0x0a, 0x0c, 0x43, 0x4c,
	0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x68, 0x65, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x68,
	0x65, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0e, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64,
	0x61, 0x79, 0x12, 0x2d, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x50, 0x42, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x64, 0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x64, 0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2b, 0x0a,
	0x09, 0x6d, 0x69, 0x64, 0x61, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x50, 0x42, 0x5f, 0x4d, 0x69, 0x64, 0x61, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x09, 0x6d, 0x69, 0x64, 0x61, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3e, 0x0a, 0x13, 0x67, 0x61,
	0x6d, 0x65, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x42,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x13, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64,
	0x12, 0x2d, 0x0a, 0x0a, 0x41, 0x53, 0x41, 0x49, 0x61, 0x64, 0x44, 0x61, 0x74, 0x61, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x50, 0x42, 0x41, 0x53, 0x41, 0x49, 0x61, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x0a, 0x41, 0x53, 0x41, 0x49, 0x61, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x1e, 0x0a, 0x0a, 0x73, 0x64, 0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x64, 0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x64, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x64, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x0e, 0x73, 0x64, 0x6b, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x64, 0x6b, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0x3b, 0x0a, 0x11, 0x43, 0x4c, 0x5f, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x5f, 0x52, 0x45, 0x51, 0x12, 0x26, 0x0a, 0x0e, 0x67, 0x75,
	0x69, 0x64, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0e, 0x67, 0x75, 0x69, 0x64, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x22, 0x41, 0x0a, 0x0b, 0x43, 0x4c, 0x48, 0x65, 0x61, 0x72, 0x74, 0x42, 0x65, 0x61,
	0x74, 0x12, 0x32, 0x0a, 0x14, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x6e, 0x74, 0x4c, 0x61,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x14, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x6e, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x14, 0x0a, 0x12, 0x43, 0x4c, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x23, 0x0a, 0x07, 0x43,
	0x4c, 0x47, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x22, 0x18, 0x0a, 0x16, 0x43, 0x4c, 0x5f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x5f, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x4f, 0x75, 0x74, 0x22, 0x39, 0x0a, 0x0c, 0x43, 0x4c,
	0x55, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x29, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x55,
	0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x08, 0x75, 0x73, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x22, 0x76, 0x0a, 0x12, 0x43, 0x4c, 0x46, 0x75, 0x6e, 0x74, 0x69,
	0x6f, 0x6e, 0x55, 0x6e, 0x4c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x66,
	0x75, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x66, 0x75, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x69, 0x6e,
	0x67, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x74, 0x63, 0x69, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0e, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x74, 0x63, 0x69, 0x61,
	0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x72, 0x74, 0x63, 0x69, 0x61, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x50, 0x61, 0x72, 0x74, 0x63, 0x69, 0x61, 0x6c, 0x22, 0x12, 0x0a,
	0x10, 0x43, 0x4c, 0x4d, 0x61, 0x69, 0x6c, 0x41, 0x6c, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x22, 0x27, 0x0a, 0x0d, 0x43, 0x4c, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x22, 0x2a, 0x0a, 0x10, 0x43, 0x4c,
	0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x16,
	0x0a, 0x06, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x22, 0x15, 0x0a, 0x13, 0x43, 0x4c, 0x52, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x41, 0x6c, 0x6c, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x22, 0x26, 0x0a,
	0x0c, 0x43, 0x4c, 0x44, 0x65, 0x6c, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a,
	0x06, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6d,
	0x61, 0x69, 0x6c, 0x49, 0x64, 0x22, 0x15, 0x0a, 0x13, 0x43, 0x4c, 0x44, 0x65, 0x6c, 0x41, 0x6c,
	0x6c, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x22, 0x18, 0x0a, 0x16,
	0x43, 0x4c, 0x4d, 0x61, 0x69, 0x6c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x77,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x22, 0x39, 0x0a, 0x0f, 0x43, 0x4c, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x22, 0x33, 0x0a, 0x0f, 0x43, 0x4c, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x69, 0x67,
	0x6e, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x39, 0x0a, 0x11, 0x43, 0x4c, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x06, 0x67,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x45, 0x47,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x22, 0x5c, 0x0a, 0x15, 0x43, 0x4c, 0x4e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x64, 0x65, 0x53,
	0x74, 0x65, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x43, 0x0a, 0x13, 0x63, 0x75,
	0x72, 0x4e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x64, 0x65, 0x53, 0x65, 0x74, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x50, 0x42, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x13, 0x63, 0x75, 0x72, 0x4e,
	0x65, 0x77, 0x47, 0x75, 0x69, 0x64, 0x65, 0x53, 0x65, 0x74, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x63, 0x0a, 0x18, 0x43, 0x4c, 0x4e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x64, 0x65, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x12, 0x47, 0x0a, 0x15, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x4e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x64, 0x65, 0x53, 0x65, 0x74, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x50, 0x42, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x15, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x4e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x64, 0x65, 0x53, 0x65, 0x74, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0x33, 0x0a, 0x17, 0x43, 0x4c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x18, 0x0a, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x22, 0x38, 0x0a, 0x14, 0x43, 0x4c, 0x47,
	0x75, 0x69, 0x64, 0x65, 0x53, 0x74, 0x65, 0x70, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x52, 0x65,
	0x71, 0x12, 0x20, 0x0a, 0x05, 0x67, 0x75, 0x69, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0a, 0x2e, 0x50, 0x42, 0x49, 0x6e, 0x74, 0x50, 0x61, 0x69, 0x72, 0x52, 0x05, 0x67, 0x75,
	0x69, 0x64, 0x65, 0x22, 0x31, 0x0a, 0x15, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x64, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x22, 0x44, 0x0a, 0x13, 0x43, 0x4c, 0x53, 0x69, 0x67, 0x6e,
	0x49, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a,
	0x0a, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x0d, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x54, 0x6f, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x5e, 0x0a, 0x15,
	0x43, 0x4c, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x0a, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x53, 0x69, 0x67, 0x6e,
	0x49, 0x6e, 0x54, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x61, 0x79, 0x4e, 0x75, 0x6d, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x44, 0x61, 0x79, 0x4e, 0x75, 0x6d, 0x22, 0x1f, 0x0a, 0x1d,
	0x43, 0x4c, 0x53, 0x65, 0x76, 0x65, 0x6e, 0x44, 0x61, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x1a, 0x0a,
	0x18, 0x43, 0x4c, 0x53, 0x69, 0x67, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x36, 0x0a, 0x10, 0x43, 0x4c, 0x53,
	0x79, 0x6e, 0x63, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x50, 0x42,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x1a, 0x0a, 0x18, 0x43, 0x4c, 0x41, 0x6c, 0x6c, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x22, 0x32, 0x0a,
	0x16, 0x43, 0x4c, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x52, 0x61, 0x73, 0x69, 0x6e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x49,
	0x44, 0x22, 0x3c, 0x0a, 0x1c, 0x43, 0x4c, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x47, 0x72, 0x6f,
	0x77, 0x74, 0x68, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x55, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x55, 0x49, 0x44, 0x22,
	0x5d, 0x0a, 0x23, 0x43, 0x4c, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x47, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x55, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x55, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x44, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x44, 0x22, 0x46,
	0x0a, 0x26, 0x43, 0x4c, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x47, 0x72, 0x6f, 0x77, 0x74, 0x68,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x41, 0x6c, 0x6c, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x55, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x55, 0x49, 0x44, 0x22, 0x36, 0x0a, 0x10, 0x43, 0x4c, 0x53, 0x68, 0x61, 0x72,
	0x65, 0x50, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x75,
	0x72, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x55, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x63, 0x75, 0x72, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x55, 0x49, 0x44, 0x22, 0x42,
	0x0a, 0x18, 0x43, 0x4c, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x12, 0x26, 0x0a, 0x0e, 0x69, 0x6e,
	0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x12, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x22, 0x4e, 0x0a, 0x18, 0x43, 0x4c, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x42, 0x6f, 0x78, 0x44, 0x61, 0x74, 0x61, 0x12, 0x15,
	0x0a, 0x06, 0x62, 0x6f, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x62, 0x6f, 0x78, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x78, 0x5f, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x62, 0x6f, 0x78, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x22, 0x0c, 0x0a, 0x0a, 0x43, 0x4c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x22, 0x31, 0x0a, 0x15, 0x43, 0x4c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x41, 0x77, 0x61, 0x72, 0x64,
	0x54, 0x6f, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x22, 0x39, 0x0a, 0x11, 0x43, 0x4c, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4f,
	0x74, 0x68, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x24, 0x0a, 0x0d, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x22, 0xaf,
	0x01, 0x0a, 0x0d, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x69, 0x63, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x63,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x72, 0x65, 0x65, 0x4a, 0x6f, 0x69, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x66, 0x72, 0x65, 0x65, 0x4a, 0x6f, 0x69, 0x6e,
	0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x53, 0x74, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x72, 0x65, 0x71, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x22, 0x0d, 0x0a, 0x0b, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4d, 0x61, 0x69, 0x6e, 0x22,
	0x11, 0x0a, 0x0f, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x46, 0x61, 0x73, 0x74, 0x4a, 0x6f,
	0x69, 0x6e, 0x22, 0x23, 0x0a, 0x0d, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x22, 0x1e, 0x0a, 0x0c, 0x43, 0x4c, 0x47, 0x75, 0x69,
	0x6c, 0x64, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x0d, 0x0a, 0x0b, 0x43, 0x4c, 0x47, 0x75, 0x69,
	0x6c, 0x64, 0x48, 0x61, 0x6c, 0x6c, 0x22, 0x0d, 0x0a, 0x0b, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x54, 0x65, 0x63, 0x68, 0x22, 0x24, 0x0a, 0x12, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x54, 0x65, 0x63, 0x68, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x75, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x12, 0x0a, 0x10, 0x43,
	0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x54, 0x65, 0x63, 0x68, 0x52, 0x65, 0x73, 0x65, 0x74, 0x22,
	0x0d, 0x0a, 0x0b, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x68, 0x6f, 0x70, 0x22, 0x4a,
	0x0a, 0x0e, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x68, 0x6f, 0x70, 0x42, 0x75, 0x79,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xca, 0x01, 0x0a, 0x0b, 0x43,
	0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x45, 0x64, 0x69, 0x74, 0x12, 0x1b, 0x0a, 0x03, 0x6f, 0x70,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f,
	0x70, 0x74, 0x52, 0x03, 0x6f, 0x70, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e,
	0x6f, 0x74, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x74,
	0x69, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x63, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x63, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x66,
	0x72, 0x65, 0x65, 0x4a, 0x6f, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x66,
	0x72, 0x65, 0x65, 0x4a, 0x6f, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x65, 0x71, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75,
	0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x15, 0x0a, 0x13, 0x43, 0x4c, 0x47, 0x75, 0x69,
	0x6c, 0x64, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4d, 0x67, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x16,
	0x0a, 0x14, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4d,
	0x67, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x4e, 0x0a, 0x0f, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4d, 0x67, 0x72, 0x12, 0x1b, 0x0a, 0x03, 0x6f, 0x70, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70,
	0x74, 0x52, 0x03, 0x6f, 0x70, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x49, 0x44, 0x22, 0x4f, 0x0a, 0x10, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4d, 0x67, 0x72, 0x12, 0x1b, 0x0a, 0x03, 0x6f, 0x70,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f,
	0x70, 0x74, 0x52, 0x03, 0x6f, 0x70, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x44, 0x22, 0x0d, 0x0a, 0x0b, 0x43, 0x4c, 0x47, 0x75, 0x69,
	0x6c, 0x64, 0x51, 0x75, 0x69, 0x74, 0x22, 0x10, 0x0a, 0x0e, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x44, 0x69, 0x73, 0x6d, 0x69, 0x73, 0x73, 0x22, 0x10, 0x0a, 0x0e, 0x43, 0x4c, 0x47, 0x75,
	0x69, 0x6c, 0x64, 0x49, 0x6d, 0x70, 0x65, 0x61, 0x63, 0x68, 0x22, 0x2c, 0x0a, 0x16, 0x43, 0x4c,
	0x47, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x65, 0x6e, 0x64, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x49, 0x6e,
	0x76, 0x69, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x22, 0x4d, 0x0a, 0x17, 0x43, 0x4c, 0x47, 0x75,
	0x69, 0x6c, 0x64, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x22, 0x23, 0x0a, 0x11, 0x43, 0x4c, 0x47, 0x75, 0x69,
	0x6c, 0x64, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4a, 0x6f, 0x69, 0x6e, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x21, 0x0a, 0x0d,
	0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x44, 0x6f, 0x6e, 0x61, 0x74, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6f, 0x70, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6f, 0x70, 0x74, 0x22,
	0x0d, 0x0a, 0x0b, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x61, 0x6e, 0x6b, 0x22, 0x0c,
	0x0a, 0x0a, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x6f, 0x67, 0x22, 0x27, 0x0a, 0x15,
	0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x42, 0x61, 0x72, 0x67, 0x61, 0x69, 0x6e, 0x69, 0x6e,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x29, 0x0a, 0x17, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x42, 0x61, 0x72, 0x67, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x23, 0x0a, 0x11, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x42, 0x61, 0x72, 0x67, 0x61,
	0x69, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x26, 0x0a, 0x14, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x42, 0x61, 0x72, 0x67, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x42, 0x75, 0x79, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x12, 0x0a,
	0x10, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x42, 0x6f, 0x73, 0x73, 0x45, 0x6e, 0x74, 0x65,
	0x72, 0x22, 0x15, 0x0a, 0x13, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x42, 0x6f, 0x73, 0x73,
	0x41, 0x74, 0x6b, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x22, 0x12, 0x0a, 0x10, 0x43, 0x4c, 0x47, 0x75,
	0x69, 0x6c, 0x64, 0x42, 0x6f, 0x73, 0x73, 0x53, 0x77, 0x65, 0x65, 0x70, 0x22, 0x31, 0x0a, 0x13,
	0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x42, 0x6f, 0x73, 0x73, 0x42, 0x75, 0x79, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x75, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0x11, 0x0a, 0x0f, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x42, 0x6f, 0x73, 0x73, 0x52, 0x61,
	0x6e, 0x6b, 0x22, 0x6d, 0x0a, 0x0e, 0x43, 0x4c, 0x53, 0x65, 0x6e, 0x64, 0x43, 0x68, 0x61, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x25, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x74, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x08, 0x63, 0x68, 0x61, 0x74, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x22, 0x2a, 0x0a, 0x0c, 0x43, 0x4c, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x22, 0x4c, 0x0a,
	0x0d, 0x43, 0x4c, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x4d, 0x73, 0x67, 0x47, 0x65, 0x74, 0x12, 0x25,
	0x0a, 0x08, 0x63, 0x68, 0x61, 0x74, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x09, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x63, 0x68, 0x61,
	0x74, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x22, 0x38, 0x0a, 0x14, 0x43,
	0x4c, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x22, 0x15, 0x0a, 0x13, 0x43, 0x4c, 0x53, 0x79, 0x6e, 0x63, 0x41,
	0x6c, 0x6c, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x71, 0x22, 0x0e, 0x0a, 0x0c,
	0x43, 0x4c, 0x41, 0x6c, 0x6c, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x71, 0x22, 0x31, 0x0a, 0x11,
	0x43, 0x4c, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x65, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x52, 0x65,
	0x71, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x55, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x55, 0x69, 0x64, 0x22,
	0x30, 0x0a, 0x10, 0x43, 0x4c, 0x41, 0x67, 0x72, 0x65, 0x65, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x55, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x55, 0x69,
	0x64, 0x22, 0x31, 0x0a, 0x11, 0x43, 0x4c, 0x52, 0x65, 0x66, 0x75, 0x73, 0x65, 0x46, 0x72, 0x69,
	0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x55, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x55, 0x69, 0x64, 0x22, 0x31, 0x0a, 0x11, 0x43, 0x4c, 0x44, 0x65, 0x6c, 0x4f, 0x6e, 0x65,
	0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x55, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x55, 0x69, 0x64, 0x22, 0x49, 0x0a, 0x17, 0x43, 0x4c, 0x62, 0x6c, 0x61,
	0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x22, 0x26, 0x0a, 0x0a, 0x43, 0x4c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x35, 0x0a, 0x07, 0x43, 0x4c,
	0x42, 0x49, 0x4c, 0x6f, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x22, 0x2b, 0x0a, 0x11, 0x43, 0x4c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x52,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x64, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x41, 0x64, 0x74, 0x79, 0x70, 0x65, 0x22, 0x10,
	0x0a, 0x0e, 0x43, 0x4c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x54, 0x6f, 0x70, 0x4c, 0x69, 0x73, 0x74,
	0x22, 0x0f, 0x0a, 0x0d, 0x43, 0x4c, 0x48, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x22, 0x36, 0x0a, 0x14, 0x43, 0x4c, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x68, 0x65, 0x61,
	0x64, 0x49, 0x63, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x68,
	0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x32, 0x0a, 0x10, 0x43, 0x4c, 0x55,
	0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1e, 0x0a,
	0x0a, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x0d, 0x0a,
	0x0b, 0x43, 0x4c, 0x48, 0x65, 0x61, 0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x22, 0x36, 0x0a, 0x12,
	0x43, 0x4c, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x48, 0x65, 0x61, 0x64, 0x46, 0x72, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x68, 0x65, 0x61, 0x64, 0x46, 0x72, 0x61,
	0x6d, 0x65, 0x49, 0x64, 0x22, 0x35, 0x0a, 0x11, 0x43, 0x4c, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b,
	0x48, 0x65, 0x61, 0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x65, 0x61,
	0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x68, 0x65, 0x61, 0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x22, 0x23, 0x0a, 0x09, 0x43,
	0x4c, 0x47, 0x69, 0x66, 0x74, 0x42, 0x75, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x69, 0x66, 0x74,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64,
	0x22, 0x2f, 0x0a, 0x0d, 0x43, 0x4c, 0x54, 0x69, 0x6d, 0x65, 0x47, 0x69, 0x66, 0x74, 0x42, 0x75,
	0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x47, 0x69, 0x66, 0x74, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x47, 0x69, 0x66, 0x74, 0x49,
	0x64, 0x22, 0x16, 0x0a, 0x14, 0x43, 0x4c, 0x53, 0x65, 0x76, 0x65, 0x6e, 0x53, 0x69, 0x67, 0x6e,
	0x49, 0x6e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x22, 0x35, 0x0a, 0x15, 0x43, 0x4c, 0x53,
	0x65, 0x76, 0x65, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x47, 0x65, 0x74, 0x41, 0x77, 0x61,
	0x72, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x44, 0x61, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x44, 0x61, 0x79,
	0x22, 0x16, 0x0a, 0x14, 0x43, 0x4c, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x49,
	0x6e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x22, 0x35, 0x0a, 0x15, 0x43, 0x4c, 0x44, 0x61,
	0x69, 0x6c, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x47, 0x65, 0x74, 0x41, 0x77, 0x61, 0x72,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x44, 0x61, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x44, 0x61, 0x79, 0x22,
	0x2e, 0x0a, 0x1c, 0x43, 0x4c, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e,
	0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x72, 0x75, 0x65, 0x64, 0x41, 0x77, 0x61, 0x72, 0x64, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22,
	0x44, 0x0a, 0x16, 0x43, 0x4c, 0x46, 0x69, 0x72, 0x73, 0x74, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x69, 0x66,
	0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x79, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x64, 0x61, 0x79, 0x73, 0x22, 0x2e, 0x0a, 0x14, 0x43, 0x4c, 0x42, 0x75, 0x79, 0x46, 0x69,
	0x72, 0x73, 0x74, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x47, 0x69, 0x66, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67,
	0x69, 0x66, 0x74, 0x49, 0x64, 0x22, 0x16, 0x0a, 0x14, 0x43, 0x4c, 0x54, 0x6f, 0x70, 0x75, 0x70,
	0x52, 0x65, 0x62, 0x61, 0x74, 0x65, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x22, 0x39, 0x0a,
	0x15, 0x43, 0x4c, 0x54, 0x6f, 0x70, 0x75, 0x70, 0x52, 0x65, 0x62, 0x61, 0x74, 0x65, 0x47, 0x65,
	0x74, 0x41, 0x77, 0x61, 0x72, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x6f, 0x70, 0x75, 0x70, 0x54,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x6f, 0x70,
	0x75, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x16, 0x0a, 0x14, 0x43, 0x4c, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x6c, 0x79, 0x43, 0x61, 0x72, 0x64, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x22, 0x2e, 0x0a, 0x14, 0x43, 0x4c, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x43, 0x61, 0x72,
	0x64, 0x42, 0x75, 0x79, 0x43, 0x61, 0x72, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x61, 0x72, 0x64,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64,
	0x22, 0x19, 0x0a, 0x17, 0x43, 0x4c, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x43, 0x61, 0x72,
	0x64, 0x4e, 0x65, 0x77, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x22, 0x20, 0x0a, 0x1e, 0x43,
	0x4c, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x65, 0x77, 0x47,
	0x65, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x22, 0x15, 0x0a,
	0x13, 0x43, 0x4c, 0x47, 0x72, 0x61, 0x64, 0x65, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x47, 0x65, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x22, 0x3b, 0x0a, 0x13, 0x43, 0x4c, 0x47, 0x72, 0x61, 0x64, 0x65, 0x64,
	0x46, 0x75, 0x6e, 0x64, 0x42, 0x75, 0x79, 0x46, 0x75, 0x6e, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x78, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0d, 0x67, 0x72, 0x61, 0x64, 0x65, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x49, 0x64,
	0x78, 0x22, 0x3e, 0x0a, 0x16, 0x43, 0x4c, 0x47, 0x72, 0x61, 0x64, 0x65, 0x64, 0x46, 0x75, 0x6e,
	0x64, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x57, 0x65, 0x61, 0x6c, 0x12, 0x24, 0x0a, 0x0d, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x78, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0d, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64,
	0x78, 0x22, 0x40, 0x0a, 0x18, 0x43, 0x4c, 0x47, 0x72, 0x61, 0x64, 0x65, 0x64, 0x46, 0x75, 0x6e,
	0x64, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x65, 0x72, 0x57, 0x65, 0x61, 0x6c, 0x12, 0x24, 0x0a,
	0x0d, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x49, 0x64, 0x78, 0x22, 0x34, 0x0a, 0x0f, 0x43, 0x4c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x3d, 0x0a, 0x13, 0x43, 0x4c, 0x4d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x42, 0x79, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xc2, 0x01, 0x0a, 0x16, 0x43, 0x4c, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x28, 0x0a, 0x0f, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x67, 0x6f,
	0x6f, 0x64, 0x73, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a,
	0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x67,
	0x61, 0x6d, 0x65, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x67, 0x61, 0x6d, 0x65, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x22, 0x59, 0x0a,
	0x13, 0x43, 0x4c, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0b, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x22, 0x3c, 0x0a, 0x19, 0x43, 0x4c, 0x47, 0x65,
	0x74, 0x53, 0x65, 0x76, 0x65, 0x6e, 0x44, 0x61, 0x79, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x22, 0x57, 0x0a, 0x0f, 0x43, 0x4c, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x79, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x79, 0x69, 0x64, 0x22,
	0x3b, 0x0a, 0x15, 0x43, 0x4c, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x0c, 0x52, 0x65, 0x64, 0x65,
	0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x22, 0x15, 0x0a, 0x13,
	0x43, 0x4c, 0x47, 0x61, 0x63, 0x68, 0x61, 0x42, 0x6f, 0x6e, 0x75, 0x73, 0x47, 0x65, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x22, 0x2c, 0x0a, 0x14, 0x43, 0x4c, 0x47, 0x61, 0x63, 0x68, 0x61, 0x42, 0x6f,
	0x6e, 0x75, 0x73, 0x47, 0x65, 0x74, 0x41, 0x77, 0x61, 0x72, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x22, 0x2d, 0x0a, 0x13, 0x43, 0x4c, 0x47, 0x65, 0x74, 0x46, 0x75, 0x6e, 0x63, 0x50, 0x72,
	0x65, 0x76, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x46, 0x75, 0x6e, 0x63,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x46, 0x75, 0x6e, 0x63, 0x49, 0x64,
	0x22, 0x25, 0x0a, 0x13, 0x43, 0x4c, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x14, 0x0a, 0x12, 0x43, 0x4c, 0x47, 0x61, 0x63,
	0x68, 0x61, 0x57, 0x68, 0x65, 0x65, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x22, 0x0e, 0x0a,
	0x0c, 0x43, 0x4c, 0x5f, 0x54, 0x6f, 0x77, 0x65, 0x72, 0x4d, 0x61, 0x69, 0x6e, 0x22, 0x29, 0x0a,
	0x0d, 0x43, 0x4c, 0x5f, 0x54, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x73, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x34, 0x0a, 0x18, 0x43, 0x4c, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x12,
	0x0a, 0x10, 0x43, 0x4c, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0x23, 0x0a, 0x11, 0x43, 0x4c, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x54, 0x61,
	0x73, 0x6b, 0x53, 0x68, 0x61, 0x72, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x27, 0x0a, 0x15, 0x43, 0x4c, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x10, 0x0a, 0x0e, 0x43, 0x4c, 0x41, 0x72, 0x65, 0x6e, 0x61, 0x47, 0x65, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x22, 0x31, 0x0a, 0x13, 0x43, 0x4c, 0x41, 0x72, 0x65, 0x6e, 0x61, 0x52, 0x65, 0x71,
	0x43, 0x68, 0x61, 0x6c, 0x6c, 0x65, 0x6e, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x69, 0x76,
	0x61, 0x6c, 0x49, 0x64, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x69, 0x76,
	0x61, 0x6c, 0x49, 0x64, 0x78, 0x22, 0x16, 0x0a, 0x14, 0x43, 0x4c, 0x4f, 0x70, 0x65, 0x6e, 0x50,
	0x6f, 0x77, 0x65, 0x72, 0x42, 0x75, 0x79, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x22, 0x44, 0x0a,
	0x10, 0x43, 0x4c, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x42, 0x75, 0x79, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x75, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x62, 0x75, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62,
	0x75, 0x79, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x62, 0x75, 0x79,
	0x4e, 0x75, 0x6d, 0x22, 0x12, 0x0a, 0x10, 0x43, 0x4c, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x22, 0x15, 0x0a, 0x13, 0x43, 0x4c, 0x41, 0x6c, 0x6c,
	0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x22, 0x39,
	0x0a, 0x13, 0x43, 0x4c, 0x4f, 0x6e, 0x65, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x0c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x67, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x16, 0x0a, 0x14, 0x43, 0x4c, 0x48,
	0x65, 0x61, 0x76, 0x65, 0x6e, 0x6c, 0x79, 0x44, 0x61, 0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x22, 0x19, 0x0a, 0x17, 0x43, 0x4c, 0x48, 0x65, 0x61, 0x76, 0x65, 0x6e, 0x6c, 0x79, 0x44,
	0x61, 0x6f, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x71, 0x22, 0x3d, 0x0a, 0x0d,
	0x43, 0x4c, 0x57, 0x65, 0x65, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a,
	0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67,
	0x69, 0x66, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x0f, 0x0a, 0x0d, 0x43,
	0x4c, 0x4f, 0x70, 0x65, 0x6e, 0x48, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x22, 0x14, 0x0a, 0x12,
	0x43, 0x4c, 0x47, 0x65, 0x74, 0x48, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x22, 0x19, 0x0a, 0x17, 0x43, 0x4c, 0x47, 0x65, 0x74, 0x48, 0x6f, 0x6f, 0x6b, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x22, 0x6f, 0x0a,
	0x0b, 0x43, 0x4c, 0x54, 0x69, 0x70, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x54, 0x69, 0x70, 0x4f,
	0x66, 0x66, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x54, 0x69,
	0x70, 0x4f, 0x66, 0x66, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x54, 0x69, 0x70, 0x4f,
	0x66, 0x66, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x54, 0x69, 0x70, 0x4f, 0x66, 0x66, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x4b,
	0x0a, 0x13, 0x43, 0x4c, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x47, 0x69,
	0x66, 0x74, 0x52, 0x65, 0x71, 0x12, 0x34, 0x0a, 0x15, 0x67, 0x69, 0x66, 0x74, 0x52, 0x65, 0x63,
	0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x67, 0x69, 0x66, 0x74, 0x52, 0x65, 0x63, 0x69, 0x70, 0x69,
	0x65, 0x6e, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x22, 0x31, 0x0a, 0x15, 0x43,
	0x4c, 0x52, 0x65, 0x63, 0x69, 0x76, 0x65, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x47, 0x69, 0x66,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x69, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x69, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x26,
	0x0a, 0x10, 0x43, 0x4c, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x6c, 0x6c, 0x47, 0x69, 0x66, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x04, 0x75, 0x69, 0x64, 0x73, 0x22, 0x28, 0x0a, 0x12, 0x43, 0x4c, 0x52, 0x65, 0x63, 0x69,
	0x76, 0x65, 0x41, 0x6c, 0x6c, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04,
	0x75, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x04, 0x75, 0x69, 0x64, 0x73,
	0x22, 0x0f, 0x0a, 0x0d, 0x43, 0x4c, 0x48, 0x65, 0x72, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x22, 0x2a, 0x0a, 0x10, 0x43, 0x4c, 0x48, 0x65, 0x72, 0x6f, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x55, 0x70, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x72, 0x6f, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x72, 0x6f, 0x49, 0x64, 0x22, 0x2f, 0x0a,
	0x15, 0x43, 0x4c, 0x48, 0x65, 0x72, 0x6f, 0x41, 0x77, 0x61, 0x6b, 0x65, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x55, 0x70, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x72, 0x6f, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x72, 0x6f, 0x49, 0x64, 0x22, 0x11,
	0x0a, 0x0f, 0x43, 0x4c, 0x4c, 0x69, 0x6e, 0x65, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x22, 0x24, 0x0a, 0x12, 0x43, 0x4c, 0x4c, 0x69, 0x6e, 0x65, 0x75, 0x70, 0x55, 0x6e, 0x6c,
	0x6f, 0x63, 0x6b, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x23, 0x0a, 0x11, 0x43, 0x4c, 0x4c, 0x69, 0x6e,
	0x65, 0x75, 0x70, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x4e, 0x0a, 0x0e,
	0x43, 0x4c, 0x4c, 0x69, 0x6e, 0x65, 0x75, 0x70, 0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16,
	0x0a, 0x06, 0x68, 0x65, 0x72, 0x6f, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x68, 0x65, 0x72, 0x6f, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63,
	0x65, 0x48, 0x65, 0x72, 0x6f, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x72,
	0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x48, 0x65, 0x72, 0x6f, 0x49, 0x64, 0x22, 0x3e, 0x0a, 0x11,
	0x43, 0x4c, 0x4c, 0x69, 0x6e, 0x65, 0x75, 0x70, 0x52, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x65, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x11, 0x0a, 0x0f,
	0x43, 0x4c, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x75, 0x66, 0x66, 0x52, 0x65, 0x71, 0x22,
	0x0c, 0x0a, 0x0a, 0x43, 0x4c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x22, 0x17, 0x0a,
	0x15, 0x43, 0x4c, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x22, 0x2f, 0x0a, 0x11, 0x43, 0x4c, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x62,
	0x75, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x62,
	0x75, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x22, 0x2b, 0x0a, 0x0f, 0x43, 0x4c, 0x45, 0x6e, 0x74,
	0x65, 0x72, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x63,
	0x65, 0x6e, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x63, 0x65,
	0x6e, 0x65, 0x49, 0x64, 0x22, 0x58, 0x0a, 0x0a, 0x43, 0x4c, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x26, 0x0a, 0x05, 0x6d, 0x6f, 0x76, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x50, 0x42, 0x4d, 0x6f, 0x76, 0x65, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x6d, 0x6f, 0x76, 0x65, 0x73, 0x22, 0x34,
	0x0a, 0x0a, 0x43, 0x4c, 0x52, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x71, 0x12, 0x26, 0x0a, 0x05,
	0x6d, 0x6f, 0x76, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x50, 0x42,
	0x4d, 0x6f, 0x76, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x6d,
	0x6f, 0x76, 0x65, 0x73, 0x22, 0x3d, 0x0a, 0x13, 0x43, 0x4c, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x45, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x77,
	0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x77, 0x69, 0x6e, 0x12, 0x14, 0x0a,
	0x05, 0x6b, 0x69, 0x6c, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6b, 0x69,
	0x6c, 0x6c, 0x73, 0x22, 0x12, 0x0a, 0x10, 0x43, 0x4c, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x42, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x22, 0x37, 0x0a, 0x12, 0x43, 0x4c, 0x43, 0x6c, 0x61,
	0x69, 0x6d, 0x41, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x41, 0x64,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x22, 0x49, 0x0a, 0x16, 0x43, 0x4c, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0b, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x11, 0x0a, 0x0f, 0x43,
	0x4c, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x22, 0x13,
	0x0a, 0x11, 0x43, 0x4c, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x22, 0x36, 0x0a, 0x14, 0x43, 0x4c, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72,
	0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x55, 0x70, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x74,
	0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x49, 0x64, 0x22, 0x35, 0x0a, 0x13, 0x43,
	0x4c, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x72, 0x55, 0x70, 0x52,
	0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65,
	0x49, 0x64, 0x22, 0x80, 0x01, 0x0a, 0x12, 0x43, 0x4c, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72,
	0x65, 0x47, 0x61, 0x63, 0x68, 0x61, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x61, 0x63,
	0x68, 0x61, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x67, 0x61, 0x63, 0x68,
	0x61, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x72, 0x61, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x64, 0x72, 0x61, 0x77, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x32, 0x0a, 0x08, 0x63, 0x6f, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x47, 0x61,
	0x63, 0x68, 0x61, 0x43, 0x6f, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x63, 0x6f, 0x73,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x31, 0x5a, 0x23, 0x6c, 0x69, 0x74, 0x65, 0x66, 0x72, 0x61,
	0x6d, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x63, 0x73, 0xaa, 0x02, 0x09, 0x47,
	0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_CLProtocol_proto_rawDescOnce sync.Once
	file_CLProtocol_proto_rawDescData = file_CLProtocol_proto_rawDesc
)

func file_CLProtocol_proto_rawDescGZIP() []byte {
	file_CLProtocol_proto_rawDescOnce.Do(func() {
		file_CLProtocol_proto_rawDescData = protoimpl.X.CompressGZIP(file_CLProtocol_proto_rawDescData)
	})
	return file_CLProtocol_proto_rawDescData
}

var file_CLProtocol_proto_msgTypes = make([]protoimpl.MessageInfo, 173)
var file_CLProtocol_proto_goTypes = []interface{}{
	(*CL_LOGIN_REQ)(nil),                           // 0: CL_LOGIN_REQ
	(*CL_PlayerData_REQ)(nil),                      // 1: CL_PlayerData_REQ
	(*CLHeartBeat)(nil),                            // 2: CLHeartBeat
	(*CLUpdateServerTime)(nil),                     // 3: CLUpdateServerTime
	(*CLGmReq)(nil),                                // 4: CLGmReq
	(*CL_PlayerData_LoginOut)(nil),                 // 5: CL_PlayerData_LoginOut
	(*CLUseItemReq)(nil),                           // 6: CLUseItemReq
	(*CLFuntionUnLockReq)(nil),                     // 7: CLFuntionUnLockReq
	(*CLMailAllListReq)(nil),                       // 8: CLMailAllListReq
	(*CLReadMailReq)(nil),                          // 9: CLReadMailReq
	(*CLReceiveMailReq)(nil),                       // 10: CLReceiveMailReq
	(*CLReceiveAllMailReq)(nil),                    // 11: CLReceiveAllMailReq
	(*CLDelMailReq)(nil),                           // 12: CLDelMailReq
	(*CLDelAllReadMailReq)(nil),                    // 13: CLDelAllReadMailReq
	(*CLMailQuestionAwardReq)(nil),                 // 14: CLMailQuestionAwardReq
	(*CLChangeNameReq)(nil),                        // 15: CLChangeNameReq
	(*CLChangeSignReq)(nil),                        // 16: CLChangeSignReq
	(*CLChangeGenderReq)(nil),                      // 17: CLChangeGenderReq
	(*CLNewGuideStepInfoReq)(nil),                  // 18: CLNewGuideStepInfoReq
	(*CLNewGuideClientStartReq)(nil),               // 19: CLNewGuideClientStartReq
	(*CLClientTriggerGuideReq)(nil),                // 20: CLClientTriggerGuideReq
	(*CLGuideStepFinishReq)(nil),                   // 21: CLGuideStepFinishReq
	(*CLGuideGroupFinishReq)(nil),                  // 22: CLGuideGroupFinishReq
	(*CLSignInInfoRequest)(nil),                    // 23: CLSignInInfoRequest
	(*CLSignInRewardRequest)(nil),                  // 24: CLSignInRewardRequest
	(*CLSevenDaySignInRewardRequest)(nil),          // 25: CLSevenDaySignInRewardRequest
	(*CLSignMonthRewardRequest)(nil),               // 26: CLSignMonthRewardRequest
	(*CLSyncSettingReq)(nil),                       // 27: CLSyncSettingReq
	(*CLAllFriendInvitationReq)(nil),               // 28: CLAllFriendInvitationReq
	(*CLFriendRasinRewardReq)(nil),                 // 29: CLFriendRasinRewardReq
	(*CLFriendGrowthQuestRewardReq)(nil),           // 30: CLFriendGrowthQuestRewardReq
	(*CLFriendGrowthQuestReceiveRewardReq)(nil),    // 31: CLFriendGrowthQuestReceiveRewardReq
	(*CLFriendGrowthQuestReceiveAllRewardReq)(nil), // 32: CLFriendGrowthQuestReceiveAllRewardReq
	(*CLSharePosterReq)(nil),                       // 33: CLSharePosterReq
	(*CLEnterInvitationCodeReq)(nil),               // 34: CLEnterInvitationCodeReq
	(*CLFinishCommonExpBoxData)(nil),               // 35: CLFinishCommonExpBoxData
	(*CLQuestReq)(nil),                             // 36: CLQuestReq
	(*CLQuestAwardToMailReq)(nil),                  // 37: CLQuestAwardToMailReq
	(*CLPlayerOtherInfo)(nil),                      // 38: CLPlayerOtherInfo
	(*CLGuildCreate)(nil),                          // 39: CLGuildCreate
	(*CLGuildMain)(nil),                            // 40: CLGuildMain
	(*CLGuildFastJoin)(nil),                        // 41: CLGuildFastJoin
	(*CLGuildSearch)(nil),                          // 42: CLGuildSearch
	(*CLGuildApply)(nil),                           // 43: CLGuildApply
	(*CLGuildHall)(nil),                            // 44: CLGuildHall
	(*CLGuildTech)(nil),                            // 45: CLGuildTech
	(*CLGuildTechLevelup)(nil),                     // 46: CLGuildTechLevelup
	(*CLGuildTechReset)(nil),                       // 47: CLGuildTechReset
	(*CLGuildShop)(nil),                            // 48: CLGuildShop
	(*CLGuildShopBuy)(nil),                         // 49: CLGuildShopBuy
	(*CLGuildEdit)(nil),                            // 50: CLGuildEdit
	(*CLGuildApplyMgrList)(nil),                    // 51: CLGuildApplyMgrList
	(*CLGuildMemberMgrList)(nil),                   // 52: CLGuildMemberMgrList
	(*CLGuildApplyMgr)(nil),                        // 53: CLGuildApplyMgr
	(*CLGuildMemberMgr)(nil),                       // 54: CLGuildMemberMgr
	(*CLGuildQuit)(nil),                            // 55: CLGuildQuit
	(*CLGuildDismiss)(nil),                         // 56: CLGuildDismiss
	(*CLGuildImpeach)(nil),                         // 57: CLGuildImpeach
	(*CLGuildSendWorldInvite)(nil),                 // 58: CLGuildSendWorldInvite
	(*CLGuildSendPlayerInvite)(nil),                // 59: CLGuildSendPlayerInvite
	(*CLGuildInviteJoin)(nil),                      // 60: CLGuildInviteJoin
	(*CLGuildDonate)(nil),                          // 61: CLGuildDonate
	(*CLGuildRank)(nil),                            // 62: CLGuildRank
	(*CLGuildLog)(nil),                             // 63: CLGuildLog
	(*CLGuildBargainingInfo)(nil),                  // 64: CLGuildBargainingInfo
	(*CLGuildBargainingNotice)(nil),                // 65: CLGuildBargainingNotice
	(*CLGuildBargaining)(nil),                      // 66: CLGuildBargaining
	(*CLGuildBargainingBuy)(nil),                   // 67: CLGuildBargainingBuy
	(*CLGuildBossEnter)(nil),                       // 68: CLGuildBossEnter
	(*CLGuildBossAtkBegin)(nil),                    // 69: CLGuildBossAtkBegin
	(*CLGuildBossSweep)(nil),                       // 70: CLGuildBossSweep
	(*CLGuildBossBuyCount)(nil),                    // 71: CLGuildBossBuyCount
	(*CLGuildBossRank)(nil),                        // 72: CLGuildBossRank
	(*CLSendChatInfo)(nil),                         // 73: CLSendChatInfo
	(*CLPrivateRed)(nil),                           // 74: CLPrivateRed
	(*CLWorldMsgGet)(nil),                          // 75: CLWorldMsgGet
	(*CLRecommendFriendReq)(nil),                   // 76: CLRecommendFriendReq
	(*CLSyncAllFriendsReq)(nil),                    // 77: CLSyncAllFriendsReq
	(*CLAllGiftReq)(nil),                           // 78: CLAllGiftReq
	(*CLAddOneFriendReq)(nil),                      // 79: CLAddOneFriendReq
	(*CLAgreeFriendReq)(nil),                       // 80: CLAgreeFriendReq
	(*CLRefuseFriendReq)(nil),                      // 81: CLRefuseFriendReq
	(*CLDelOneFriendReq)(nil),                      // 82: CLDelOneFriendReq
	(*CLblacklistOperationReq)(nil),                // 83: CLblacklistOperationReq
	(*CLQuestRed)(nil),                             // 84: CLQuestRed
	(*CLBILog)(nil),                                // 85: CLBILog
	(*CLClientAdReceive)(nil),                      // 86: CLClientAdReceive
	(*CLGuildTopList)(nil),                         // 87: CLGuildTopList
	(*CLHeadIconReq)(nil),                          // 88: CLHeadIconReq
	(*CLReplaceHeadIconReq)(nil),                   // 89: CLReplaceHeadIconReq
	(*CLUnlockHeadIcon)(nil),                       // 90: CLUnlockHeadIcon
	(*CLHeadFrame)(nil),                            // 91: CLHeadFrame
	(*CLReplaceHeadFrame)(nil),                     // 92: CLReplaceHeadFrame
	(*CLUnlockHeadFrame)(nil),                      // 93: CLUnlockHeadFrame
	(*CLGiftBuy)(nil),                              // 94: CLGiftBuy
	(*CLTimeGiftBuy)(nil),                          // 95: CLTimeGiftBuy
	(*CLSevenSignInGetData)(nil),                   // 96: CLSevenSignInGetData
	(*CLSevenSignInGetAward)(nil),                  // 97: CLSevenSignInGetAward
	(*CLDailySignInGetData)(nil),                   // 98: CLDailySignInGetData
	(*CLDailySignInGetAward)(nil),                  // 99: CLDailySignInGetAward
	(*CLDailySignInGetAccruedAward)(nil),           // 100: CLDailySignInGetAccruedAward
	(*CLFirstChargeGetReward)(nil),                 // 101: CLFirstChargeGetReward
	(*CLBuyFirstChargeGift)(nil),                   // 102: CLBuyFirstChargeGift
	(*CLTopupRebateGetData)(nil),                   // 103: CLTopupRebateGetData
	(*CLTopupRebateGetAward)(nil),                  // 104: CLTopupRebateGetAward
	(*CLMonthlyCardGetData)(nil),                   // 105: CLMonthlyCardGetData
	(*CLMonthlyCardBuyCard)(nil),                   // 106: CLMonthlyCardBuyCard
	(*CLMonthlyCardNewGetData)(nil),                // 107: CLMonthlyCardNewGetData
	(*CLMonthlyCardNewGetExtraReward)(nil),         // 108: CLMonthlyCardNewGetExtraReward
	(*CLGradedFundGetData)(nil),                    // 109: CLGradedFundGetData
	(*CLGradedFundBuyFund)(nil),                    // 110: CLGradedFundBuyFund
	(*CLGradedFundGetComWeal)(nil),                 // 111: CLGradedFundGetComWeal
	(*CLGradedFundGetSuperWeal)(nil),               // 112: CLGradedFundGetSuperWeal
	(*CLMissionSubmit)(nil),                        // 113: CLMissionSubmit
	(*CLMissionSubmitById)(nil),                    // 114: CLMissionSubmitById
	(*CLPaymentPreRequestReq)(nil),                 // 115: CLPaymentPreRequestReq
	(*CLGetActivityReward)(nil),                    // 116: CLGetActivityReward
	(*CLGetSevenDayActivityData)(nil),              // 117: CLGetSevenDayActivityData
	(*CLDeleteAccount)(nil),                        // 118: CLDeleteAccount
	(*CLRedeemCodeRewardReq)(nil),                  // 119: CLRedeemCodeRewardReq
	(*CLGachaBonusGetData)(nil),                    // 120: CLGachaBonusGetData
	(*CLGachaBonusGetAward)(nil),                   // 121: CLGachaBonusGetAward
	(*CLGetFuncPrevReward)(nil),                    // 122: CLGetFuncPrevReward
	(*CLGetQuestionReward)(nil),                    // 123: CLGetQuestionReward
	(*CLGachaWheelReward)(nil),                     // 124: CLGachaWheelReward
	(*CL_TowerMain)(nil),                           // 125: CL_TowerMain
	(*CL_TowerStart)(nil),                          // 126: CL_TowerStart
	(*CLTotalRechargeGetReward)(nil),               // 127: CLTotalRechargeGetReward
	(*CLInviteTaskList)(nil),                       // 128: CLInviteTaskList
	(*CLInviteTaskShare)(nil),                      // 129: CLInviteTaskShare
	(*CLInviteTaskGetReward)(nil),                  // 130: CLInviteTaskGetReward
	(*CLArenaGetData)(nil),                         // 131: CLArenaGetData
	(*CLArenaReqChallenge)(nil),                    // 132: CLArenaReqChallenge
	(*CLOpenPowerBuyingReq)(nil),                   // 133: CLOpenPowerBuyingReq
	(*CLPowerBuyingReq)(nil),                       // 134: CLPowerBuyingReq
	(*CLPowerRewardReq)(nil),                       // 135: CLPowerRewardReq
	(*CLAllPowerRewardReq)(nil),                    // 136: CLAllPowerRewardReq
	(*CLOnePowerRewardReq)(nil),                    // 137: CLOnePowerRewardReq
	(*CLHeavenlyDaoInfoReq)(nil),                   // 138: CLHeavenlyDaoInfoReq
	(*CLHeavenlyDaoPromoteReq)(nil),                // 139: CLHeavenlyDaoPromoteReq
	(*CLWeekCardReq)(nil),                          // 140: CLWeekCardReq
	(*CLOpenHookReq)(nil),                          // 141: CLOpenHookReq
	(*CLGetHookRewardReq)(nil),                     // 142: CLGetHookRewardReq
	(*CLGetHookExtraRewardReq)(nil),                // 143: CLGetHookExtraRewardReq
	(*CLTipOffReq)(nil),                            // 144: CLTipOffReq
	(*CLSendSingleGiftReq)(nil),                    // 145: CLSendSingleGiftReq
	(*CLReciveSingleGiftReq)(nil),                  // 146: CLReciveSingleGiftReq
	(*CLSendAllGiftReq)(nil),                       // 147: CLSendAllGiftReq
	(*CLReciveAllGiftReq)(nil),                     // 148: CLReciveAllGiftReq
	(*CLHeroListReq)(nil),                          // 149: CLHeroListReq
	(*CLHeroLevelUpReq)(nil),                       // 150: CLHeroLevelUpReq
	(*CLHeroAwakeLevelUpReq)(nil),                  // 151: CLHeroAwakeLevelUpReq
	(*CLLineupListReq)(nil),                        // 152: CLLineupListReq
	(*CLLineupUnlockSlot)(nil),                     // 153: CLLineupUnlockSlot
	(*CLLineupSwitchReq)(nil),                      // 154: CLLineupSwitchReq
	(*CLLineupSetReq)(nil),                         // 155: CLLineupSetReq
	(*CLLineupRenameReq)(nil),                      // 156: CLLineupRenameReq
	(*CLSeasonBuffReq)(nil),                        // 157: CLSeasonBuffReq
	(*CLMatchReq)(nil),                             // 158: CLMatchReq
	(*CLRoundBattleStartReq)(nil),                  // 159: CLRoundBattleStartReq
	(*CLSelectBufferReq)(nil),                      // 160: CLSelectBufferReq
	(*CLEnterSceneReq)(nil),                        // 161: CLEnterSceneReq
	(*CLMergeReq)(nil),                             // 162: CLMergeReq
	(*CLReadyReq)(nil),                             // 163: CLReadyReq
	(*CLRoundBattleEndReq)(nil),                    // 164: CLRoundBattleEndReq
	(*CLLeaveBattleReq)(nil),                       // 165: CLLeaveBattleReq
	(*CLClaimAdRewardReq)(nil),                     // 166: CLClaimAdRewardReq
	(*CLClaimSeasonRewardReq)(nil),                 // 167: CLClaimSeasonRewardReq
	(*CLSeasonInfoReq)(nil),                        // 168: CLSeasonInfoReq
	(*CLTreasureListReq)(nil),                      // 169: CLTreasureListReq
	(*CLTreasureLevelUpReq)(nil),                   // 170: CLTreasureLevelUpReq
	(*CLTreasureStarUpReq)(nil),                    // 171: CLTreasureStarUpReq
	(*CLTreasureGachaReq)(nil),                     // 172: CLTreasureGachaReq
	(*public.PBDeviceInfo)(nil),                    // 173: PBDeviceInfo
	(*public.PB_MidasInfo)(nil),                    // 174: PB_MidasInfo
	(public.LoginByType)(0),                        // 175: LoginByType
	(*public.PBASAIadData)(nil),                    // 176: PBASAIadData
	(*public.UseItemParam)(nil),                    // 177: UseItemParam
	(public.EGenderType)(0),                        // 178: EGenderType
	(*public.PBCommonKeyValue)(nil),                // 179: PBCommonKeyValue
	(*public.PBIntPair)(nil),                       // 180: PBIntPair
	(public.SignInToType)(0),                       // 181: SignInToType
	(*public.PBSettingData)(nil),                   // 182: PBSettingData
	(public.GuildOpt)(0),                           // 183: GuildOpt
	(public.ChatType)(0),                           // 184: ChatType
	(*public.PBMoveOperation)(nil),                 // 185: PBMoveOperation
	(public.AdRewardType)(0),                       // 186: AdRewardType
	(public.RewardType)(0),                         // 187: RewardType
	(public.TreasureGachaCostType)(0),              // 188: TreasureGachaCostType
}
var file_CLProtocol_proto_depIdxs = []int32{
	173, // 0: CL_LOGIN_REQ.deviceInfo:type_name -> PBDeviceInfo
	174, // 1: CL_LOGIN_REQ.midasInfo:type_name -> PB_MidasInfo
	175, // 2: CL_LOGIN_REQ.gameCenterLoginType:type_name -> LoginByType
	176, // 3: CL_LOGIN_REQ.ASAIadData:type_name -> PBASAIadData
	177, // 4: CLUseItemReq.useParam:type_name -> UseItemParam
	178, // 5: CLChangeGenderReq.gender:type_name -> EGenderType
	179, // 6: CLNewGuideStepInfoReq.curNewGuideSetpInfo:type_name -> PBCommonKeyValue
	179, // 7: CLNewGuideClientStartReq.startNewGuideSetpInfo:type_name -> PBCommonKeyValue
	180, // 8: CLGuideStepFinishReq.guide:type_name -> PBIntPair
	181, // 9: CLSignInInfoRequest.SignInType:type_name -> SignInToType
	181, // 10: CLSignInRewardRequest.SignInType:type_name -> SignInToType
	182, // 11: CLSyncSettingReq.data:type_name -> PBSettingData
	183, // 12: CLGuildEdit.opt:type_name -> GuildOpt
	183, // 13: CLGuildApplyMgr.opt:type_name -> GuildOpt
	183, // 14: CLGuildMemberMgr.opt:type_name -> GuildOpt
	184, // 15: CLSendChatInfo.chattype:type_name -> ChatType
	184, // 16: CLWorldMsgGet.chattype:type_name -> ChatType
	185, // 17: CLMergeReq.moves:type_name -> PBMoveOperation
	185, // 18: CLReadyReq.moves:type_name -> PBMoveOperation
	186, // 19: CLClaimAdRewardReq.type:type_name -> AdRewardType
	187, // 20: CLClaimSeasonRewardReq.type:type_name -> RewardType
	188, // 21: CLTreasureGachaReq.costType:type_name -> TreasureGachaCostType
	22,  // [22:22] is the sub-list for method output_type
	22,  // [22:22] is the sub-list for method input_type
	22,  // [22:22] is the sub-list for extension type_name
	22,  // [22:22] is the sub-list for extension extendee
	0,   // [0:22] is the sub-list for field type_name
}

func init() { file_CLProtocol_proto_init() }
func file_CLProtocol_proto_init() {
	if File_CLProtocol_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_CLProtocol_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CL_LOGIN_REQ); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CL_PlayerData_REQ); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLHeartBeat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLUpdateServerTime); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGmReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CL_PlayerData_LoginOut); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLUseItemReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLFuntionUnLockReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLMailAllListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLReadMailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLReceiveMailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLReceiveAllMailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLDelMailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLDelAllReadMailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLMailQuestionAwardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLChangeNameReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLChangeSignReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLChangeGenderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLNewGuideStepInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLNewGuideClientStartReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLClientTriggerGuideReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuideStepFinishReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuideGroupFinishReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLSignInInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLSignInRewardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLSevenDaySignInRewardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLSignMonthRewardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLSyncSettingReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLAllFriendInvitationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLFriendRasinRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLFriendGrowthQuestRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLFriendGrowthQuestReceiveRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLFriendGrowthQuestReceiveAllRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLSharePosterReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLEnterInvitationCodeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLFinishCommonExpBoxData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLQuestReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLQuestAwardToMailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLPlayerOtherInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildCreate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildMain); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildFastJoin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildSearch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildApply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildHall); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildTech); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildTechLevelup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildTechReset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildShop); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildShopBuy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildEdit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildApplyMgrList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildMemberMgrList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildApplyMgr); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildMemberMgr); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildQuit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildDismiss); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildImpeach); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildSendWorldInvite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildSendPlayerInvite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildInviteJoin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildDonate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildRank); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildBargainingInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildBargainingNotice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildBargaining); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildBargainingBuy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildBossEnter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildBossAtkBegin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildBossSweep); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildBossBuyCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildBossRank); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLSendChatInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLPrivateRed); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLWorldMsgGet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLRecommendFriendReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLSyncAllFriendsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLAllGiftReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLAddOneFriendReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLAgreeFriendReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLRefuseFriendReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLDelOneFriendReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLblacklistOperationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLQuestRed); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLBILog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLClientAdReceive); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGuildTopList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLHeadIconReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLReplaceHeadIconReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLUnlockHeadIcon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLHeadFrame); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLReplaceHeadFrame); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[93].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLUnlockHeadFrame); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[94].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGiftBuy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[95].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLTimeGiftBuy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[96].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLSevenSignInGetData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[97].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLSevenSignInGetAward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[98].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLDailySignInGetData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[99].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLDailySignInGetAward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[100].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLDailySignInGetAccruedAward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[101].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLFirstChargeGetReward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[102].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLBuyFirstChargeGift); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[103].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLTopupRebateGetData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[104].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLTopupRebateGetAward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[105].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLMonthlyCardGetData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[106].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLMonthlyCardBuyCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[107].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLMonthlyCardNewGetData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[108].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLMonthlyCardNewGetExtraReward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[109].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGradedFundGetData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[110].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGradedFundBuyFund); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[111].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGradedFundGetComWeal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[112].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGradedFundGetSuperWeal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[113].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLMissionSubmit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[114].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLMissionSubmitById); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[115].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLPaymentPreRequestReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[116].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGetActivityReward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[117].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGetSevenDayActivityData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[118].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLDeleteAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[119].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLRedeemCodeRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[120].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGachaBonusGetData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[121].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGachaBonusGetAward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[122].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGetFuncPrevReward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[123].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGetQuestionReward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[124].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGachaWheelReward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[125].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CL_TowerMain); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[126].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CL_TowerStart); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[127].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLTotalRechargeGetReward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[128].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLInviteTaskList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[129].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLInviteTaskShare); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[130].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLInviteTaskGetReward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[131].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLArenaGetData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[132].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLArenaReqChallenge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[133].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLOpenPowerBuyingReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[134].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLPowerBuyingReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[135].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLPowerRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[136].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLAllPowerRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[137].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLOnePowerRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[138].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLHeavenlyDaoInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[139].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLHeavenlyDaoPromoteReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[140].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLWeekCardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[141].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLOpenHookReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[142].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGetHookRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[143].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLGetHookExtraRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[144].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLTipOffReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[145].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLSendSingleGiftReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[146].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLReciveSingleGiftReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[147].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLSendAllGiftReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[148].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLReciveAllGiftReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[149].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLHeroListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[150].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLHeroLevelUpReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[151].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLHeroAwakeLevelUpReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[152].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLLineupListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[153].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLLineupUnlockSlot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[154].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLLineupSwitchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[155].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLLineupSetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[156].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLLineupRenameReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[157].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLSeasonBuffReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[158].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLMatchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[159].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLRoundBattleStartReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[160].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLSelectBufferReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[161].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLEnterSceneReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[162].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLMergeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[163].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLReadyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[164].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLRoundBattleEndReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[165].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLLeaveBattleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[166].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLClaimAdRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[167].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLClaimSeasonRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[168].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLSeasonInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[169].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLTreasureListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[170].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLTreasureLevelUpReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[171].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLTreasureStarUpReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_CLProtocol_proto_msgTypes[172].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLTreasureGachaReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_CLProtocol_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   173,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_CLProtocol_proto_goTypes,
		DependencyIndexes: file_CLProtocol_proto_depIdxs,
		MessageInfos:      file_CLProtocol_proto_msgTypes,
	}.Build()
	File_CLProtocol_proto = out.File
	file_CLProtocol_proto_rawDesc = nil
	file_CLProtocol_proto_goTypes = nil
	file_CLProtocol_proto_depIdxs = nil
}
