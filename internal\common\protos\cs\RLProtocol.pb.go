// 协议ID类型为short，-32767 到 32767
//StartMessageID = 6000; // 必须以;分号结束
//MaxMessageID = 6999; // 必须以;分号结束

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.16.0
// source: RLProtocol.proto

package cs

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//客户端登录Logic服请求
type RL_Test_REQ struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlatformID string `protobuf:"bytes,1,opt,name=platformID,proto3" json:"platformID,omitempty"`
}

func (x *RL_Test_REQ) Reset() {
	*x = RL_Test_REQ{}
	if protoimpl.UnsafeEnabled {
		mi := &file_RLProtocol_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RL_Test_REQ) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RL_Test_REQ) ProtoMessage() {}

func (x *RL_Test_REQ) ProtoReflect() protoreflect.Message {
	mi := &file_RLProtocol_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RL_Test_REQ.ProtoReflect.Descriptor instead.
func (*RL_Test_REQ) Descriptor() ([]byte, []int) {
	return file_RLProtocol_proto_rawDescGZIP(), []int{0}
}

func (x *RL_Test_REQ) GetPlatformID() string {
	if x != nil {
		return x.PlatformID
	}
	return ""
}

var File_RLProtocol_proto protoreflect.FileDescriptor

var file_RLProtocol_proto_rawDesc = []byte{
	0x0a, 0x10, 0x52, 0x4c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0b, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x22,
	0x2d, 0x0a, 0x0b, 0x52, 0x4c, 0x5f, 0x54, 0x65, 0x73, 0x74, 0x5f, 0x52, 0x45, 0x51, 0x12, 0x1e,
	0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x44, 0x42, 0x25,
	0x5a, 0x23, 0x6c, 0x69, 0x74, 0x65, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x73, 0x2f, 0x63, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_RLProtocol_proto_rawDescOnce sync.Once
	file_RLProtocol_proto_rawDescData = file_RLProtocol_proto_rawDesc
)

func file_RLProtocol_proto_rawDescGZIP() []byte {
	file_RLProtocol_proto_rawDescOnce.Do(func() {
		file_RLProtocol_proto_rawDescData = protoimpl.X.CompressGZIP(file_RLProtocol_proto_rawDescData)
	})
	return file_RLProtocol_proto_rawDescData
}

var file_RLProtocol_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_RLProtocol_proto_goTypes = []interface{}{
	(*RL_Test_REQ)(nil), // 0: GamePackage.RL_Test_REQ
}
var file_RLProtocol_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_RLProtocol_proto_init() }
func file_RLProtocol_proto_init() {
	if File_RLProtocol_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_RLProtocol_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RL_Test_REQ); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_RLProtocol_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_RLProtocol_proto_goTypes,
		DependencyIndexes: file_RLProtocol_proto_depIdxs,
		MessageInfos:      file_RLProtocol_proto_msgTypes,
	}.Build()
	File_RLProtocol_proto = out.File
	file_RLProtocol_proto_rawDesc = nil
	file_RLProtocol_proto_goTypes = nil
	file_RLProtocol_proto_depIdxs = nil
}
