// 协议ID类型为short，-32767 到 32767
//StartMessageID = 0; // 必须以;分号结束
//MaxMessageID = 199; // 必须以;分号结束

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.16.0
// source: SSProtocol.proto

package cs

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	public "liteframe/internal/common/protos/public"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//Switch与其他服务的心跳检测
type SS_HEARTBEAT_REQUEST struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp int64 `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *SS_HEARTBEAT_REQUEST) Reset() {
	*x = SS_HEARTBEAT_REQUEST{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SS_HEARTBEAT_REQUEST) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SS_HEARTBEAT_REQUEST) ProtoMessage() {}

func (x *SS_HEARTBEAT_REQUEST) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SS_HEARTBEAT_REQUEST.ProtoReflect.Descriptor instead.
func (*SS_HEARTBEAT_REQUEST) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{0}
}

func (x *SS_HEARTBEAT_REQUEST) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

//其他服务返回给Switch的心跳检测
type SS_HEARTBEAT_RESPONSE struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp int64 `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *SS_HEARTBEAT_RESPONSE) Reset() {
	*x = SS_HEARTBEAT_RESPONSE{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SS_HEARTBEAT_RESPONSE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SS_HEARTBEAT_RESPONSE) ProtoMessage() {}

func (x *SS_HEARTBEAT_RESPONSE) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SS_HEARTBEAT_RESPONSE.ProtoReflect.Descriptor instead.
func (*SS_HEARTBEAT_RESPONSE) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{1}
}

func (x *SS_HEARTBEAT_RESPONSE) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

//======================公会开始===============================
//-------------------- Player -> GuildSystem (请求) -------------------------
// 玩家请求创建公会
// 对应 CL: CLGuildCreate
type P2GCreateGuild struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                 // 公会名称
	Notice       string `protobuf:"bytes,2,opt,name=notice,proto3" json:"notice,omitempty"`             // 公会宣言
	IconId       int32  `protobuf:"varint,3,opt,name=iconId,proto3" json:"iconId,omitempty"`            // 徽章ID
	FreeJoin     bool   `protobuf:"varint,4,opt,name=freeJoin,proto3" json:"freeJoin,omitempty"`        // 是否允许自由加入
	ReqStage     int32  `protobuf:"varint,5,opt,name=reqStage,proto3" json:"reqStage,omitempty"`        // 加入申请的关卡条件
	Announcement string `protobuf:"bytes,6,opt,name=announcement,proto3" json:"announcement,omitempty"` // 公会公告
	Uid          uint64 `protobuf:"varint,7,opt,name=uid,proto3" json:"uid,omitempty"`                  // 创建者玩家的UID
}

func (x *P2GCreateGuild) Reset() {
	*x = P2GCreateGuild{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2GCreateGuild) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2GCreateGuild) ProtoMessage() {}

func (x *P2GCreateGuild) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2GCreateGuild.ProtoReflect.Descriptor instead.
func (*P2GCreateGuild) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{2}
}

func (x *P2GCreateGuild) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *P2GCreateGuild) GetNotice() string {
	if x != nil {
		return x.Notice
	}
	return ""
}

func (x *P2GCreateGuild) GetIconId() int32 {
	if x != nil {
		return x.IconId
	}
	return 0
}

func (x *P2GCreateGuild) GetFreeJoin() bool {
	if x != nil {
		return x.FreeJoin
	}
	return false
}

func (x *P2GCreateGuild) GetReqStage() int32 {
	if x != nil {
		return x.ReqStage
	}
	return 0
}

func (x *P2GCreateGuild) GetAnnouncement() string {
	if x != nil {
		return x.Announcement
	}
	return ""
}

func (x *P2GCreateGuild) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

// 玩家请求申请加入公会
// 对应 CL: CLGuildApply
type P2GApplyJoinGuild struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId int64  `protobuf:"varint,1,opt,name=guildId,proto3" json:"guildId,omitempty"` // 目标公会ID
	Uid     uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`         // 申请者玩家的UID GuildSystem将通过此UID从UserSnapSystem获取详情
}

func (x *P2GApplyJoinGuild) Reset() {
	*x = P2GApplyJoinGuild{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2GApplyJoinGuild) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2GApplyJoinGuild) ProtoMessage() {}

func (x *P2GApplyJoinGuild) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2GApplyJoinGuild.ProtoReflect.Descriptor instead.
func (*P2GApplyJoinGuild) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{3}
}

func (x *P2GApplyJoinGuild) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *P2GApplyJoinGuild) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

type P2GJoinGuild struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId int64 `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"` // 目标公会ID
}

func (x *P2GJoinGuild) Reset() {
	*x = P2GJoinGuild{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2GJoinGuild) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2GJoinGuild) ProtoMessage() {}

func (x *P2GJoinGuild) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2GJoinGuild.ProtoReflect.Descriptor instead.
func (*P2GJoinGuild) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{4}
}

func (x *P2GJoinGuild) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

// 玩家请求快速加入公会
// 对应 CL: CLGuildFastJoin
type P2GFastJoinGuild struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid   uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`     // 请求快速加入的玩家UID GuildSystem将通过此UID从UserSnapSystem获取信息以匹配联盟
	Level uint32 `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"` // 玩家等级
}

func (x *P2GFastJoinGuild) Reset() {
	*x = P2GFastJoinGuild{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2GFastJoinGuild) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2GFastJoinGuild) ProtoMessage() {}

func (x *P2GFastJoinGuild) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2GFastJoinGuild.ProtoReflect.Descriptor instead.
func (*P2GFastJoinGuild) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{5}
}

func (x *P2GFastJoinGuild) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *P2GFastJoinGuild) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

// 玩家请求离开公会
// 对应 CL: CLGuildQuit
type P2GLeaveGuild struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId int64  `protobuf:"varint,1,opt,name=guildId,proto3" json:"guildId,omitempty"` // 当前公会ID
	Uid     uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`         // 执行离开操作的玩家UID
}

func (x *P2GLeaveGuild) Reset() {
	*x = P2GLeaveGuild{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2GLeaveGuild) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2GLeaveGuild) ProtoMessage() {}

func (x *P2GLeaveGuild) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2GLeaveGuild.ProtoReflect.Descriptor instead.
func (*P2GLeaveGuild) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{6}
}

func (x *P2GLeaveGuild) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *P2GLeaveGuild) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

type P2GDismissGuildReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId int64 `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
}

func (x *P2GDismissGuildReq) Reset() {
	*x = P2GDismissGuildReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2GDismissGuildReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2GDismissGuildReq) ProtoMessage() {}

func (x *P2GDismissGuildReq) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2GDismissGuildReq.ProtoReflect.Descriptor instead.
func (*P2GDismissGuildReq) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{7}
}

func (x *P2GDismissGuildReq) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

// 玩家请求解散公会 (会长操作)
// 对应 CL: CLGuildDismiss
type P2GDismissGuild struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId int64  `protobuf:"varint,1,opt,name=guildId,proto3" json:"guildId,omitempty"` // 要解散的公会ID
	Uid     uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`         // 执行解散操作的会长玩家UID
}

func (x *P2GDismissGuild) Reset() {
	*x = P2GDismissGuild{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2GDismissGuild) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2GDismissGuild) ProtoMessage() {}

func (x *P2GDismissGuild) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2GDismissGuild.ProtoReflect.Descriptor instead.
func (*P2GDismissGuild) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{8}
}

func (x *P2GDismissGuild) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *P2GDismissGuild) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

type P2GUpdatePosition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId  int64  `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`    // 公会ID
	TargetId uint64 `protobuf:"varint,2,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"` // 目标玩家ID
	Position int32  `protobuf:"varint,3,opt,name=position,proto3" json:"position,omitempty"`                 // 新职位
}

func (x *P2GUpdatePosition) Reset() {
	*x = P2GUpdatePosition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2GUpdatePosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2GUpdatePosition) ProtoMessage() {}

func (x *P2GUpdatePosition) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2GUpdatePosition.ProtoReflect.Descriptor instead.
func (*P2GUpdatePosition) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{9}
}

func (x *P2GUpdatePosition) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *P2GUpdatePosition) GetTargetId() uint64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *P2GUpdatePosition) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

type P2GUpdateContribution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId int64 `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"` // 公会ID
	Value   int32 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`                    // 贡献值变化量
}

func (x *P2GUpdateContribution) Reset() {
	*x = P2GUpdateContribution{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2GUpdateContribution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2GUpdateContribution) ProtoMessage() {}

func (x *P2GUpdateContribution) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2GUpdateContribution.ProtoReflect.Descriptor instead.
func (*P2GUpdateContribution) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{10}
}

func (x *P2GUpdateContribution) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *P2GUpdateContribution) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

// 玩家请求获取公会推荐列表 (当玩家未加入公会时)
// 对应 CL: CLGuildHall (当传入 guildId 为 0 时，Player Actor 内部转换为此请求)
type P2GGetRecommendList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"` // 请求推荐列表的玩家UID
}

func (x *P2GGetRecommendList) Reset() {
	*x = P2GGetRecommendList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2GGetRecommendList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2GGetRecommendList) ProtoMessage() {}

func (x *P2GGetRecommendList) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2GGetRecommendList.ProtoReflect.Descriptor instead.
func (*P2GGetRecommendList) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{11}
}

func (x *P2GGetRecommendList) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

// 玩家请求获取其已加入公会的详细信息
// 对应 CL: CLGuildHall (当传入 guildId 不为 0 时，Player Actor 内部转换为此请求)
type P2GGetGuildDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId int64  `protobuf:"varint,1,opt,name=guildId,proto3" json:"guildId,omitempty"` // 玩家当前所在公会ID
	Uid     uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`         // 请求公会详情的玩家UID (用于GuildSystem确定该玩家在公会中的职位、贡献等)
}

func (x *P2GGetGuildDetail) Reset() {
	*x = P2GGetGuildDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2GGetGuildDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2GGetGuildDetail) ProtoMessage() {}

func (x *P2GGetGuildDetail) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2GGetGuildDetail.ProtoReflect.Descriptor instead.
func (*P2GGetGuildDetail) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{12}
}

func (x *P2GGetGuildDetail) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *P2GGetGuildDetail) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

// 官员请求获取公会申请列表
// 对应 CL: CLGuildApplyMgrList
type P2GGetApplyList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId int64  `protobuf:"varint,1,opt,name=guildId,proto3" json:"guildId,omitempty"` // 官员所在公会ID
	Uid     uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`         // 执行操作的官员玩家UID (用于权限校验，虽然权限主要在Player Actor做，但GuildSystem可做二次校验)
}

func (x *P2GGetApplyList) Reset() {
	*x = P2GGetApplyList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2GGetApplyList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2GGetApplyList) ProtoMessage() {}

func (x *P2GGetApplyList) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2GGetApplyList.ProtoReflect.Descriptor instead.
func (*P2GGetApplyList) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{13}
}

func (x *P2GGetApplyList) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *P2GGetApplyList) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

// 官员处理公会申请 (同意/拒绝)
// 对应 CL: CLGuildApplyMgr
type P2GProcessApplication struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId     int64           `protobuf:"varint,1,opt,name=guildId,proto3" json:"guildId,omitempty"`             // 官员所在公会ID
	OperatorUid uint64          `protobuf:"varint,2,opt,name=operatorUid,proto3" json:"operatorUid,omitempty"`     // 执行操作的官员玩家UID
	TargetUid   uint64          `protobuf:"varint,3,opt,name=targetUid,proto3" json:"targetUid,omitempty"`         // 被处理申请的玩家UID
	Action      public.GuildOpt `protobuf:"varint,4,opt,name=action,proto3,enum=GuildOpt" json:"action,omitempty"` // 操作类型: GuildOpt_ApplyMgrAgree 或 GuildOpt_ApplyMgrRefuse
}

func (x *P2GProcessApplication) Reset() {
	*x = P2GProcessApplication{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2GProcessApplication) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2GProcessApplication) ProtoMessage() {}

func (x *P2GProcessApplication) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2GProcessApplication.ProtoReflect.Descriptor instead.
func (*P2GProcessApplication) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{14}
}

func (x *P2GProcessApplication) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *P2GProcessApplication) GetOperatorUid() uint64 {
	if x != nil {
		return x.OperatorUid
	}
	return 0
}

func (x *P2GProcessApplication) GetTargetUid() uint64 {
	if x != nil {
		return x.TargetUid
	}
	return 0
}

func (x *P2GProcessApplication) GetAction() public.GuildOpt {
	if x != nil {
		return x.Action
	}
	return public.GuildOpt_GuildOpt_None
}

// 官员执行成员管理操作 (任命职位/踢人)
// 对应 CL: CLGuildMemberMgr
type P2GMemberAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId        int64                `protobuf:"varint,1,opt,name=guildId,proto3" json:"guildId,omitempty"`                                  // 官员所在公会ID
	OperatorUid    uint64               `protobuf:"varint,2,opt,name=operatorUid,proto3" json:"operatorUid,omitempty"`                          // [注释: 执行操作的官员玩家UID]
	TargetUid      uint64               `protobuf:"varint,3,opt,name=targetUid,proto3" json:"targetUid,omitempty"`                              // [注释: 被操作的目标成员玩家UID]
	Action         public.GuildOpt      `protobuf:"varint,4,opt,name=action,proto3,enum=GuildOpt" json:"action,omitempty"`                      // 操作类型: 任命职位 (GuildOpt_MemberMgrXXX等) 或踢人 (GuildOpt_MemberMgrKick)
	TargetPosition public.GuildPosition `protobuf:"varint,5,opt,name=targetPosition,proto3,enum=GuildPosition" json:"targetPosition,omitempty"` // 如果是任命操作，这里是目标职位
}

func (x *P2GMemberAction) Reset() {
	*x = P2GMemberAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2GMemberAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2GMemberAction) ProtoMessage() {}

func (x *P2GMemberAction) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2GMemberAction.ProtoReflect.Descriptor instead.
func (*P2GMemberAction) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{15}
}

func (x *P2GMemberAction) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *P2GMemberAction) GetOperatorUid() uint64 {
	if x != nil {
		return x.OperatorUid
	}
	return 0
}

func (x *P2GMemberAction) GetTargetUid() uint64 {
	if x != nil {
		return x.TargetUid
	}
	return 0
}

func (x *P2GMemberAction) GetAction() public.GuildOpt {
	if x != nil {
		return x.Action
	}
	return public.GuildOpt_GuildOpt_None
}

func (x *P2GMemberAction) GetTargetPosition() public.GuildPosition {
	if x != nil {
		return x.TargetPosition
	}
	return public.GuildPosition_GuildPosition_Normal
}

// 官员修改公会信息
// 对应 CL: CLGuildEdit
type P2GEditGuildInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId      int64           `protobuf:"varint,1,opt,name=guildId,proto3" json:"guildId,omitempty"`          // 官员所在公会ID
	OperatorUid  uint64          `protobuf:"varint,2,opt,name=operatorUid,proto3" json:"operatorUid,omitempty"`  // 执行操作的官员玩家UID
	Opt          public.GuildOpt `protobuf:"varint,3,opt,name=opt,proto3,enum=GuildOpt" json:"opt,omitempty"`    // 具体修改哪个信息 (GuildOpt_EditIcon, GuildOpt_EditName 等)
	Name         string          `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                 // 新名称 (如果 opt 是修改名称)
	Notice       string          `protobuf:"bytes,5,opt,name=notice,proto3" json:"notice,omitempty"`             // 新宣言
	IconId       int32           `protobuf:"varint,6,opt,name=iconId,proto3" json:"iconId,omitempty"`            // 新徽章ID
	FreeJoin     bool            `protobuf:"varint,7,opt,name=freeJoin,proto3" json:"freeJoin,omitempty"`        // 新的自由加入设置
	ReqStage     int32           `protobuf:"varint,8,opt,name=reqStage,proto3" json:"reqStage,omitempty"`        // 新的申请关卡条件
	Announcement string          `protobuf:"bytes,9,opt,name=announcement,proto3" json:"announcement,omitempty"` // 新的公告
}

func (x *P2GEditGuildInfo) Reset() {
	*x = P2GEditGuildInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2GEditGuildInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2GEditGuildInfo) ProtoMessage() {}

func (x *P2GEditGuildInfo) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2GEditGuildInfo.ProtoReflect.Descriptor instead.
func (*P2GEditGuildInfo) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{16}
}

func (x *P2GEditGuildInfo) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *P2GEditGuildInfo) GetOperatorUid() uint64 {
	if x != nil {
		return x.OperatorUid
	}
	return 0
}

func (x *P2GEditGuildInfo) GetOpt() public.GuildOpt {
	if x != nil {
		return x.Opt
	}
	return public.GuildOpt_GuildOpt_None
}

func (x *P2GEditGuildInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *P2GEditGuildInfo) GetNotice() string {
	if x != nil {
		return x.Notice
	}
	return ""
}

func (x *P2GEditGuildInfo) GetIconId() int32 {
	if x != nil {
		return x.IconId
	}
	return 0
}

func (x *P2GEditGuildInfo) GetFreeJoin() bool {
	if x != nil {
		return x.FreeJoin
	}
	return false
}

func (x *P2GEditGuildInfo) GetReqStage() int32 {
	if x != nil {
		return x.ReqStage
	}
	return 0
}

func (x *P2GEditGuildInfo) GetAnnouncement() string {
	if x != nil {
		return x.Announcement
	}
	return ""
}

// Player Actor (GM) 请求 GuildSystem 修改公会属性 (等级/经验)
type P2GGMUpdateGuildAttrs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId    uint64 `protobuf:"varint,1,opt,name=playerId,proto3" json:"playerId,omitempty"`                          // 执行GM操作的玩家UID
	GuildId     int64  `protobuf:"varint,2,opt,name=guildId,proto3" json:"guildId,omitempty"`                            // 目标公会的数据库ID
	TargetLevel int32  `protobuf:"varint,3,opt,name=target_level,json=targetLevel,proto3" json:"target_level,omitempty"` // 如果要修改等级，则设置 target_level；否则客户端可以不设置或发送一个特殊值 (如 -1)
	ExpToAdd    int64  `protobuf:"varint,4,opt,name=exp_to_add,json=expToAdd,proto3" json:"exp_to_add,omitempty"`        // 如果要增加经验，则设置 exp_to_add；否则客户端可以发送一个特殊值 (如 -1)
}

func (x *P2GGMUpdateGuildAttrs) Reset() {
	*x = P2GGMUpdateGuildAttrs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2GGMUpdateGuildAttrs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2GGMUpdateGuildAttrs) ProtoMessage() {}

func (x *P2GGMUpdateGuildAttrs) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2GGMUpdateGuildAttrs.ProtoReflect.Descriptor instead.
func (*P2GGMUpdateGuildAttrs) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{17}
}

func (x *P2GGMUpdateGuildAttrs) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *P2GGMUpdateGuildAttrs) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *P2GGMUpdateGuildAttrs) GetTargetLevel() int32 {
	if x != nil {
		return x.TargetLevel
	}
	return 0
}

func (x *P2GGMUpdateGuildAttrs) GetExpToAdd() int64 {
	if x != nil {
		return x.ExpToAdd
	}
	return 0
}

// 玩家请求执行联盟捐献
type P2GGuildDonate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid                     uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`                                         // [注释: 执行捐献的玩家UID]
	GuildId                 int64  `protobuf:"varint,2,opt,name=guildId,proto3" json:"guildId,omitempty"`                                 // 玩家所在公会ID
	DonateOpt               int32  `protobuf:"varint,3,opt,name=donateOpt,proto3" json:"donateOpt,omitempty"`                             // 捐献操作选项 (0:免费, 1:金币, 2:钻石)
	GuildExpGained          int64  `protobuf:"varint,4,opt,name=guildExpGained,proto3" json:"guildExpGained,omitempty"`                   // [注释: 本次捐献为公会增加的经验值]
	GuildContributionGained int64  `protobuf:"varint,5,opt,name=guildContributionGained,proto3" json:"guildContributionGained,omitempty"` // [注释: 本次捐献为公会增加的总贡献值/资金]
}

func (x *P2GGuildDonate) Reset() {
	*x = P2GGuildDonate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2GGuildDonate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2GGuildDonate) ProtoMessage() {}

func (x *P2GGuildDonate) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2GGuildDonate.ProtoReflect.Descriptor instead.
func (*P2GGuildDonate) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{18}
}

func (x *P2GGuildDonate) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *P2GGuildDonate) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *P2GGuildDonate) GetDonateOpt() int32 {
	if x != nil {
		return x.DonateOpt
	}
	return 0
}

func (x *P2GGuildDonate) GetGuildExpGained() int64 {
	if x != nil {
		return x.GuildExpGained
	}
	return 0
}

func (x *P2GGuildDonate) GetGuildContributionGained() int64 {
	if x != nil {
		return x.GuildContributionGained
	}
	return 0
}

//-------------------- GuildSystem -> Player (响应/通知) --------------------
type G2PGuildCreated struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success  bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`                // 创建结果
	GuildId  int64  `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"` // 创建的公会ID
	Name     string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                       // 公会名称
	Position int32  `protobuf:"varint,4,opt,name=position,proto3" json:"position,omitempty"`              // 玩家职位(会长)
}

func (x *G2PGuildCreated) Reset() {
	*x = G2PGuildCreated{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PGuildCreated) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PGuildCreated) ProtoMessage() {}

func (x *G2PGuildCreated) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PGuildCreated.ProtoReflect.Descriptor instead.
func (*G2PGuildCreated) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{19}
}

func (x *G2PGuildCreated) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *G2PGuildCreated) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *G2PGuildCreated) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *G2PGuildCreated) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

// 创建公会响应
// 对应 P2GCreateGuild 的响应
type G2PCreateGuildRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errorcode int32                `protobuf:"varint,1,opt,name=errorcode,proto3" json:"errorcode,omitempty"`
	GuildId   int64                `protobuf:"varint,3,opt,name=guildId,proto3" json:"guildId,omitempty"`
	Name      string               `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Position  public.GuildPosition `protobuf:"varint,5,opt,name=position,proto3,enum=GuildPosition" json:"position,omitempty"` // 玩家职位(会长)
}

func (x *G2PCreateGuildRsp) Reset() {
	*x = G2PCreateGuildRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PCreateGuildRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PCreateGuildRsp) ProtoMessage() {}

func (x *G2PCreateGuildRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PCreateGuildRsp.ProtoReflect.Descriptor instead.
func (*G2PCreateGuildRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{20}
}

func (x *G2PCreateGuildRsp) GetErrorcode() int32 {
	if x != nil {
		return x.Errorcode
	}
	return 0
}

func (x *G2PCreateGuildRsp) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *G2PCreateGuildRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *G2PCreateGuildRsp) GetPosition() public.GuildPosition {
	if x != nil {
		return x.Position
	}
	return public.GuildPosition_GuildPosition_Normal
}

type G2PJoinGuild struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success  bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`                // 加入结果
	GuildId  int64  `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"` // 公会ID
	Name     string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                       // 公会名称
	Position int32  `protobuf:"varint,4,opt,name=position,proto3" json:"position,omitempty"`              // 玩家职位
}

func (x *G2PJoinGuild) Reset() {
	*x = G2PJoinGuild{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PJoinGuild) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PJoinGuild) ProtoMessage() {}

func (x *G2PJoinGuild) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PJoinGuild.ProtoReflect.Descriptor instead.
func (*G2PJoinGuild) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{21}
}

func (x *G2PJoinGuild) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *G2PJoinGuild) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *G2PJoinGuild) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *G2PJoinGuild) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

// 申请/快速加入公会响应
// 对应 P2GApplyJoinGuild / P2GFastJoinGuild 的响应
type G2PJoinGuildRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errorcode int32                `protobuf:"varint,1,opt,name=errorcode,proto3" json:"errorcode,omitempty"`
	GuildId   int64                `protobuf:"varint,3,opt,name=guildId,proto3" json:"guildId,omitempty"`                      // 目标公会ID
	Name      string               `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                             // 公会名称 (仅快速加入成功时有意义)
	Position  public.GuildPosition `protobuf:"varint,5,opt,name=position,proto3,enum=GuildPosition" json:"position,omitempty"` // 玩家职位 (仅快速加入成功时有意义)
}

func (x *G2PJoinGuildRsp) Reset() {
	*x = G2PJoinGuildRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PJoinGuildRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PJoinGuildRsp) ProtoMessage() {}

func (x *G2PJoinGuildRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PJoinGuildRsp.ProtoReflect.Descriptor instead.
func (*G2PJoinGuildRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{22}
}

func (x *G2PJoinGuildRsp) GetErrorcode() int32 {
	if x != nil {
		return x.Errorcode
	}
	return 0
}

func (x *G2PJoinGuildRsp) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *G2PJoinGuildRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *G2PJoinGuildRsp) GetPosition() public.GuildPosition {
	if x != nil {
		return x.Position
	}
	return public.GuildPosition_GuildPosition_Normal
}

type G2PLeaveGuild struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 退出结果
}

func (x *G2PLeaveGuild) Reset() {
	*x = G2PLeaveGuild{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PLeaveGuild) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PLeaveGuild) ProtoMessage() {}

func (x *G2PLeaveGuild) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PLeaveGuild.ProtoReflect.Descriptor instead.
func (*G2PLeaveGuild) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{23}
}

func (x *G2PLeaveGuild) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 离开公会响应
// 对应 P2GLeaveGuild 的响应
type G2PLeaveGuildRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errorcode int32 `protobuf:"varint,1,opt,name=errorcode,proto3" json:"errorcode,omitempty"`
}

func (x *G2PLeaveGuildRsp) Reset() {
	*x = G2PLeaveGuildRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PLeaveGuildRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PLeaveGuildRsp) ProtoMessage() {}

func (x *G2PLeaveGuildRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PLeaveGuildRsp.ProtoReflect.Descriptor instead.
func (*G2PLeaveGuildRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{24}
}

func (x *G2PLeaveGuildRsp) GetErrorcode() int32 {
	if x != nil {
		return x.Errorcode
	}
	return 0
}

type G2PDismissGuildRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errorcode int32 `protobuf:"varint,1,opt,name=errorcode,proto3" json:"errorcode,omitempty"`
	GuildId   int64 `protobuf:"varint,2,opt,name=guildId,proto3" json:"guildId,omitempty"` // 被解散的公会ID
}

func (x *G2PDismissGuildRsp) Reset() {
	*x = G2PDismissGuildRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PDismissGuildRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PDismissGuildRsp) ProtoMessage() {}

func (x *G2PDismissGuildRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PDismissGuildRsp.ProtoReflect.Descriptor instead.
func (*G2PDismissGuildRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{25}
}

func (x *G2PDismissGuildRsp) GetErrorcode() int32 {
	if x != nil {
		return x.Errorcode
	}
	return 0
}

func (x *G2PDismissGuildRsp) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

type G2PPositionChanged struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success  bool  `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`   // 更新结果
	Position int32 `protobuf:"varint,2,opt,name=position,proto3" json:"position,omitempty"` // 新职位
}

func (x *G2PPositionChanged) Reset() {
	*x = G2PPositionChanged{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PPositionChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PPositionChanged) ProtoMessage() {}

func (x *G2PPositionChanged) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PPositionChanged.ProtoReflect.Descriptor instead.
func (*G2PPositionChanged) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{26}
}

func (x *G2PPositionChanged) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *G2PPositionChanged) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

type G2PContributionChanged struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success  bool  `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`                   // 更新结果
	NewValue int32 `protobuf:"varint,2,opt,name=new_value,json=newValue,proto3" json:"new_value,omitempty"` // 新的贡献值
}

func (x *G2PContributionChanged) Reset() {
	*x = G2PContributionChanged{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PContributionChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PContributionChanged) ProtoMessage() {}

func (x *G2PContributionChanged) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PContributionChanged.ProtoReflect.Descriptor instead.
func (*G2PContributionChanged) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{27}
}

func (x *G2PContributionChanged) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *G2PContributionChanged) GetNewValue() int32 {
	if x != nil {
		return x.NewValue
	}
	return 0
}

// 获取公会推荐列表响应
// 对应 P2GGetRecommendList 的响应
type G2PGetRecommendListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errorcode int32                      `protobuf:"varint,1,opt,name=errorcode,proto3" json:"errorcode,omitempty"`
	GuildList []*public.PBGuildRecommend `protobuf:"bytes,2,rep,name=guildList,proto3" json:"guildList,omitempty"`
}

func (x *G2PGetRecommendListRsp) Reset() {
	*x = G2PGetRecommendListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PGetRecommendListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PGetRecommendListRsp) ProtoMessage() {}

func (x *G2PGetRecommendListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PGetRecommendListRsp.ProtoReflect.Descriptor instead.
func (*G2PGetRecommendListRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{28}
}

func (x *G2PGetRecommendListRsp) GetErrorcode() int32 {
	if x != nil {
		return x.Errorcode
	}
	return 0
}

func (x *G2PGetRecommendListRsp) GetGuildList() []*public.PBGuildRecommend {
	if x != nil {
		return x.GuildList
	}
	return nil
}

// 获取公会详细信息响应
// 对应 P2GGetGuildDetail 的响应
type G2PGetGuildDetailRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errorcode           int32                     `protobuf:"varint,1,opt,name=errorcode,proto3" json:"errorcode,omitempty"`
	GuildInfo           *public.PBGuildDetailInfo `protobuf:"bytes,2,opt,name=guild_info,json=guildInfo,proto3" json:"guild_info,omitempty"`                                                     // 联盟的核心详细信息
	MemberList          []*public.PBGuildMember   `protobuf:"bytes,3,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`                                                  // 成员列表
	PlayerGuildPosition public.GuildPosition      `protobuf:"varint,4,opt,name=player_guild_position,json=playerGuildPosition,proto3,enum=GuildPosition" json:"player_guild_position,omitempty"` // 请求者在该公会的职位
	PlayerContribution  int64                     `protobuf:"varint,5,opt,name=player_contribution,json=playerContribution,proto3" json:"player_contribution,omitempty"`                         // 请求者在该公会的贡献度
}

func (x *G2PGetGuildDetailRsp) Reset() {
	*x = G2PGetGuildDetailRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PGetGuildDetailRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PGetGuildDetailRsp) ProtoMessage() {}

func (x *G2PGetGuildDetailRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PGetGuildDetailRsp.ProtoReflect.Descriptor instead.
func (*G2PGetGuildDetailRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{29}
}

func (x *G2PGetGuildDetailRsp) GetErrorcode() int32 {
	if x != nil {
		return x.Errorcode
	}
	return 0
}

func (x *G2PGetGuildDetailRsp) GetGuildInfo() *public.PBGuildDetailInfo {
	if x != nil {
		return x.GuildInfo
	}
	return nil
}

func (x *G2PGetGuildDetailRsp) GetMemberList() []*public.PBGuildMember {
	if x != nil {
		return x.MemberList
	}
	return nil
}

func (x *G2PGetGuildDetailRsp) GetPlayerGuildPosition() public.GuildPosition {
	if x != nil {
		return x.PlayerGuildPosition
	}
	return public.GuildPosition_GuildPosition_Normal
}

func (x *G2PGetGuildDetailRsp) GetPlayerContribution() int64 {
	if x != nil {
		return x.PlayerContribution
	}
	return 0
}

// 获取公会申请列表响应
// 对应 P2GGetApplyList 的响应
type G2PGetApplyListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errorcode         int32                  `protobuf:"varint,1,opt,name=errorcode,proto3" json:"errorcode,omitempty"`
	ApplyList         []*public.PBGuildApply `protobuf:"bytes,2,rep,name=applyList,proto3" json:"applyList,omitempty"`
	TodayJoinedCount  int32                  `protobuf:"varint,3,opt,name=today_joined_count,json=todayJoinedCount,proto3" json:"today_joined_count,omitempty"`      // 今日已通过申请/快速加入的人数
	DailyMaxJoinLimit int32                  `protobuf:"varint,4,opt,name=daily_max_join_limit,json=dailyMaxJoinLimit,proto3" json:"daily_max_join_limit,omitempty"` // 服务器写死的每日最大入盟人数上限 (方便客户端显示 X/Y)
}

func (x *G2PGetApplyListRsp) Reset() {
	*x = G2PGetApplyListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PGetApplyListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PGetApplyListRsp) ProtoMessage() {}

func (x *G2PGetApplyListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PGetApplyListRsp.ProtoReflect.Descriptor instead.
func (*G2PGetApplyListRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{30}
}

func (x *G2PGetApplyListRsp) GetErrorcode() int32 {
	if x != nil {
		return x.Errorcode
	}
	return 0
}

func (x *G2PGetApplyListRsp) GetApplyList() []*public.PBGuildApply {
	if x != nil {
		return x.ApplyList
	}
	return nil
}

func (x *G2PGetApplyListRsp) GetTodayJoinedCount() int32 {
	if x != nil {
		return x.TodayJoinedCount
	}
	return 0
}

func (x *G2PGetApplyListRsp) GetDailyMaxJoinLimit() int32 {
	if x != nil {
		return x.DailyMaxJoinLimit
	}
	return 0
}

// 处理公会申请响应
// 对应 P2GProcessApplication 的响应
type G2PProcessApplicationRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errorcode int32                 `protobuf:"varint,1,opt,name=errorcode,proto3" json:"errorcode,omitempty"`
	Opt       public.GuildOpt       `protobuf:"varint,2,opt,name=opt,proto3,enum=GuildOpt" json:"opt,omitempty"` // 实际执行的操作 (同意/拒绝)
	TargetUid uint64                `protobuf:"varint,3,opt,name=targetUid,proto3" json:"targetUid,omitempty"`   // 被处理申请的玩家UID
	Member    *public.PBGuildMember `protobuf:"bytes,4,opt,name=member,proto3" json:"member,omitempty"`          // 被处理申请的玩家的公会信息
}

func (x *G2PProcessApplicationRsp) Reset() {
	*x = G2PProcessApplicationRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PProcessApplicationRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PProcessApplicationRsp) ProtoMessage() {}

func (x *G2PProcessApplicationRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PProcessApplicationRsp.ProtoReflect.Descriptor instead.
func (*G2PProcessApplicationRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{31}
}

func (x *G2PProcessApplicationRsp) GetErrorcode() int32 {
	if x != nil {
		return x.Errorcode
	}
	return 0
}

func (x *G2PProcessApplicationRsp) GetOpt() public.GuildOpt {
	if x != nil {
		return x.Opt
	}
	return public.GuildOpt_GuildOpt_None
}

func (x *G2PProcessApplicationRsp) GetTargetUid() uint64 {
	if x != nil {
		return x.TargetUid
	}
	return 0
}

func (x *G2PProcessApplicationRsp) GetMember() *public.PBGuildMember {
	if x != nil {
		return x.Member
	}
	return nil
}

// 成员管理操作响应 (任命/踢人)
// 对应 P2GMemberAction 的响应
type G2PMemberActionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errorcode int32                 `protobuf:"varint,1,opt,name=errorcode,proto3" json:"errorcode,omitempty"`
	Action    public.GuildOpt       `protobuf:"varint,2,opt,name=action,proto3,enum=GuildOpt" json:"action,omitempty"` // 实际执行的操作
	TargetUid uint64                `protobuf:"varint,3,opt,name=targetUid,proto3" json:"targetUid,omitempty"`         // 被操作的目标成员玩家UID
	Member    *public.PBGuildMember `protobuf:"bytes,4,opt,name=member,proto3" json:"member,omitempty"`                // 被操作的目标成员玩家的公会信息
}

func (x *G2PMemberActionRsp) Reset() {
	*x = G2PMemberActionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PMemberActionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PMemberActionRsp) ProtoMessage() {}

func (x *G2PMemberActionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PMemberActionRsp.ProtoReflect.Descriptor instead.
func (*G2PMemberActionRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{32}
}

func (x *G2PMemberActionRsp) GetErrorcode() int32 {
	if x != nil {
		return x.Errorcode
	}
	return 0
}

func (x *G2PMemberActionRsp) GetAction() public.GuildOpt {
	if x != nil {
		return x.Action
	}
	return public.GuildOpt_GuildOpt_None
}

func (x *G2PMemberActionRsp) GetTargetUid() uint64 {
	if x != nil {
		return x.TargetUid
	}
	return 0
}

func (x *G2PMemberActionRsp) GetMember() *public.PBGuildMember {
	if x != nil {
		return x.Member
	}
	return nil
}

// 修改公会信息响应
// 对应 P2GEditGuildInfo 的响应
type G2PEditGuildInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errorcode    int32           `protobuf:"varint,1,opt,name=errorcode,proto3" json:"errorcode,omitempty"`
	Opt          public.GuildOpt `protobuf:"varint,2,opt,name=opt,proto3,enum=GuildOpt" json:"opt,omitempty"`    // 实际执行的修改操作
	Name         string          `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                 //名称
	Notice       string          `protobuf:"bytes,4,opt,name=notice,proto3" json:"notice,omitempty"`             //宣言
	IconId       int32           `protobuf:"varint,5,opt,name=iconId,proto3" json:"iconId,omitempty"`            //图标
	FreeJoin     bool            `protobuf:"varint,6,opt,name=freeJoin,proto3" json:"freeJoin,omitempty"`        //true 自由加入 false 需要审批，
	ReqStage     int32           `protobuf:"varint,7,opt,name=reqStage,proto3" json:"reqStage,omitempty"`        //审批时需具备的关卡条件：0.无限制 1.困难 2.疯狂 3.地狱
	Announcement string          `protobuf:"bytes,8,opt,name=announcement,proto3" json:"announcement,omitempty"` // 修改后的公告
}

func (x *G2PEditGuildInfoRsp) Reset() {
	*x = G2PEditGuildInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PEditGuildInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PEditGuildInfoRsp) ProtoMessage() {}

func (x *G2PEditGuildInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PEditGuildInfoRsp.ProtoReflect.Descriptor instead.
func (*G2PEditGuildInfoRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{33}
}

func (x *G2PEditGuildInfoRsp) GetErrorcode() int32 {
	if x != nil {
		return x.Errorcode
	}
	return 0
}

func (x *G2PEditGuildInfoRsp) GetOpt() public.GuildOpt {
	if x != nil {
		return x.Opt
	}
	return public.GuildOpt_GuildOpt_None
}

func (x *G2PEditGuildInfoRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *G2PEditGuildInfoRsp) GetNotice() string {
	if x != nil {
		return x.Notice
	}
	return ""
}

func (x *G2PEditGuildInfoRsp) GetIconId() int32 {
	if x != nil {
		return x.IconId
	}
	return 0
}

func (x *G2PEditGuildInfoRsp) GetFreeJoin() bool {
	if x != nil {
		return x.FreeJoin
	}
	return false
}

func (x *G2PEditGuildInfoRsp) GetReqStage() int32 {
	if x != nil {
		return x.ReqStage
	}
	return 0
}

func (x *G2PEditGuildInfoRsp) GetAnnouncement() string {
	if x != nil {
		return x.Announcement
	}
	return ""
}

// 公会状态通用更新通知 (GuildSystem 主动推送给 Player)
// 用于处理: 玩家成功加入、被踢、职位变更、联盟升级、申请被处理或过期等需要更新 Player 内部状态的场景
// LC 对应: 多种LC场景都可能由这个SS消息触发 Player 状态更新，进而影响后续LC消息的准确性 (如LC_Guild_Sync, LCGuildHall)
type G2PGuildGeneralUpdateNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UpdateType             public.GuildUpdateType `protobuf:"varint,1,opt,name=updateType,proto3,enum=GuildUpdateType" json:"updateType,omitempty"`    // 更新类型枚举
	GuildId                int64                  `protobuf:"varint,2,opt,name=guildId,proto3" json:"guildId,omitempty"`                               // 相关公会ID
	GuildName              string                 `protobuf:"bytes,3,opt,name=guildName,proto3" json:"guildName,omitempty"`                            // 相关公会名称
	NewPosition            public.GuildPosition   `protobuf:"varint,4,opt,name=newPosition,proto3,enum=GuildPosition" json:"newPosition,omitempty"`    // 新的职位
	NewGuildLevel          int32                  `protobuf:"varint,5,opt,name=newGuildLevel,proto3" json:"newGuildLevel,omitempty"`                   // 新的联盟等级
	PendingApplyRemoved    bool                   `protobuf:"varint,6,opt,name=pendingApplyRemoved,proto3" json:"pendingApplyRemoved,omitempty"`       // 如果是申请过期或被处理，标记此申请是否从玩家待处理列表移除
	RelatedGuildIdForApply int64                  `protobuf:"varint,7,opt,name=relatedGuildIdForApply,proto3" json:"relatedGuildIdForApply,omitempty"` // 如果是申请相关的通知，这里是被申请的公会ID
	TargetUid              uint64                 `protobuf:"varint,8,opt,name=targetUid,proto3" json:"targetUid,omitempty"`                           // 玩家uid
}

func (x *G2PGuildGeneralUpdateNtf) Reset() {
	*x = G2PGuildGeneralUpdateNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PGuildGeneralUpdateNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PGuildGeneralUpdateNtf) ProtoMessage() {}

func (x *G2PGuildGeneralUpdateNtf) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PGuildGeneralUpdateNtf.ProtoReflect.Descriptor instead.
func (*G2PGuildGeneralUpdateNtf) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{34}
}

func (x *G2PGuildGeneralUpdateNtf) GetUpdateType() public.GuildUpdateType {
	if x != nil {
		return x.UpdateType
	}
	return public.GuildUpdateType_GuildUpdateType_UNKNOWN
}

func (x *G2PGuildGeneralUpdateNtf) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *G2PGuildGeneralUpdateNtf) GetGuildName() string {
	if x != nil {
		return x.GuildName
	}
	return ""
}

func (x *G2PGuildGeneralUpdateNtf) GetNewPosition() public.GuildPosition {
	if x != nil {
		return x.NewPosition
	}
	return public.GuildPosition_GuildPosition_Normal
}

func (x *G2PGuildGeneralUpdateNtf) GetNewGuildLevel() int32 {
	if x != nil {
		return x.NewGuildLevel
	}
	return 0
}

func (x *G2PGuildGeneralUpdateNtf) GetPendingApplyRemoved() bool {
	if x != nil {
		return x.PendingApplyRemoved
	}
	return false
}

func (x *G2PGuildGeneralUpdateNtf) GetRelatedGuildIdForApply() int64 {
	if x != nil {
		return x.RelatedGuildIdForApply
	}
	return 0
}

func (x *G2PGuildGeneralUpdateNtf) GetTargetUid() uint64 {
	if x != nil {
		return x.TargetUid
	}
	return 0
}

// GuildSystem 对 Player Actor (GM) 发起的GM操作的响应
type G2PCommonGMRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errorcode int32 `protobuf:"varint,1,opt,name=errorcode,proto3" json:"errorcode,omitempty"`
}

func (x *G2PCommonGMRsp) Reset() {
	*x = G2PCommonGMRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PCommonGMRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PCommonGMRsp) ProtoMessage() {}

func (x *G2PCommonGMRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PCommonGMRsp.ProtoReflect.Descriptor instead.
func (*G2PCommonGMRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{35}
}

func (x *G2PCommonGMRsp) GetErrorcode() int32 {
	if x != nil {
		return x.Errorcode
	}
	return 0
}

// 联盟捐献响应
type G2PGuildDonateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errorcode                 int32 `protobuf:"varint,1,opt,name=errorcode,proto3" json:"errorcode,omitempty"`
	NewGuildLevel             int32 `protobuf:"varint,2,opt,name=newGuildLevel,proto3" json:"newGuildLevel,omitempty"`                         // 捐献后的公会等级
	NewGuildExp               int64 `protobuf:"varint,3,opt,name=newGuildExp,proto3" json:"newGuildExp,omitempty"`                             // 捐献后的公会经验
	NewGuildTotalContribution int64 `protobuf:"varint,4,opt,name=newGuildTotalContribution,proto3" json:"newGuildTotalContribution,omitempty"` // 捐献后的公会总贡献
	Opt                       int32 `protobuf:"varint,5,opt,name=opt,proto3" json:"opt,omitempty"`                                             //捐献操作：0.免费 1.金币 2.钻石
}

func (x *G2PGuildDonateRsp) Reset() {
	*x = G2PGuildDonateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PGuildDonateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PGuildDonateRsp) ProtoMessage() {}

func (x *G2PGuildDonateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PGuildDonateRsp.ProtoReflect.Descriptor instead.
func (*G2PGuildDonateRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{36}
}

func (x *G2PGuildDonateRsp) GetErrorcode() int32 {
	if x != nil {
		return x.Errorcode
	}
	return 0
}

func (x *G2PGuildDonateRsp) GetNewGuildLevel() int32 {
	if x != nil {
		return x.NewGuildLevel
	}
	return 0
}

func (x *G2PGuildDonateRsp) GetNewGuildExp() int64 {
	if x != nil {
		return x.NewGuildExp
	}
	return 0
}

func (x *G2PGuildDonateRsp) GetNewGuildTotalContribution() int64 {
	if x != nil {
		return x.NewGuildTotalContribution
	}
	return 0
}

func (x *G2PGuildDonateRsp) GetOpt() int32 {
	if x != nil {
		return x.Opt
	}
	return 0
}

//-------------------- GuildSystem <-> PlayerSystem -------------------------
type G2PSUpdateGuildMembersReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId int64           `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"` // 解散的公会id
	Uids    []uint64        `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`               // 需要更新的玩家uid列表
	Opt     public.GuildOpt `protobuf:"varint,3,opt,name=opt,proto3,enum=GuildOpt" json:"opt,omitempty"`
}

func (x *G2PSUpdateGuildMembersReq) Reset() {
	*x = G2PSUpdateGuildMembersReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PSUpdateGuildMembersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PSUpdateGuildMembersReq) ProtoMessage() {}

func (x *G2PSUpdateGuildMembersReq) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PSUpdateGuildMembersReq.ProtoReflect.Descriptor instead.
func (*G2PSUpdateGuildMembersReq) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{37}
}

func (x *G2PSUpdateGuildMembersReq) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *G2PSUpdateGuildMembersReq) GetUids() []uint64 {
	if x != nil {
		return x.Uids
	}
	return nil
}

func (x *G2PSUpdateGuildMembersReq) GetOpt() public.GuildOpt {
	if x != nil {
		return x.Opt
	}
	return public.GuildOpt_GuildOpt_None
}

type PS2GUpdateGuildMembersRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code       uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	GuildId    int64    `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	FailedUids []uint64 `protobuf:"varint,3,rep,packed,name=failed_uids,json=failedUids,proto3" json:"failed_uids,omitempty"` // 更新失败的uid列表
}

func (x *PS2GUpdateGuildMembersRsp) Reset() {
	*x = PS2GUpdateGuildMembersRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PS2GUpdateGuildMembersRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PS2GUpdateGuildMembersRsp) ProtoMessage() {}

func (x *PS2GUpdateGuildMembersRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PS2GUpdateGuildMembersRsp.ProtoReflect.Descriptor instead.
func (*PS2GUpdateGuildMembersRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{38}
}

func (x *PS2GUpdateGuildMembersRsp) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PS2GUpdateGuildMembersRsp) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *PS2GUpdateGuildMembersRsp) GetFailedUids() []uint64 {
	if x != nil {
		return x.FailedUids
	}
	return nil
}

// GuildSystem -> PlayerSystem: 请求批量更新玩家的公会相关状态
// 用于: 公会解散时通知所有成员，联盟升级时通知所有成员
type G2PSBatchUpdatePlayerGuildStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId       int64                                `protobuf:"varint,1,opt,name=guildId,proto3" json:"guildId,omitempty"`
	Uids          []uint64                             `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"` // 需要更新状态的玩家UID列表
	ActionType    public.GuildSystemInternalActionType `protobuf:"varint,3,opt,name=actionType,proto3,enum=GuildSystemInternalActionType" json:"actionType,omitempty"`
	NewGuildLevel int32                                `protobuf:"varint,4,opt,name=newGuildLevel,proto3" json:"newGuildLevel,omitempty"` // 如果 actionType 是 LEVEL_UP
	NewMaxMembers int32                                `protobuf:"varint,5,opt,name=newMaxMembers,proto3" json:"newMaxMembers,omitempty"` // 如果 actionType 是 LEVEL_UP
}

func (x *G2PSBatchUpdatePlayerGuildStatusReq) Reset() {
	*x = G2PSBatchUpdatePlayerGuildStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PSBatchUpdatePlayerGuildStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PSBatchUpdatePlayerGuildStatusReq) ProtoMessage() {}

func (x *G2PSBatchUpdatePlayerGuildStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PSBatchUpdatePlayerGuildStatusReq.ProtoReflect.Descriptor instead.
func (*G2PSBatchUpdatePlayerGuildStatusReq) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{39}
}

func (x *G2PSBatchUpdatePlayerGuildStatusReq) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *G2PSBatchUpdatePlayerGuildStatusReq) GetUids() []uint64 {
	if x != nil {
		return x.Uids
	}
	return nil
}

func (x *G2PSBatchUpdatePlayerGuildStatusReq) GetActionType() public.GuildSystemInternalActionType {
	if x != nil {
		return x.ActionType
	}
	return public.GuildSystemInternalActionType_INTERNAL_ACTION_UNKNOWN
}

func (x *G2PSBatchUpdatePlayerGuildStatusReq) GetNewGuildLevel() int32 {
	if x != nil {
		return x.NewGuildLevel
	}
	return 0
}

func (x *G2PSBatchUpdatePlayerGuildStatusReq) GetNewMaxMembers() int32 {
	if x != nil {
		return x.NewMaxMembers
	}
	return 0
}

// GuildSystem -> PlayerSystem: 通知单个玩家成功加入公会 (申请被同意或快速加入成功)
type G2PSNotifyPlayerJoinedGuild struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid        uint64               `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"` // 加入公会的玩家UID
	GuildId    int64                `protobuf:"varint,2,opt,name=guildId,proto3" json:"guildId,omitempty"`
	GuildName  string               `protobuf:"bytes,3,opt,name=guildName,proto3" json:"guildName,omitempty"`
	Position   public.GuildPosition `protobuf:"varint,4,opt,name=position,proto3,enum=GuildPosition" json:"position,omitempty"`
	GuildLevel int32                `protobuf:"varint,5,opt,name=guildLevel,proto3" json:"guildLevel,omitempty"`
}

func (x *G2PSNotifyPlayerJoinedGuild) Reset() {
	*x = G2PSNotifyPlayerJoinedGuild{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PSNotifyPlayerJoinedGuild) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PSNotifyPlayerJoinedGuild) ProtoMessage() {}

func (x *G2PSNotifyPlayerJoinedGuild) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PSNotifyPlayerJoinedGuild.ProtoReflect.Descriptor instead.
func (*G2PSNotifyPlayerJoinedGuild) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{40}
}

func (x *G2PSNotifyPlayerJoinedGuild) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *G2PSNotifyPlayerJoinedGuild) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *G2PSNotifyPlayerJoinedGuild) GetGuildName() string {
	if x != nil {
		return x.GuildName
	}
	return ""
}

func (x *G2PSNotifyPlayerJoinedGuild) GetPosition() public.GuildPosition {
	if x != nil {
		return x.Position
	}
	return public.GuildPosition_GuildPosition_Normal
}

func (x *G2PSNotifyPlayerJoinedGuild) GetGuildLevel() int32 {
	if x != nil {
		return x.GuildLevel
	}
	return 0
}

// GuildSystem -> PlayerSystem: 通知单个玩家离开或被踢出公会
type G2PSNotifyPlayerLeftGuild struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid     uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`         // 离开/被踢的玩家UID
	GuildId int64  `protobuf:"varint,2,opt,name=guildId,proto3" json:"guildId,omitempty"` // 涉及的公会ID
}

func (x *G2PSNotifyPlayerLeftGuild) Reset() {
	*x = G2PSNotifyPlayerLeftGuild{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PSNotifyPlayerLeftGuild) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PSNotifyPlayerLeftGuild) ProtoMessage() {}

func (x *G2PSNotifyPlayerLeftGuild) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PSNotifyPlayerLeftGuild.ProtoReflect.Descriptor instead.
func (*G2PSNotifyPlayerLeftGuild) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{41}
}

func (x *G2PSNotifyPlayerLeftGuild) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *G2PSNotifyPlayerLeftGuild) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

// GuildSystem -> PlayerSystem: 通知单个玩家职位发生变更
type G2PSNotifyPlayerPositionChanged struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid         uint64               `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"` // 职位变更的玩家UID
	GuildId     int64                `protobuf:"varint,2,opt,name=guildId,proto3" json:"guildId,omitempty"`
	NewPosition public.GuildPosition `protobuf:"varint,3,opt,name=newPosition,proto3,enum=GuildPosition" json:"newPosition,omitempty"`
	Opt         public.GuildOpt      `protobuf:"varint,4,opt,name=opt,proto3,enum=GuildOpt" json:"opt,omitempty"`
}

func (x *G2PSNotifyPlayerPositionChanged) Reset() {
	*x = G2PSNotifyPlayerPositionChanged{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PSNotifyPlayerPositionChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PSNotifyPlayerPositionChanged) ProtoMessage() {}

func (x *G2PSNotifyPlayerPositionChanged) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PSNotifyPlayerPositionChanged.ProtoReflect.Descriptor instead.
func (*G2PSNotifyPlayerPositionChanged) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{42}
}

func (x *G2PSNotifyPlayerPositionChanged) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *G2PSNotifyPlayerPositionChanged) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *G2PSNotifyPlayerPositionChanged) GetNewPosition() public.GuildPosition {
	if x != nil {
		return x.NewPosition
	}
	return public.GuildPosition_GuildPosition_Normal
}

func (x *G2PSNotifyPlayerPositionChanged) GetOpt() public.GuildOpt {
	if x != nil {
		return x.Opt
	}
	return public.GuildOpt_GuildOpt_None
}

// GuildSystem -> PlayerSystem: 通知单个玩家其对某个公会的待处理申请应被移除 (过期/已被处理(批准/拒绝))
type G2PSNotifyRemovePendingApply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid     uint64          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`         // 申请被移除的玩家UID
	GuildId int64           `protobuf:"varint,2,opt,name=guildId,proto3" json:"guildId,omitempty"` // 对应的申请公会ID
	Opt     public.GuildOpt `protobuf:"varint,3,opt,name=opt,proto3,enum=GuildOpt" json:"opt,omitempty"`
}

func (x *G2PSNotifyRemovePendingApply) Reset() {
	*x = G2PSNotifyRemovePendingApply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2PSNotifyRemovePendingApply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2PSNotifyRemovePendingApply) ProtoMessage() {}

func (x *G2PSNotifyRemovePendingApply) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2PSNotifyRemovePendingApply.ProtoReflect.Descriptor instead.
func (*G2PSNotifyRemovePendingApply) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{43}
}

func (x *G2PSNotifyRemovePendingApply) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *G2PSNotifyRemovePendingApply) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *G2PSNotifyRemovePendingApply) GetOpt() public.GuildOpt {
	if x != nil {
		return x.Opt
	}
	return public.GuildOpt_GuildOpt_None
}

//-------------------- PlayerSystem -> Player -------------------------------
type PS2PUpdateGuildStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId int64           `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"` // 被解散的公会id
	Opt     public.GuildOpt `protobuf:"varint,2,opt,name=opt,proto3,enum=GuildOpt" json:"opt,omitempty"`
}

func (x *PS2PUpdateGuildStatusReq) Reset() {
	*x = PS2PUpdateGuildStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PS2PUpdateGuildStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PS2PUpdateGuildStatusReq) ProtoMessage() {}

func (x *PS2PUpdateGuildStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PS2PUpdateGuildStatusReq.ProtoReflect.Descriptor instead.
func (*PS2PUpdateGuildStatusReq) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{44}
}

func (x *PS2PUpdateGuildStatusReq) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *PS2PUpdateGuildStatusReq) GetOpt() public.GuildOpt {
	if x != nil {
		return x.Opt
	}
	return public.GuildOpt_GuildOpt_None
}

type P2PSUpdateGuildStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	GuildId int64  `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid     uint64 `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"` // 当前player actor的uid
}

func (x *P2PSUpdateGuildStatusRsp) Reset() {
	*x = P2PSUpdateGuildStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2PSUpdateGuildStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2PSUpdateGuildStatusRsp) ProtoMessage() {}

func (x *P2PSUpdateGuildStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2PSUpdateGuildStatusRsp.ProtoReflect.Descriptor instead.
func (*P2PSUpdateGuildStatusRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{45}
}

func (x *P2PSUpdateGuildStatusRsp) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *P2PSUpdateGuildStatusRsp) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *P2PSUpdateGuildStatusRsp) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

// PlayerSystem -> Player: 要求玩家 Actor 更新其内部的公会相关数据
// 触发场景: 收到 G2PSxxx 通知后，PlayerSystem 转发给对应的 Player Actor
type PS2PUpdateGuildAffiliationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId    int64                `protobuf:"varint,1,opt,name=guildId,proto3" json:"guildId,omitempty"`                      // 新的 guild_id (0 表示离开/解散)
	GuildName  string               `protobuf:"bytes,2,opt,name=guildName,proto3" json:"guildName,omitempty"`                   // 新的公会名称
	Position   public.GuildPosition `protobuf:"varint,3,opt,name=position,proto3,enum=GuildPosition" json:"position,omitempty"` // 新的职位
	GuildLevel int32                `protobuf:"varint,4,opt,name=guildLevel,proto3" json:"guildLevel,omitempty"`                // 新的公会等级
	Opt        public.GuildOpt      `protobuf:"varint,5,opt,name=opt,proto3,enum=GuildOpt" json:"opt,omitempty"`
}

func (x *PS2PUpdateGuildAffiliationReq) Reset() {
	*x = PS2PUpdateGuildAffiliationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PS2PUpdateGuildAffiliationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PS2PUpdateGuildAffiliationReq) ProtoMessage() {}

func (x *PS2PUpdateGuildAffiliationReq) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PS2PUpdateGuildAffiliationReq.ProtoReflect.Descriptor instead.
func (*PS2PUpdateGuildAffiliationReq) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{46}
}

func (x *PS2PUpdateGuildAffiliationReq) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *PS2PUpdateGuildAffiliationReq) GetGuildName() string {
	if x != nil {
		return x.GuildName
	}
	return ""
}

func (x *PS2PUpdateGuildAffiliationReq) GetPosition() public.GuildPosition {
	if x != nil {
		return x.Position
	}
	return public.GuildPosition_GuildPosition_Normal
}

func (x *PS2PUpdateGuildAffiliationReq) GetGuildLevel() int32 {
	if x != nil {
		return x.GuildLevel
	}
	return 0
}

func (x *PS2PUpdateGuildAffiliationReq) GetOpt() public.GuildOpt {
	if x != nil {
		return x.Opt
	}
	return public.GuildOpt_GuildOpt_None
}

// PlayerSystem -> Player: 要求玩家 Actor 移除其本地维护的某个待处理申请ID
type PS2PRemovePendingApplyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId int64           `protobuf:"varint,1,opt,name=guildId,proto3" json:"guildId,omitempty"` // 需要从玩家的待处理申请列表中移除的公会ID
	Opt     public.GuildOpt `protobuf:"varint,2,opt,name=opt,proto3,enum=GuildOpt" json:"opt,omitempty"`
}

func (x *PS2PRemovePendingApplyReq) Reset() {
	*x = PS2PRemovePendingApplyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PS2PRemovePendingApplyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PS2PRemovePendingApplyReq) ProtoMessage() {}

func (x *PS2PRemovePendingApplyReq) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PS2PRemovePendingApplyReq.ProtoReflect.Descriptor instead.
func (*PS2PRemovePendingApplyReq) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{47}
}

func (x *PS2PRemovePendingApplyReq) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *PS2PRemovePendingApplyReq) GetOpt() public.GuildOpt {
	if x != nil {
		return x.Opt
	}
	return public.GuildOpt_GuildOpt_None
}

// Player -> PlayerSystem: 玩家 Actor 已更新公会状态后的响应
type P2PSUpdateGuildAffiliationRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errorcode int32  `protobuf:"varint,1,opt,name=errorcode,proto3" json:"errorcode,omitempty"`
	GuildId   int64  `protobuf:"varint,2,opt,name=guildId,proto3" json:"guildId,omitempty"`
	Uid       uint64 `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"` // 当前玩家的UID
}

func (x *P2PSUpdateGuildAffiliationRsp) Reset() {
	*x = P2PSUpdateGuildAffiliationRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2PSUpdateGuildAffiliationRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2PSUpdateGuildAffiliationRsp) ProtoMessage() {}

func (x *P2PSUpdateGuildAffiliationRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2PSUpdateGuildAffiliationRsp.ProtoReflect.Descriptor instead.
func (*P2PSUpdateGuildAffiliationRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{48}
}

func (x *P2PSUpdateGuildAffiliationRsp) GetErrorcode() int32 {
	if x != nil {
		return x.Errorcode
	}
	return 0
}

func (x *P2PSUpdateGuildAffiliationRsp) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *P2PSUpdateGuildAffiliationRsp) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

// 付款预请求创建订单 玩家->支付系统
type P2PMCreateOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId         string `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId,omitempty"`                 // 订单 ID，主键
	GoodsRegisterId string `protobuf:"bytes,2,opt,name=goodsRegisterId,proto3" json:"goodsRegisterId,omitempty"` // 商品注册ID
	ModuleType      int32  `protobuf:"varint,3,opt,name=moduleType,proto3" json:"moduleType,omitempty"`          // 支付对应模块
	GoodsPrice      int32  `protobuf:"varint,4,opt,name=goodsPrice,proto3" json:"goodsPrice,omitempty"`          // 产品实际支付价格（RMB级别为元，此参数不带引号，请用数字类型处理）
	ChannelId       string `protobuf:"bytes,5,opt,name=channelId,proto3" json:"channelId,omitempty"`             // 渠道标识
	GameGoodsId     string `protobuf:"bytes,6,opt,name=gameGoodsId,proto3" json:"gameGoodsId,omitempty"`         // 游戏商品ID
	PlayerId        uint64 `protobuf:"varint,7,opt,name=playerId,proto3" json:"playerId,omitempty"`              // 玩家 ID
}

func (x *P2PMCreateOrderReq) Reset() {
	*x = P2PMCreateOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2PMCreateOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2PMCreateOrderReq) ProtoMessage() {}

func (x *P2PMCreateOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2PMCreateOrderReq.ProtoReflect.Descriptor instead.
func (*P2PMCreateOrderReq) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{49}
}

func (x *P2PMCreateOrderReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *P2PMCreateOrderReq) GetGoodsRegisterId() string {
	if x != nil {
		return x.GoodsRegisterId
	}
	return ""
}

func (x *P2PMCreateOrderReq) GetModuleType() int32 {
	if x != nil {
		return x.ModuleType
	}
	return 0
}

func (x *P2PMCreateOrderReq) GetGoodsPrice() int32 {
	if x != nil {
		return x.GoodsPrice
	}
	return 0
}

func (x *P2PMCreateOrderReq) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *P2PMCreateOrderReq) GetGameGoodsId() string {
	if x != nil {
		return x.GameGoodsId
	}
	return ""
}

func (x *P2PMCreateOrderReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

type PM2PCreateOrderRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code     uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`        // 错误码
	PushInfo string `protobuf:"bytes,2,opt,name=pushInfo,proto3" json:"pushInfo,omitempty"` // 透传字段
}

func (x *PM2PCreateOrderRsp) Reset() {
	*x = PM2PCreateOrderRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PM2PCreateOrderRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PM2PCreateOrderRsp) ProtoMessage() {}

func (x *PM2PCreateOrderRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PM2PCreateOrderRsp.ProtoReflect.Descriptor instead.
func (*PM2PCreateOrderRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{50}
}

func (x *PM2PCreateOrderRsp) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PM2PCreateOrderRsp) GetPushInfo() string {
	if x != nil {
		return x.PushInfo
	}
	return ""
}

// 发货	支付系统->玩家
type PM2PSDeliverReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Goods *public.PBPayGoodsInfo  `protobuf:"bytes,1,opt,name=goods,proto3" json:"goods,omitempty"`
	Quest *public.PBQuestDataInfo `protobuf:"bytes,2,opt,name=quest,proto3" json:"quest,omitempty"`
}

func (x *PM2PSDeliverReq) Reset() {
	*x = PM2PSDeliverReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PM2PSDeliverReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PM2PSDeliverReq) ProtoMessage() {}

func (x *PM2PSDeliverReq) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PM2PSDeliverReq.ProtoReflect.Descriptor instead.
func (*PM2PSDeliverReq) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{51}
}

func (x *PM2PSDeliverReq) GetGoods() *public.PBPayGoodsInfo {
	if x != nil {
		return x.Goods
	}
	return nil
}

func (x *PM2PSDeliverReq) GetQuest() *public.PBQuestDataInfo {
	if x != nil {
		return x.Quest
	}
	return nil
}

type PS2PMDeliverRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  uint32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Goods *public.PBPayGoodsInfo  `protobuf:"bytes,2,opt,name=goods,proto3" json:"goods,omitempty"`
	Quest *public.PBQuestDataInfo `protobuf:"bytes,3,opt,name=quest,proto3" json:"quest,omitempty"`
}

func (x *PS2PMDeliverRsp) Reset() {
	*x = PS2PMDeliverRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PS2PMDeliverRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PS2PMDeliverRsp) ProtoMessage() {}

func (x *PS2PMDeliverRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PS2PMDeliverRsp.ProtoReflect.Descriptor instead.
func (*PS2PMDeliverRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{52}
}

func (x *PS2PMDeliverRsp) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PS2PMDeliverRsp) GetGoods() *public.PBPayGoodsInfo {
	if x != nil {
		return x.Goods
	}
	return nil
}

func (x *PS2PMDeliverRsp) GetQuest() *public.PBQuestDataInfo {
	if x != nil {
		return x.Quest
	}
	return nil
}

type PS2PDeliverReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Goods *public.PBPayGoodsInfo `protobuf:"bytes,1,opt,name=goods,proto3" json:"goods,omitempty"`
}

func (x *PS2PDeliverReq) Reset() {
	*x = PS2PDeliverReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PS2PDeliverReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PS2PDeliverReq) ProtoMessage() {}

func (x *PS2PDeliverReq) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PS2PDeliverReq.ProtoReflect.Descriptor instead.
func (*PS2PDeliverReq) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{53}
}

func (x *PS2PDeliverReq) GetGoods() *public.PBPayGoodsInfo {
	if x != nil {
		return x.Goods
	}
	return nil
}

type PS2PQuestReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Quest *public.PBQuestDataInfo `protobuf:"bytes,1,opt,name=quest,proto3" json:"quest,omitempty"`
}

func (x *PS2PQuestReq) Reset() {
	*x = PS2PQuestReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PS2PQuestReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PS2PQuestReq) ProtoMessage() {}

func (x *PS2PQuestReq) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PS2PQuestReq.ProtoReflect.Descriptor instead.
func (*PS2PQuestReq) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{54}
}

func (x *PS2PQuestReq) GetQuest() *public.PBQuestDataInfo {
	if x != nil {
		return x.Quest
	}
	return nil
}

type P2PMDeliverRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Goods *public.PBPayGoodsInfo `protobuf:"bytes,2,opt,name=goods,proto3" json:"goods,omitempty"`
}

func (x *P2PMDeliverRsp) Reset() {
	*x = P2PMDeliverRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2PMDeliverRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2PMDeliverRsp) ProtoMessage() {}

func (x *P2PMDeliverRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2PMDeliverRsp.ProtoReflect.Descriptor instead.
func (*P2PMDeliverRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{55}
}

func (x *P2PMDeliverRsp) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *P2PMDeliverRsp) GetGoods() *public.PBPayGoodsInfo {
	if x != nil {
		return x.Goods
	}
	return nil
}

// 玩家->监控系统
type P2USUserUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserData *public.UserSnapUserInfo `protobuf:"bytes,1,opt,name=user_data,json=userData,proto3" json:"user_data,omitempty"`
}

func (x *P2USUserUpdate) Reset() {
	*x = P2USUserUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2USUserUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2USUserUpdate) ProtoMessage() {}

func (x *P2USUserUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2USUserUpdate.ProtoReflect.Descriptor instead.
func (*P2USUserUpdate) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{56}
}

func (x *P2USUserUpdate) GetUserData() *public.UserSnapUserInfo {
	if x != nil {
		return x.UserData
	}
	return nil
}

// 所需服务器->监控系统
type S2USGetUserData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *S2USGetUserData) Reset() {
	*x = S2USGetUserData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2USGetUserData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2USGetUserData) ProtoMessage() {}

func (x *S2USGetUserData) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2USGetUserData.ProtoReflect.Descriptor instead.
func (*S2USGetUserData) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{57}
}

func (x *S2USGetUserData) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

type US2SGetUserDataRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code     uint32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 0成功，1失败
	UserData *public.UserSnapUserInfo `protobuf:"bytes,2,opt,name=user_data,json=userData,proto3" json:"user_data,omitempty"`
}

func (x *US2SGetUserDataRsp) Reset() {
	*x = US2SGetUserDataRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *US2SGetUserDataRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*US2SGetUserDataRsp) ProtoMessage() {}

func (x *US2SGetUserDataRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use US2SGetUserDataRsp.ProtoReflect.Descriptor instead.
func (*US2SGetUserDataRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{58}
}

func (x *US2SGetUserDataRsp) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *US2SGetUserDataRsp) GetUserData() *public.UserSnapUserInfo {
	if x != nil {
		return x.UserData
	}
	return nil
}

type S2USSetNameIfAbsent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid  uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"` //新名字
}

func (x *S2USSetNameIfAbsent) Reset() {
	*x = S2USSetNameIfAbsent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2USSetNameIfAbsent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2USSetNameIfAbsent) ProtoMessage() {}

func (x *S2USSetNameIfAbsent) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2USSetNameIfAbsent.ProtoReflect.Descriptor instead.
func (*S2USSetNameIfAbsent) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{59}
}

func (x *S2USSetNameIfAbsent) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *S2USSetNameIfAbsent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type US2SSetNameIfAbsentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 0成功，1失败
}

func (x *US2SSetNameIfAbsentRsp) Reset() {
	*x = US2SSetNameIfAbsentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *US2SSetNameIfAbsentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*US2SSetNameIfAbsentRsp) ProtoMessage() {}

func (x *US2SSetNameIfAbsentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use US2SSetNameIfAbsentRsp.ProtoReflect.Descriptor instead.
func (*US2SSetNameIfAbsentRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{60}
}

func (x *US2SSetNameIfAbsentRsp) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

//====================竞技场开始=======================
//-------------------- ArenaSystem <-> Player -------------------------------
//竞技场获取数据
type P2ASArenaGetData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid       uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`             // ID
	Name      string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`            // 名字
	Level     uint32 `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`         // 等级
	HeadIcon  int32  `protobuf:"varint,4,opt,name=headIcon,proto3" json:"headIcon,omitempty"`   // 头像
	HeadFrame int32  `protobuf:"varint,5,opt,name=headFrame,proto3" json:"headFrame,omitempty"` // 头像框
}

func (x *P2ASArenaGetData) Reset() {
	*x = P2ASArenaGetData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2ASArenaGetData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2ASArenaGetData) ProtoMessage() {}

func (x *P2ASArenaGetData) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2ASArenaGetData.ProtoReflect.Descriptor instead.
func (*P2ASArenaGetData) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{61}
}

func (x *P2ASArenaGetData) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *P2ASArenaGetData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *P2ASArenaGetData) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *P2ASArenaGetData) GetHeadIcon() int32 {
	if x != nil {
		return x.HeadIcon
	}
	return 0
}

func (x *P2ASArenaGetData) GetHeadFrame() int32 {
	if x != nil {
		return x.HeadFrame
	}
	return 0
}

type AS2PArenaGetData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score     uint32                     `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"`        // 竞技场积分
	RivalList []*public.PBArenaRivalInfo `protobuf:"bytes,2,rep,name=rivalList,proto3" json:"rivalList,omitempty"` // 对手列表
}

func (x *AS2PArenaGetData) Reset() {
	*x = AS2PArenaGetData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AS2PArenaGetData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AS2PArenaGetData) ProtoMessage() {}

func (x *AS2PArenaGetData) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AS2PArenaGetData.ProtoReflect.Descriptor instead.
func (*AS2PArenaGetData) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{62}
}

func (x *AS2PArenaGetData) GetScore() uint32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *AS2PArenaGetData) GetRivalList() []*public.PBArenaRivalInfo {
	if x != nil {
		return x.RivalList
	}
	return nil
}

//竞技场请求挑战对手
type P2ASArenaReqChallenge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RivalIdx int32 `protobuf:"varint,1,opt,name=rivalIdx,proto3" json:"rivalIdx,omitempty"` // 对手索引 1~3
}

func (x *P2ASArenaReqChallenge) Reset() {
	*x = P2ASArenaReqChallenge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2ASArenaReqChallenge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2ASArenaReqChallenge) ProtoMessage() {}

func (x *P2ASArenaReqChallenge) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2ASArenaReqChallenge.ProtoReflect.Descriptor instead.
func (*P2ASArenaReqChallenge) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{63}
}

func (x *P2ASArenaReqChallenge) GetRivalIdx() int32 {
	if x != nil {
		return x.RivalIdx
	}
	return 0
}

type AS2PArenaChallengeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RivalIdx int32 `protobuf:"varint,1,opt,name=rivalIdx,proto3" json:"rivalIdx,omitempty"` // 对手索引 1~3
	Result   int32 `protobuf:"varint,2,opt,name=result,proto3" json:"result,omitempty"`     // 挑战结果 0 成功   1 失败
}

func (x *AS2PArenaChallengeResult) Reset() {
	*x = AS2PArenaChallengeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AS2PArenaChallengeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AS2PArenaChallengeResult) ProtoMessage() {}

func (x *AS2PArenaChallengeResult) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AS2PArenaChallengeResult.ProtoReflect.Descriptor instead.
func (*AS2PArenaChallengeResult) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{64}
}

func (x *AS2PArenaChallengeResult) GetRivalIdx() int32 {
	if x != nil {
		return x.RivalIdx
	}
	return 0
}

func (x *AS2PArenaChallengeResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

//=================== 举报开始 ========================
// 举报信息
type P2TSTipOff struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId       uint64 `protobuf:"varint,1,opt,name=playerId,proto3" json:"playerId,omitempty"`             // 被举报者的玩家ID
	TipOffType     int32  `protobuf:"varint,2,opt,name=TipOffType,proto3" json:"TipOffType,omitempty"`         // 举报类型
	TipOffContent  string `protobuf:"bytes,3,opt,name=TipOffContent,proto3" json:"TipOffContent,omitempty"`    // 举报说明
	SenderPlayerId uint64 `protobuf:"varint,4,opt,name=senderPlayerId,proto3" json:"senderPlayerId,omitempty"` // 举报者的玩家ID
}

func (x *P2TSTipOff) Reset() {
	*x = P2TSTipOff{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2TSTipOff) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2TSTipOff) ProtoMessage() {}

func (x *P2TSTipOff) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2TSTipOff.ProtoReflect.Descriptor instead.
func (*P2TSTipOff) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{65}
}

func (x *P2TSTipOff) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *P2TSTipOff) GetTipOffType() int32 {
	if x != nil {
		return x.TipOffType
	}
	return 0
}

func (x *P2TSTipOff) GetTipOffContent() string {
	if x != nil {
		return x.TipOffContent
	}
	return ""
}

func (x *P2TSTipOff) GetSenderPlayerId() uint64 {
	if x != nil {
		return x.SenderPlayerId
	}
	return 0
}

// 举报信息返回
type TS2PTipOff struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result int32 `protobuf:"varint,2,opt,name=result,proto3" json:"result,omitempty"` // 0 成功   1 失败
}

func (x *TS2PTipOff) Reset() {
	*x = TS2PTipOff{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TS2PTipOff) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TS2PTipOff) ProtoMessage() {}

func (x *TS2PTipOff) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TS2PTipOff.ProtoReflect.Descriptor instead.
func (*TS2PTipOff) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{66}
}

func (x *TS2PTipOff) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

//====================赛季BUFF开始=======================
//-------------------- SeasonBuffSystem <-> Player -------------------------------
//请求赛季BUFF信息
type P2SBSeasonBuffReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *P2SBSeasonBuffReq) Reset() {
	*x = P2SBSeasonBuffReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2SBSeasonBuffReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2SBSeasonBuffReq) ProtoMessage() {}

func (x *P2SBSeasonBuffReq) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2SBSeasonBuffReq.ProtoReflect.Descriptor instead.
func (*P2SBSeasonBuffReq) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{67}
}

//赛季BUFF信息返回
type SB2PSeasonBuffRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode int32                `protobuf:"varint,1,opt,name=errorCode,proto3" json:"errorCode,omitempty"`
	Info      *public.PBSeasonBuff `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *SB2PSeasonBuffRsp) Reset() {
	*x = SB2PSeasonBuffRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SB2PSeasonBuffRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SB2PSeasonBuffRsp) ProtoMessage() {}

func (x *SB2PSeasonBuffRsp) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SB2PSeasonBuffRsp.ProtoReflect.Descriptor instead.
func (*SB2PSeasonBuffRsp) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{68}
}

func (x *SB2PSeasonBuffRsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *SB2PSeasonBuffRsp) GetInfo() *public.PBSeasonBuff {
	if x != nil {
		return x.Info
	}
	return nil
}

//-------------------- SeasonBuffSystem <-> PlayerSystem -------------------------------
//赛季buff同步玩家系统
type SB2PSSeasonBuffSync struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info *public.PBSeasonBuff `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *SB2PSSeasonBuffSync) Reset() {
	*x = SB2PSSeasonBuffSync{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SB2PSSeasonBuffSync) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SB2PSSeasonBuffSync) ProtoMessage() {}

func (x *SB2PSSeasonBuffSync) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SB2PSSeasonBuffSync.ProtoReflect.Descriptor instead.
func (*SB2PSSeasonBuffSync) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{69}
}

func (x *SB2PSSeasonBuffSync) GetInfo() *public.PBSeasonBuff {
	if x != nil {
		return x.Info
	}
	return nil
}

//-------------------- PlayerSystem <-> Player -------------------------------
type PS2PSeasonBuffSync struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info *public.PBSeasonBuff `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *PS2PSeasonBuffSync) Reset() {
	*x = PS2PSeasonBuffSync{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PS2PSeasonBuffSync) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PS2PSeasonBuffSync) ProtoMessage() {}

func (x *PS2PSeasonBuffSync) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PS2PSeasonBuffSync.ProtoReflect.Descriptor instead.
func (*PS2PSeasonBuffSync) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{70}
}

func (x *PS2PSeasonBuffSync) GetInfo() *public.PBSeasonBuff {
	if x != nil {
		return x.Info
	}
	return nil
}

//====================赛季开始=======================
type SeasonResetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OldId int32 `protobuf:"varint,1,opt,name=oldId,proto3" json:"oldId,omitempty"`
	NewId int32 `protobuf:"varint,2,opt,name=newId,proto3" json:"newId,omitempty"`
}

func (x *SeasonResetReq) Reset() {
	*x = SeasonResetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_SSProtocol_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeasonResetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeasonResetReq) ProtoMessage() {}

func (x *SeasonResetReq) ProtoReflect() protoreflect.Message {
	mi := &file_SSProtocol_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeasonResetReq.ProtoReflect.Descriptor instead.
func (*SeasonResetReq) Descriptor() ([]byte, []int) {
	return file_SSProtocol_proto_rawDescGZIP(), []int{71}
}

func (x *SeasonResetReq) GetOldId() int32 {
	if x != nil {
		return x.OldId
	}
	return 0
}

func (x *SeasonResetReq) GetNewId() int32 {
	if x != nil {
		return x.NewId
	}
	return 0
}

var File_SSProtocol_proto protoreflect.FileDescriptor

var file_SSProtocol_proto_rawDesc = []byte{
	0x0a, 0x10, 0x53, 0x53, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0b, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x1a,
	0x13, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x45, 0x6e, 0x75, 0x6d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x34, 0x0a, 0x14, 0x53, 0x53, 0x5f, 0x48, 0x45, 0x41,
	0x52, 0x54, 0x42, 0x45, 0x41, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x12, 0x1c,
	0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x35, 0x0a, 0x15,
	0x53, 0x53, 0x5f, 0x48, 0x45, 0x41, 0x52, 0x54, 0x42, 0x45, 0x41, 0x54, 0x5f, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x22, 0xc2, 0x01, 0x0a, 0x0e, 0x50, 0x32, 0x47, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f,
	0x74, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x74, 0x69,
	0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x63, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x69, 0x63, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x72,
	0x65, 0x65, 0x4a, 0x6f, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x66, 0x72,
	0x65, 0x65, 0x4a, 0x6f, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x65, 0x71, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e,
	0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x3f, 0x0a, 0x11, 0x50, 0x32, 0x47, 0x41,
	0x70, 0x70, 0x6c, 0x79, 0x4a, 0x6f, 0x69, 0x6e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x29, 0x0a, 0x0c, 0x50, 0x32, 0x47,
	0x4a, 0x6f, 0x69, 0x6e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x75, 0x69,
	0x6c, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69,
	0x6c, 0x64, 0x49, 0x64, 0x22, 0x3a, 0x0a, 0x10, 0x50, 0x32, 0x47, 0x46, 0x61, 0x73, 0x74, 0x4a,
	0x6f, 0x69, 0x6e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x22, 0x3b, 0x0a, 0x0d, 0x50, 0x32, 0x47, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x2f, 0x0a,
	0x12, 0x50, 0x32, 0x47, 0x44, 0x69, 0x73, 0x6d, 0x69, 0x73, 0x73, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x22, 0x3d,
	0x0a, 0x0f, 0x50, 0x32, 0x47, 0x44, 0x69, 0x73, 0x6d, 0x69, 0x73, 0x73, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x67, 0x0a,
	0x11, 0x50, 0x32, 0x47, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x48, 0x0a, 0x15, 0x50, 0x32, 0x47, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x19, 0x0a, 0x08, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0x27, 0x0a, 0x13, 0x50, 0x32, 0x47, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x3f, 0x0a, 0x11, 0x50, 0x32, 0x47,
	0x47, 0x65, 0x74, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x18,
	0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x3d, 0x0a, 0x0f, 0x50, 0x32,
	0x47, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x94, 0x01, 0x0a, 0x15, 0x50, 0x32,
	0x47, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x20, 0x0a,
	0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x55, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x55, 0x69, 0x64, 0x12, 0x21, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x09, 0x2e,
	0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0xc6, 0x01, 0x0a, 0x0f, 0x50, 0x32, 0x47, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x20,
	0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x55, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x55, 0x69, 0x64, 0x12, 0x21,
	0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x09,
	0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x36, 0x0a, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x8b, 0x02, 0x0a, 0x10, 0x50, 0x32,
	0x47, 0x45, 0x64, 0x69, 0x74, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18,
	0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x03, 0x6f, 0x70,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f,
	0x70, 0x74, 0x52, 0x03, 0x6f, 0x70, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e,
	0x6f, 0x74, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x74,
	0x69, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x63, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x63, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x66,
	0x72, 0x65, 0x65, 0x4a, 0x6f, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x66,
	0x72, 0x65, 0x65, 0x4a, 0x6f, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x65, 0x71, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75,
	0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x8e, 0x01, 0x0a, 0x15, 0x50, 0x32, 0x47, 0x47,
	0x4d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x41, 0x74, 0x74, 0x72,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x0a, 0x65, 0x78,
	0x70, 0x5f, 0x74, 0x6f, 0x5f, 0x61, 0x64, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x65, 0x78, 0x70, 0x54, 0x6f, 0x41, 0x64, 0x64, 0x22, 0xbc, 0x01, 0x0a, 0x0e, 0x50, 0x32, 0x47,
	0x47, 0x75, 0x69, 0x6c, 0x64, 0x44, 0x6f, 0x6e, 0x61, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x6e, 0x61, 0x74,
	0x65, 0x4f, 0x70, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x64, 0x6f, 0x6e, 0x61,
	0x74, 0x65, 0x4f, 0x70, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x45, 0x78,
	0x70, 0x47, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x67,
	0x75, 0x69, 0x6c, 0x64, 0x45, 0x78, 0x70, 0x47, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x12, 0x38, 0x0a,
	0x17, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x47, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x17,
	0x67, 0x75, 0x69, 0x6c, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x47, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x22, 0x76, 0x0a, 0x0f, 0x47, 0x32, 0x50, 0x47, 0x75,
	0x69, 0x6c, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x8b, 0x01, 0x0a, 0x11, 0x47, 0x32, 0x50, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x75, 0x69,
	0x6c, 0x64, 0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x2a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x73, 0x0a,
	0x0c, 0x47, 0x32, 0x50, 0x4a, 0x6f, 0x69, 0x6e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x75, 0x69, 0x6c, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x89, 0x01, 0x0a, 0x0f, 0x47, 0x32, 0x50, 0x4a, 0x6f, 0x69, 0x6e, 0x47, 0x75,
	0x69, 0x6c, 0x64, 0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x29,
	0x0a, 0x0d, 0x47, 0x32, 0x50, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x30, 0x0a, 0x10, 0x47, 0x32, 0x50,
	0x4c, 0x65, 0x61, 0x76, 0x65, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a,
	0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x4c, 0x0a, 0x12, 0x47,
	0x32, 0x50, 0x44, 0x69, 0x73, 0x6d, 0x69, 0x73, 0x73, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x73,
	0x70, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x22, 0x4a, 0x0a, 0x12, 0x47, 0x32, 0x50,
	0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4f, 0x0a, 0x16, 0x47, 0x32, 0x50, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x65, 0x77,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6e, 0x65,
	0x77, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x67, 0x0a, 0x16, 0x47, 0x32, 0x50, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70,
	0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2f,
	0x0a, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x50, 0x42, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x52, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x8d, 0x02, 0x0a, 0x14, 0x47, 0x32, 0x50, 0x47, 0x65, 0x74, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x31, 0x0a, 0x0a, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x50, 0x42, 0x47,
	0x75, 0x69, 0x6c, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09,
	0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2f, 0x0a, 0x0b, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x50, 0x42, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0a,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x42, 0x0a, 0x15, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x5f, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x13, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f,
	0x0a, 0x13, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0xbe, 0x01, 0x0a, 0x12, 0x47, 0x32, 0x50, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x50, 0x42, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x52, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x5f, 0x6a, 0x6f, 0x69, 0x6e, 0x65,
	0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x74,
	0x6f, 0x64, 0x61, 0x79, 0x4a, 0x6f, 0x69, 0x6e, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x2f, 0x0a, 0x14, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x6a, 0x6f, 0x69,
	0x6e, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x64,
	0x61, 0x69, 0x6c, 0x79, 0x4d, 0x61, 0x78, 0x4a, 0x6f, 0x69, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x22, 0x9b, 0x01, 0x0a, 0x18, 0x47, 0x32, 0x50, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a,
	0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x03, 0x6f,
	0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x4f, 0x70, 0x74, 0x52, 0x03, 0x6f, 0x70, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x55, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x55, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x50, 0x42, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x9b,
	0x01, 0x0a, 0x12, 0x47, 0x32, 0x50, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x52, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x55, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x55, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x50, 0x42, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x22, 0xf0, 0x01, 0x0a,
	0x13, 0x47, 0x32, 0x50, 0x45, 0x64, 0x69, 0x74, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x1b, 0x0a, 0x03, 0x6f, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x09, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x52, 0x03, 0x6f, 0x70, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69,
	0x63, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x63, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x72, 0x65, 0x65, 0x4a, 0x6f, 0x69, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x66, 0x72, 0x65, 0x65, 0x4a, 0x6f, 0x69, 0x6e, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x53, 0x74, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x72, 0x65, 0x71, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61,
	0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x22,
	0xe4, 0x02, 0x0a, 0x18, 0x47, 0x32, 0x50, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x74, 0x66, 0x12, 0x30, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x10, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x75, 0x69, 0x6c,
	0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x75, 0x69,
	0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x0b, 0x6e, 0x65, 0x77, 0x50, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x47, 0x75,
	0x69, 0x6c, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x6e, 0x65, 0x77,
	0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x6e, 0x65, 0x77, 0x47,
	0x75, 0x69, 0x6c, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x6e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x30,
	0x0a, 0x13, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x70, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x64,
	0x12, 0x36, 0x0a, 0x16, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x49, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x16, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64,
	0x46, 0x6f, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x55, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x55, 0x69, 0x64, 0x22, 0x2e, 0x0a, 0x0e, 0x47, 0x32, 0x50, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x47, 0x4d, 0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xc9, 0x01, 0x0a, 0x11, 0x47, 0x32, 0x50, 0x47, 0x75,
	0x69, 0x6c, 0x64, 0x44, 0x6f, 0x6e, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x6e, 0x65,
	0x77, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x6e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x20, 0x0a, 0x0b, 0x6e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x45, 0x78, 0x70, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x45,
	0x78, 0x70, 0x12, 0x3c, 0x0a, 0x19, 0x6e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x19, 0x6e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x54,
	0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x10, 0x0a, 0x03, 0x6f, 0x70, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6f,
	0x70, 0x74, 0x22, 0x67, 0x0a, 0x19, 0x47, 0x32, 0x50, 0x53, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x47, 0x75, 0x69, 0x6c, 0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x19, 0x0a, 0x08, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x69,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x04, 0x75, 0x69, 0x64, 0x73, 0x12, 0x1b,
	0x0a, 0x03, 0x6f, 0x70, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x47, 0x75,
	0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x52, 0x03, 0x6f, 0x70, 0x74, 0x22, 0x6b, 0x0a, 0x19, 0x50,
	0x53, 0x32, 0x47, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x67, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x65,
	0x64, 0x5f, 0x75, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0a, 0x66, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x55, 0x69, 0x64, 0x73, 0x22, 0xdf, 0x01, 0x0a, 0x23, 0x47, 0x32, 0x50,
	0x53, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x12, 0x18, 0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x69,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x04, 0x75, 0x69, 0x64, 0x73, 0x12, 0x3e,
	0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24,
	0x0a, 0x0d, 0x6e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x24, 0x0a, 0x0d, 0x6e, 0x65, 0x77, 0x4d, 0x61, 0x78, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6e, 0x65, 0x77,
	0x4d, 0x61, 0x78, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x22, 0xb3, 0x01, 0x0a, 0x1b, 0x47,
	0x32, 0x50, 0x53, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4a,
	0x6f, 0x69, 0x6e, 0x65, 0x64, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67,
	0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x22, 0x47, 0x0a, 0x19, 0x47, 0x32, 0x50, 0x53, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x4c, 0x65, 0x66, 0x74, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x22, 0x9c, 0x01, 0x0a, 0x1f, 0x47, 0x32,
	0x50, 0x53, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x0b, 0x6e, 0x65, 0x77,
	0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e,
	0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b,
	0x6e, 0x65, 0x77, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x03, 0x6f,
	0x70, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x4f, 0x70, 0x74, 0x52, 0x03, 0x6f, 0x70, 0x74, 0x22, 0x67, 0x0a, 0x1c, 0x47, 0x32, 0x50, 0x53,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x75,
	0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69,
	0x6c, 0x64, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x03, 0x6f, 0x70, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x09, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x52, 0x03, 0x6f, 0x70,
	0x74, 0x22, 0x52, 0x0a, 0x18, 0x50, 0x53, 0x32, 0x50, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47,
	0x75, 0x69, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a,
	0x08, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x03, 0x6f, 0x70, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74,
	0x52, 0x03, 0x6f, 0x70, 0x74, 0x22, 0x5b, 0x0a, 0x18, 0x50, 0x32, 0x50, 0x53, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73,
	0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x22, 0xc0, 0x01, 0x0a, 0x1d, 0x50, 0x53, 0x32, 0x50, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x41, 0x66, 0x66, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x08,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e,
	0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x75, 0x69, 0x6c,
	0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x67, 0x75,
	0x69, 0x6c, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1b, 0x0a, 0x03, 0x6f, 0x70, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74,
	0x52, 0x03, 0x6f, 0x70, 0x74, 0x22, 0x52, 0x0a, 0x19, 0x50, 0x53, 0x32, 0x50, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x52,
	0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x03,
	0x6f, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x4f, 0x70, 0x74, 0x52, 0x03, 0x6f, 0x70, 0x74, 0x22, 0x69, 0x0a, 0x1d, 0x50, 0x32, 0x50,
	0x53, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x41, 0x66, 0x66, 0x69,
	0x6c, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c,
	0x64, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64,
	0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x03, 0x75, 0x69, 0x64, 0x22, 0xf4, 0x01, 0x0a, 0x12, 0x50, 0x32, 0x50, 0x4d, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x67, 0x6f, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x20, 0x0a,
	0x0b, 0x67, 0x61, 0x6d, 0x65, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x67, 0x61, 0x6d, 0x65, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x22, 0x44, 0x0a, 0x12, 0x50,
	0x4d, 0x32, 0x50, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x73,
	0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x75, 0x73, 0x68, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x75, 0x73, 0x68, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0x60, 0x0a, 0x0f, 0x50, 0x4d, 0x32, 0x50, 0x53, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x05, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x50, 0x42, 0x50, 0x61, 0x79, 0x47, 0x6f, 0x6f, 0x64, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x05, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x50, 0x42, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0x74, 0x0a, 0x0f, 0x50, 0x53, 0x32, 0x50, 0x4d, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x67, 0x6f,
	0x6f, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x50, 0x42, 0x50, 0x61,
	0x79, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x67, 0x6f, 0x6f, 0x64,
	0x73, 0x12, 0x26, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x50, 0x42, 0x51, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x05, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x37, 0x0a, 0x0e, 0x50, 0x53, 0x32,
	0x50, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x05, 0x67,
	0x6f, 0x6f, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x50, 0x42, 0x50,
	0x61, 0x79, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x67, 0x6f, 0x6f,
	0x64, 0x73, 0x22, 0x36, 0x0a, 0x0c, 0x50, 0x53, 0x32, 0x50, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x26, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x50, 0x42, 0x51, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x05, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x4b, 0x0a, 0x0e, 0x50, 0x32,
	0x50, 0x4d, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x25, 0x0a, 0x05, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x50, 0x42, 0x50, 0x61, 0x79, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x05, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x22, 0x40, 0x0a, 0x0e, 0x50, 0x32, 0x55, 0x53, 0x55,
	0x73, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x6e, 0x61, 0x70, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x22, 0x23, 0x0a, 0x0f, 0x53, 0x32, 0x55,
	0x53, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x58,
	0x0a, 0x12, 0x55, 0x53, 0x32, 0x53, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x53, 0x6e, 0x61, 0x70, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x22, 0x3b, 0x0a, 0x13, 0x53, 0x32, 0x55, 0x53,
	0x53, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x49, 0x66, 0x41, 0x62, 0x73, 0x65, 0x6e, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x2c, 0x0a, 0x16, 0x55, 0x53, 0x32, 0x53, 0x53, 0x65, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x49, 0x66, 0x41, 0x62, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x22, 0x88, 0x01, 0x0a, 0x10, 0x50, 0x32, 0x41, 0x53, 0x41, 0x72, 0x65, 0x6e,
	0x61, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e,
	0x12, 0x1c, 0x0a, 0x09, 0x68, 0x65, 0x61, 0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x68, 0x65, 0x61, 0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x22, 0x59,
	0x0a, 0x10, 0x41, 0x53, 0x32, 0x50, 0x41, 0x72, 0x65, 0x6e, 0x61, 0x47, 0x65, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x2f, 0x0a, 0x09, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x50, 0x42,
	0x41, 0x72, 0x65, 0x6e, 0x61, 0x52, 0x69, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09,
	0x72, 0x69, 0x76, 0x61, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x33, 0x0a, 0x15, 0x50, 0x32, 0x41,
	0x53, 0x41, 0x72, 0x65, 0x6e, 0x61, 0x52, 0x65, 0x71, 0x43, 0x68, 0x61, 0x6c, 0x6c, 0x65, 0x6e,
	0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x49, 0x64, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x49, 0x64, 0x78, 0x22, 0x4e,
	0x0a, 0x18, 0x41, 0x53, 0x32, 0x50, 0x41, 0x72, 0x65, 0x6e, 0x61, 0x43, 0x68, 0x61, 0x6c, 0x6c,
	0x65, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x69,
	0x76, 0x61, 0x6c, 0x49, 0x64, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x69,
	0x76, 0x61, 0x6c, 0x49, 0x64, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x96,
	0x01, 0x0a, 0x0a, 0x50, 0x32, 0x54, 0x53, 0x54, 0x69, 0x70, 0x4f, 0x66, 0x66, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x54, 0x69, 0x70,
	0x4f, 0x66, 0x66, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x54,
	0x69, 0x70, 0x4f, 0x66, 0x66, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x54, 0x69, 0x70,
	0x4f, 0x66, 0x66, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x54, 0x69, 0x70, 0x4f, 0x66, 0x66, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x26, 0x0a, 0x0e, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x22, 0x24, 0x0a, 0x0a, 0x54, 0x53, 0x32, 0x50, 0x54,
	0x69, 0x70, 0x4f, 0x66, 0x66, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x13, 0x0a,
	0x11, 0x50, 0x32, 0x53, 0x42, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x75, 0x66, 0x66, 0x52,
	0x65, 0x71, 0x22, 0x54, 0x0a, 0x11, 0x53, 0x42, 0x32, 0x50, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x42, 0x75, 0x66, 0x66, 0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x50, 0x42, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x75,
	0x66, 0x66, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x38, 0x0a, 0x13, 0x53, 0x42, 0x32, 0x50,
	0x53, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x75, 0x66, 0x66, 0x53, 0x79, 0x6e, 0x63, 0x12,
	0x21, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e,
	0x50, 0x42, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x75, 0x66, 0x66, 0x52, 0x04, 0x69, 0x6e,
	0x66, 0x6f, 0x22, 0x37, 0x0a, 0x12, 0x50, 0x53, 0x32, 0x50, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x42, 0x75, 0x66, 0x66, 0x53, 0x79, 0x6e, 0x63, 0x12, 0x21, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x50, 0x42, 0x53, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x42, 0x75, 0x66, 0x66, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x3c, 0x0a, 0x0e, 0x53,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a,
	0x05, 0x6f, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x6c,
	0x64, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x65, 0x77, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x6e, 0x65, 0x77, 0x49, 0x64, 0x42, 0x25, 0x5a, 0x23, 0x6c, 0x69, 0x74,
	0x65, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x63, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_SSProtocol_proto_rawDescOnce sync.Once
	file_SSProtocol_proto_rawDescData = file_SSProtocol_proto_rawDesc
)

func file_SSProtocol_proto_rawDescGZIP() []byte {
	file_SSProtocol_proto_rawDescOnce.Do(func() {
		file_SSProtocol_proto_rawDescData = protoimpl.X.CompressGZIP(file_SSProtocol_proto_rawDescData)
	})
	return file_SSProtocol_proto_rawDescData
}

var file_SSProtocol_proto_msgTypes = make([]protoimpl.MessageInfo, 72)
var file_SSProtocol_proto_goTypes = []interface{}{
	(*SS_HEARTBEAT_REQUEST)(nil),                // 0: GamePackage.SS_HEARTBEAT_REQUEST
	(*SS_HEARTBEAT_RESPONSE)(nil),               // 1: GamePackage.SS_HEARTBEAT_RESPONSE
	(*P2GCreateGuild)(nil),                      // 2: GamePackage.P2GCreateGuild
	(*P2GApplyJoinGuild)(nil),                   // 3: GamePackage.P2GApplyJoinGuild
	(*P2GJoinGuild)(nil),                        // 4: GamePackage.P2GJoinGuild
	(*P2GFastJoinGuild)(nil),                    // 5: GamePackage.P2GFastJoinGuild
	(*P2GLeaveGuild)(nil),                       // 6: GamePackage.P2GLeaveGuild
	(*P2GDismissGuildReq)(nil),                  // 7: GamePackage.P2GDismissGuildReq
	(*P2GDismissGuild)(nil),                     // 8: GamePackage.P2GDismissGuild
	(*P2GUpdatePosition)(nil),                   // 9: GamePackage.P2GUpdatePosition
	(*P2GUpdateContribution)(nil),               // 10: GamePackage.P2GUpdateContribution
	(*P2GGetRecommendList)(nil),                 // 11: GamePackage.P2GGetRecommendList
	(*P2GGetGuildDetail)(nil),                   // 12: GamePackage.P2GGetGuildDetail
	(*P2GGetApplyList)(nil),                     // 13: GamePackage.P2GGetApplyList
	(*P2GProcessApplication)(nil),               // 14: GamePackage.P2GProcessApplication
	(*P2GMemberAction)(nil),                     // 15: GamePackage.P2GMemberAction
	(*P2GEditGuildInfo)(nil),                    // 16: GamePackage.P2GEditGuildInfo
	(*P2GGMUpdateGuildAttrs)(nil),               // 17: GamePackage.P2GGMUpdateGuildAttrs
	(*P2GGuildDonate)(nil),                      // 18: GamePackage.P2GGuildDonate
	(*G2PGuildCreated)(nil),                     // 19: GamePackage.G2PGuildCreated
	(*G2PCreateGuildRsp)(nil),                   // 20: GamePackage.G2PCreateGuildRsp
	(*G2PJoinGuild)(nil),                        // 21: GamePackage.G2PJoinGuild
	(*G2PJoinGuildRsp)(nil),                     // 22: GamePackage.G2PJoinGuildRsp
	(*G2PLeaveGuild)(nil),                       // 23: GamePackage.G2PLeaveGuild
	(*G2PLeaveGuildRsp)(nil),                    // 24: GamePackage.G2PLeaveGuildRsp
	(*G2PDismissGuildRsp)(nil),                  // 25: GamePackage.G2PDismissGuildRsp
	(*G2PPositionChanged)(nil),                  // 26: GamePackage.G2PPositionChanged
	(*G2PContributionChanged)(nil),              // 27: GamePackage.G2PContributionChanged
	(*G2PGetRecommendListRsp)(nil),              // 28: GamePackage.G2PGetRecommendListRsp
	(*G2PGetGuildDetailRsp)(nil),                // 29: GamePackage.G2PGetGuildDetailRsp
	(*G2PGetApplyListRsp)(nil),                  // 30: GamePackage.G2PGetApplyListRsp
	(*G2PProcessApplicationRsp)(nil),            // 31: GamePackage.G2PProcessApplicationRsp
	(*G2PMemberActionRsp)(nil),                  // 32: GamePackage.G2PMemberActionRsp
	(*G2PEditGuildInfoRsp)(nil),                 // 33: GamePackage.G2PEditGuildInfoRsp
	(*G2PGuildGeneralUpdateNtf)(nil),            // 34: GamePackage.G2PGuildGeneralUpdateNtf
	(*G2PCommonGMRsp)(nil),                      // 35: GamePackage.G2PCommonGMRsp
	(*G2PGuildDonateRsp)(nil),                   // 36: GamePackage.G2PGuildDonateRsp
	(*G2PSUpdateGuildMembersReq)(nil),           // 37: GamePackage.G2PSUpdateGuildMembersReq
	(*PS2GUpdateGuildMembersRsp)(nil),           // 38: GamePackage.PS2GUpdateGuildMembersRsp
	(*G2PSBatchUpdatePlayerGuildStatusReq)(nil), // 39: GamePackage.G2PSBatchUpdatePlayerGuildStatusReq
	(*G2PSNotifyPlayerJoinedGuild)(nil),         // 40: GamePackage.G2PSNotifyPlayerJoinedGuild
	(*G2PSNotifyPlayerLeftGuild)(nil),           // 41: GamePackage.G2PSNotifyPlayerLeftGuild
	(*G2PSNotifyPlayerPositionChanged)(nil),     // 42: GamePackage.G2PSNotifyPlayerPositionChanged
	(*G2PSNotifyRemovePendingApply)(nil),        // 43: GamePackage.G2PSNotifyRemovePendingApply
	(*PS2PUpdateGuildStatusReq)(nil),            // 44: GamePackage.PS2PUpdateGuildStatusReq
	(*P2PSUpdateGuildStatusRsp)(nil),            // 45: GamePackage.P2PSUpdateGuildStatusRsp
	(*PS2PUpdateGuildAffiliationReq)(nil),       // 46: GamePackage.PS2PUpdateGuildAffiliationReq
	(*PS2PRemovePendingApplyReq)(nil),           // 47: GamePackage.PS2PRemovePendingApplyReq
	(*P2PSUpdateGuildAffiliationRsp)(nil),       // 48: GamePackage.P2PSUpdateGuildAffiliationRsp
	(*P2PMCreateOrderReq)(nil),                  // 49: GamePackage.P2PMCreateOrderReq
	(*PM2PCreateOrderRsp)(nil),                  // 50: GamePackage.PM2PCreateOrderRsp
	(*PM2PSDeliverReq)(nil),                     // 51: GamePackage.PM2PSDeliverReq
	(*PS2PMDeliverRsp)(nil),                     // 52: GamePackage.PS2PMDeliverRsp
	(*PS2PDeliverReq)(nil),                      // 53: GamePackage.PS2PDeliverReq
	(*PS2PQuestReq)(nil),                        // 54: GamePackage.PS2PQuestReq
	(*P2PMDeliverRsp)(nil),                      // 55: GamePackage.P2PMDeliverRsp
	(*P2USUserUpdate)(nil),                      // 56: GamePackage.P2USUserUpdate
	(*S2USGetUserData)(nil),                     // 57: GamePackage.S2USGetUserData
	(*US2SGetUserDataRsp)(nil),                  // 58: GamePackage.US2SGetUserDataRsp
	(*S2USSetNameIfAbsent)(nil),                 // 59: GamePackage.S2USSetNameIfAbsent
	(*US2SSetNameIfAbsentRsp)(nil),              // 60: GamePackage.US2SSetNameIfAbsentRsp
	(*P2ASArenaGetData)(nil),                    // 61: GamePackage.P2ASArenaGetData
	(*AS2PArenaGetData)(nil),                    // 62: GamePackage.AS2PArenaGetData
	(*P2ASArenaReqChallenge)(nil),               // 63: GamePackage.P2ASArenaReqChallenge
	(*AS2PArenaChallengeResult)(nil),            // 64: GamePackage.AS2PArenaChallengeResult
	(*P2TSTipOff)(nil),                          // 65: GamePackage.P2TSTipOff
	(*TS2PTipOff)(nil),                          // 66: GamePackage.TS2PTipOff
	(*P2SBSeasonBuffReq)(nil),                   // 67: GamePackage.P2SBSeasonBuffReq
	(*SB2PSeasonBuffRsp)(nil),                   // 68: GamePackage.SB2PSeasonBuffRsp
	(*SB2PSSeasonBuffSync)(nil),                 // 69: GamePackage.SB2PSSeasonBuffSync
	(*PS2PSeasonBuffSync)(nil),                  // 70: GamePackage.PS2PSeasonBuffSync
	(*SeasonResetReq)(nil),                      // 71: GamePackage.SeasonResetReq
	(public.GuildOpt)(0),                        // 72: GuildOpt
	(public.GuildPosition)(0),                   // 73: GuildPosition
	(*public.PBGuildRecommend)(nil),             // 74: PBGuildRecommend
	(*public.PBGuildDetailInfo)(nil),            // 75: PBGuildDetailInfo
	(*public.PBGuildMember)(nil),                // 76: PBGuildMember
	(*public.PBGuildApply)(nil),                 // 77: PBGuildApply
	(public.GuildUpdateType)(0),                 // 78: GuildUpdateType
	(public.GuildSystemInternalActionType)(0),   // 79: GuildSystemInternalActionType
	(*public.PBPayGoodsInfo)(nil),               // 80: PBPayGoodsInfo
	(*public.PBQuestDataInfo)(nil),              // 81: PBQuestDataInfo
	(*public.UserSnapUserInfo)(nil),             // 82: UserSnapUserInfo
	(*public.PBArenaRivalInfo)(nil),             // 83: PBArenaRivalInfo
	(*public.PBSeasonBuff)(nil),                 // 84: PBSeasonBuff
}
var file_SSProtocol_proto_depIdxs = []int32{
	72, // 0: GamePackage.P2GProcessApplication.action:type_name -> GuildOpt
	72, // 1: GamePackage.P2GMemberAction.action:type_name -> GuildOpt
	73, // 2: GamePackage.P2GMemberAction.targetPosition:type_name -> GuildPosition
	72, // 3: GamePackage.P2GEditGuildInfo.opt:type_name -> GuildOpt
	73, // 4: GamePackage.G2PCreateGuildRsp.position:type_name -> GuildPosition
	73, // 5: GamePackage.G2PJoinGuildRsp.position:type_name -> GuildPosition
	74, // 6: GamePackage.G2PGetRecommendListRsp.guildList:type_name -> PBGuildRecommend
	75, // 7: GamePackage.G2PGetGuildDetailRsp.guild_info:type_name -> PBGuildDetailInfo
	76, // 8: GamePackage.G2PGetGuildDetailRsp.member_list:type_name -> PBGuildMember
	73, // 9: GamePackage.G2PGetGuildDetailRsp.player_guild_position:type_name -> GuildPosition
	77, // 10: GamePackage.G2PGetApplyListRsp.applyList:type_name -> PBGuildApply
	72, // 11: GamePackage.G2PProcessApplicationRsp.opt:type_name -> GuildOpt
	76, // 12: GamePackage.G2PProcessApplicationRsp.member:type_name -> PBGuildMember
	72, // 13: GamePackage.G2PMemberActionRsp.action:type_name -> GuildOpt
	76, // 14: GamePackage.G2PMemberActionRsp.member:type_name -> PBGuildMember
	72, // 15: GamePackage.G2PEditGuildInfoRsp.opt:type_name -> GuildOpt
	78, // 16: GamePackage.G2PGuildGeneralUpdateNtf.updateType:type_name -> GuildUpdateType
	73, // 17: GamePackage.G2PGuildGeneralUpdateNtf.newPosition:type_name -> GuildPosition
	72, // 18: GamePackage.G2PSUpdateGuildMembersReq.opt:type_name -> GuildOpt
	79, // 19: GamePackage.G2PSBatchUpdatePlayerGuildStatusReq.actionType:type_name -> GuildSystemInternalActionType
	73, // 20: GamePackage.G2PSNotifyPlayerJoinedGuild.position:type_name -> GuildPosition
	73, // 21: GamePackage.G2PSNotifyPlayerPositionChanged.newPosition:type_name -> GuildPosition
	72, // 22: GamePackage.G2PSNotifyPlayerPositionChanged.opt:type_name -> GuildOpt
	72, // 23: GamePackage.G2PSNotifyRemovePendingApply.opt:type_name -> GuildOpt
	72, // 24: GamePackage.PS2PUpdateGuildStatusReq.opt:type_name -> GuildOpt
	73, // 25: GamePackage.PS2PUpdateGuildAffiliationReq.position:type_name -> GuildPosition
	72, // 26: GamePackage.PS2PUpdateGuildAffiliationReq.opt:type_name -> GuildOpt
	72, // 27: GamePackage.PS2PRemovePendingApplyReq.opt:type_name -> GuildOpt
	80, // 28: GamePackage.PM2PSDeliverReq.goods:type_name -> PBPayGoodsInfo
	81, // 29: GamePackage.PM2PSDeliverReq.quest:type_name -> PBQuestDataInfo
	80, // 30: GamePackage.PS2PMDeliverRsp.goods:type_name -> PBPayGoodsInfo
	81, // 31: GamePackage.PS2PMDeliverRsp.quest:type_name -> PBQuestDataInfo
	80, // 32: GamePackage.PS2PDeliverReq.goods:type_name -> PBPayGoodsInfo
	81, // 33: GamePackage.PS2PQuestReq.quest:type_name -> PBQuestDataInfo
	80, // 34: GamePackage.P2PMDeliverRsp.goods:type_name -> PBPayGoodsInfo
	82, // 35: GamePackage.P2USUserUpdate.user_data:type_name -> UserSnapUserInfo
	82, // 36: GamePackage.US2SGetUserDataRsp.user_data:type_name -> UserSnapUserInfo
	83, // 37: GamePackage.AS2PArenaGetData.rivalList:type_name -> PBArenaRivalInfo
	84, // 38: GamePackage.SB2PSeasonBuffRsp.info:type_name -> PBSeasonBuff
	84, // 39: GamePackage.SB2PSSeasonBuffSync.info:type_name -> PBSeasonBuff
	84, // 40: GamePackage.PS2PSeasonBuffSync.info:type_name -> PBSeasonBuff
	41, // [41:41] is the sub-list for method output_type
	41, // [41:41] is the sub-list for method input_type
	41, // [41:41] is the sub-list for extension type_name
	41, // [41:41] is the sub-list for extension extendee
	0,  // [0:41] is the sub-list for field type_name
}

func init() { file_SSProtocol_proto_init() }
func file_SSProtocol_proto_init() {
	if File_SSProtocol_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_SSProtocol_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SS_HEARTBEAT_REQUEST); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SS_HEARTBEAT_RESPONSE); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2GCreateGuild); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2GApplyJoinGuild); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2GJoinGuild); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2GFastJoinGuild); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2GLeaveGuild); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2GDismissGuildReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2GDismissGuild); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2GUpdatePosition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2GUpdateContribution); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2GGetRecommendList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2GGetGuildDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2GGetApplyList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2GProcessApplication); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2GMemberAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2GEditGuildInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2GGMUpdateGuildAttrs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2GGuildDonate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PGuildCreated); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PCreateGuildRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PJoinGuild); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PJoinGuildRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PLeaveGuild); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PLeaveGuildRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PDismissGuildRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PPositionChanged); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PContributionChanged); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PGetRecommendListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PGetGuildDetailRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PGetApplyListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PProcessApplicationRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PMemberActionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PEditGuildInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PGuildGeneralUpdateNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PCommonGMRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PGuildDonateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PSUpdateGuildMembersReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PS2GUpdateGuildMembersRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PSBatchUpdatePlayerGuildStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PSNotifyPlayerJoinedGuild); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PSNotifyPlayerLeftGuild); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PSNotifyPlayerPositionChanged); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2PSNotifyRemovePendingApply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PS2PUpdateGuildStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2PSUpdateGuildStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PS2PUpdateGuildAffiliationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PS2PRemovePendingApplyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2PSUpdateGuildAffiliationRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2PMCreateOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PM2PCreateOrderRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PM2PSDeliverReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PS2PMDeliverRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PS2PDeliverReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PS2PQuestReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2PMDeliverRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2USUserUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2USGetUserData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*US2SGetUserDataRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2USSetNameIfAbsent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*US2SSetNameIfAbsentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2ASArenaGetData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AS2PArenaGetData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2ASArenaReqChallenge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AS2PArenaChallengeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2TSTipOff); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TS2PTipOff); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2SBSeasonBuffReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SB2PSeasonBuffRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SB2PSSeasonBuffSync); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PS2PSeasonBuffSync); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_SSProtocol_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeasonResetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_SSProtocol_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   72,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_SSProtocol_proto_goTypes,
		DependencyIndexes: file_SSProtocol_proto_depIdxs,
		MessageInfos:      file_SSProtocol_proto_msgTypes,
	}.Build()
	File_SSProtocol_proto = out.File
	file_SSProtocol_proto_rawDesc = nil
	file_SSProtocol_proto_goTypes = nil
	file_SSProtocol_proto_depIdxs = nil
}
