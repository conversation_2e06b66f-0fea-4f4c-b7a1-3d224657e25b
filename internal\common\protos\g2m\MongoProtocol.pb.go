// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.16.0
// source: MongoProtocol.proto

package g2m

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	dbstruct "liteframe/internal/common/protos/dbstruct"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type G2M_LoadOrCreateUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account  string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	ServerId uint64 `protobuf:"varint,2,opt,name=server_id,json=serverId,proto3" json:"server_id,omitempty"`
	Uid      uint64 `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *G2M_LoadOrCreateUser) Reset() {
	*x = G2M_LoadOrCreateUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_LoadOrCreateUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_LoadOrCreateUser) ProtoMessage() {}

func (x *G2M_LoadOrCreateUser) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_LoadOrCreateUser.ProtoReflect.Descriptor instead.
func (*G2M_LoadOrCreateUser) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{0}
}

func (x *G2M_LoadOrCreateUser) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *G2M_LoadOrCreateUser) GetServerId() uint64 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *G2M_LoadOrCreateUser) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

type M2G_LoadOrCreateUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	User  *dbstruct.UserDB `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	IsNew bool             `protobuf:"varint,2,opt,name=isNew,proto3" json:"isNew,omitempty"`
	Code  uint32           `protobuf:"varint,3,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *M2G_LoadOrCreateUser) Reset() {
	*x = M2G_LoadOrCreateUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *M2G_LoadOrCreateUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*M2G_LoadOrCreateUser) ProtoMessage() {}

func (x *M2G_LoadOrCreateUser) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use M2G_LoadOrCreateUser.ProtoReflect.Descriptor instead.
func (*M2G_LoadOrCreateUser) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{1}
}

func (x *M2G_LoadOrCreateUser) GetUser() *dbstruct.UserDB {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *M2G_LoadOrCreateUser) GetIsNew() bool {
	if x != nil {
		return x.IsNew
	}
	return false
}

func (x *M2G_LoadOrCreateUser) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type G2M_SaveUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid  uint64           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"` // 按用户 id 查找
	Data [][]byte         `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`
	User *dbstruct.UserDB `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
}

func (x *G2M_SaveUser) Reset() {
	*x = G2M_SaveUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_SaveUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_SaveUser) ProtoMessage() {}

func (x *G2M_SaveUser) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_SaveUser.ProtoReflect.Descriptor instead.
func (*G2M_SaveUser) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{2}
}

func (x *G2M_SaveUser) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *G2M_SaveUser) GetData() [][]byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *G2M_SaveUser) GetUser() *dbstruct.UserDB {
	if x != nil {
		return x.User
	}
	return nil
}

// 加载用户数据
type G2M_LoadUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`       // 按用户名查找，如果名字为空就按 id 查找
	Uid      uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`        // 按用户 id 查找
	Account  string `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"` // 按账户查找
	ServerId uint64 `protobuf:"varint,4,opt,name=server_id,json=serverId,proto3" json:"server_id,omitempty"`
}

func (x *G2M_LoadUser) Reset() {
	*x = G2M_LoadUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_LoadUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_LoadUser) ProtoMessage() {}

func (x *G2M_LoadUser) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_LoadUser.ProtoReflect.Descriptor instead.
func (*G2M_LoadUser) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{3}
}

func (x *G2M_LoadUser) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *G2M_LoadUser) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *G2M_LoadUser) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *G2M_LoadUser) GetServerId() uint64 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

// 加载用户数据返回值
type M2G_LoadUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	User *dbstruct.UserDB `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
}

func (x *M2G_LoadUser) Reset() {
	*x = M2G_LoadUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *M2G_LoadUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*M2G_LoadUser) ProtoMessage() {}

func (x *M2G_LoadUser) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use M2G_LoadUser.ProtoReflect.Descriptor instead.
func (*M2G_LoadUser) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{4}
}

func (x *M2G_LoadUser) GetUser() *dbstruct.UserDB {
	if x != nil {
		return x.User
	}
	return nil
}

// 根据账号获得或创建 uid
type G2M_LoadCreateUid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account  string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	ServerId uint64 `protobuf:"varint,2,opt,name=server_id,json=serverId,proto3" json:"server_id,omitempty"`
}

func (x *G2M_LoadCreateUid) Reset() {
	*x = G2M_LoadCreateUid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_LoadCreateUid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_LoadCreateUid) ProtoMessage() {}

func (x *G2M_LoadCreateUid) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_LoadCreateUid.ProtoReflect.Descriptor instead.
func (*G2M_LoadCreateUid) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{5}
}

func (x *G2M_LoadCreateUid) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *G2M_LoadCreateUid) GetServerId() uint64 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

type M2G_LoadCreateUid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid  uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Code uint32 `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *M2G_LoadCreateUid) Reset() {
	*x = M2G_LoadCreateUid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *M2G_LoadCreateUid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*M2G_LoadCreateUid) ProtoMessage() {}

func (x *M2G_LoadCreateUid) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use M2G_LoadCreateUid.ProtoReflect.Descriptor instead.
func (*M2G_LoadCreateUid) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{6}
}

func (x *M2G_LoadCreateUid) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *M2G_LoadCreateUid) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

// 通用数据操作
type G2M_SaveData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServerId uint64 `protobuf:"varint,1,opt,name=server_id,json=serverId,proto3" json:"server_id,omitempty"`
	Data     []byte `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *G2M_SaveData) Reset() {
	*x = G2M_SaveData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_SaveData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_SaveData) ProtoMessage() {}

func (x *G2M_SaveData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_SaveData.ProtoReflect.Descriptor instead.
func (*G2M_SaveData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{7}
}

func (x *G2M_SaveData) GetServerId() uint64 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *G2M_SaveData) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type G2M_LoadData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServerId uint64 `protobuf:"varint,1,opt,name=server_id,json=serverId,proto3" json:"server_id,omitempty"`
}

func (x *G2M_LoadData) Reset() {
	*x = G2M_LoadData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_LoadData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_LoadData) ProtoMessage() {}

func (x *G2M_LoadData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_LoadData.ProtoReflect.Descriptor instead.
func (*G2M_LoadData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{8}
}

func (x *G2M_LoadData) GetServerId() uint64 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

type M2G_LoadData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []byte `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *M2G_LoadData) Reset() {
	*x = M2G_LoadData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *M2G_LoadData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*M2G_LoadData) ProtoMessage() {}

func (x *M2G_LoadData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use M2G_LoadData.ProtoReflect.Descriptor instead.
func (*M2G_LoadData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{9}
}

func (x *M2G_LoadData) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type G2M_DeleteData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServerId uint64 `protobuf:"varint,1,opt,name=server_id,json=serverId,proto3" json:"server_id,omitempty"`
}

func (x *G2M_DeleteData) Reset() {
	*x = G2M_DeleteData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_DeleteData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_DeleteData) ProtoMessage() {}

func (x *G2M_DeleteData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_DeleteData.ProtoReflect.Descriptor instead.
func (*G2M_DeleteData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{10}
}

func (x *G2M_DeleteData) GetServerId() uint64 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

type G2M_FindDuplicateNamesData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *G2M_FindDuplicateNamesData) Reset() {
	*x = G2M_FindDuplicateNamesData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_FindDuplicateNamesData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_FindDuplicateNamesData) ProtoMessage() {}

func (x *G2M_FindDuplicateNamesData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_FindDuplicateNamesData.ProtoReflect.Descriptor instead.
func (*G2M_FindDuplicateNamesData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{11}
}

func (x *G2M_FindDuplicateNamesData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type M2G_FindDuplicateNamesData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Num int64 `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"`
}

func (x *M2G_FindDuplicateNamesData) Reset() {
	*x = M2G_FindDuplicateNamesData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *M2G_FindDuplicateNamesData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*M2G_FindDuplicateNamesData) ProtoMessage() {}

func (x *M2G_FindDuplicateNamesData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use M2G_FindDuplicateNamesData.ProtoReflect.Descriptor instead.
func (*M2G_FindDuplicateNamesData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{12}
}

func (x *M2G_FindDuplicateNamesData) GetNum() int64 {
	if x != nil {
		return x.Num
	}
	return 0
}

type G2M_ChangeNameData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid     uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name    string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	OldName string `protobuf:"bytes,3,opt,name=old_name,json=oldName,proto3" json:"old_name,omitempty"`
}

func (x *G2M_ChangeNameData) Reset() {
	*x = G2M_ChangeNameData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_ChangeNameData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_ChangeNameData) ProtoMessage() {}

func (x *G2M_ChangeNameData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_ChangeNameData.ProtoReflect.Descriptor instead.
func (*G2M_ChangeNameData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{13}
}

func (x *G2M_ChangeNameData) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *G2M_ChangeNameData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *G2M_ChangeNameData) GetOldName() string {
	if x != nil {
		return x.OldName
	}
	return ""
}

// 加载所有公会数据
type G2M_LoadGuildData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *G2M_LoadGuildData) Reset() {
	*x = G2M_LoadGuildData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_LoadGuildData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_LoadGuildData) ProtoMessage() {}

func (x *G2M_LoadGuildData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_LoadGuildData.ProtoReflect.Descriptor instead.
func (*G2M_LoadGuildData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{14}
}

type M2G_LoadGuildData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code      uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	GuildData []byte `protobuf:"bytes,2,opt,name=guild_data,json=guildData,proto3" json:"guild_data,omitempty"` // 序列化的整个GuildDB数据
}

func (x *M2G_LoadGuildData) Reset() {
	*x = M2G_LoadGuildData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *M2G_LoadGuildData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*M2G_LoadGuildData) ProtoMessage() {}

func (x *M2G_LoadGuildData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use M2G_LoadGuildData.ProtoReflect.Descriptor instead.
func (*M2G_LoadGuildData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{15}
}

func (x *M2G_LoadGuildData) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *M2G_LoadGuildData) GetGuildData() []byte {
	if x != nil {
		return x.GuildData
	}
	return nil
}

// 存储单个公会数据
type G2M_SaveGuildData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId   int64  `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildData []byte `protobuf:"bytes,2,opt,name=guild_data,json=guildData,proto3" json:"guild_data,omitempty"` // 序列化的单个GuildData数据
}

func (x *G2M_SaveGuildData) Reset() {
	*x = G2M_SaveGuildData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_SaveGuildData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_SaveGuildData) ProtoMessage() {}

func (x *G2M_SaveGuildData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_SaveGuildData.ProtoReflect.Descriptor instead.
func (*G2M_SaveGuildData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{16}
}

func (x *G2M_SaveGuildData) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *G2M_SaveGuildData) GetGuildData() []byte {
	if x != nil {
		return x.GuildData
	}
	return nil
}

type M2G_SaveGuildData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *M2G_SaveGuildData) Reset() {
	*x = M2G_SaveGuildData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *M2G_SaveGuildData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*M2G_SaveGuildData) ProtoMessage() {}

func (x *M2G_SaveGuildData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use M2G_SaveGuildData.ProtoReflect.Descriptor instead.
func (*M2G_SaveGuildData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{17}
}

func (x *M2G_SaveGuildData) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

// 删除指定公会数据
type G2M_DeleteGuildData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildId int64 `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
}

func (x *G2M_DeleteGuildData) Reset() {
	*x = G2M_DeleteGuildData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_DeleteGuildData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_DeleteGuildData) ProtoMessage() {}

func (x *G2M_DeleteGuildData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_DeleteGuildData.ProtoReflect.Descriptor instead.
func (*G2M_DeleteGuildData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{18}
}

func (x *G2M_DeleteGuildData) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

type M2G_DeleteGuildData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *M2G_DeleteGuildData) Reset() {
	*x = M2G_DeleteGuildData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *M2G_DeleteGuildData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*M2G_DeleteGuildData) ProtoMessage() {}

func (x *M2G_DeleteGuildData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use M2G_DeleteGuildData.ProtoReflect.Descriptor instead.
func (*M2G_DeleteGuildData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{19}
}

func (x *M2G_DeleteGuildData) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

// 加载支付数据
type G2M_LoadPaymentData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *G2M_LoadPaymentData) Reset() {
	*x = G2M_LoadPaymentData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_LoadPaymentData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_LoadPaymentData) ProtoMessage() {}

func (x *G2M_LoadPaymentData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_LoadPaymentData.ProtoReflect.Descriptor instead.
func (*G2M_LoadPaymentData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{20}
}

type M2G_LoadPaymentData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code        uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	PaymentData []byte `protobuf:"bytes,2,opt,name=payment_data,json=paymentData,proto3" json:"payment_data,omitempty"` // 序列化的整个PaymentDB数据
}

func (x *M2G_LoadPaymentData) Reset() {
	*x = M2G_LoadPaymentData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *M2G_LoadPaymentData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*M2G_LoadPaymentData) ProtoMessage() {}

func (x *M2G_LoadPaymentData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use M2G_LoadPaymentData.ProtoReflect.Descriptor instead.
func (*M2G_LoadPaymentData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{21}
}

func (x *M2G_LoadPaymentData) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *M2G_LoadPaymentData) GetPaymentData() []byte {
	if x != nil {
		return x.PaymentData
	}
	return nil
}

// 存储支付数据
type G2M_SavePaymentData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderKey    string `protobuf:"bytes,1,opt,name=order_key,json=orderKey,proto3" json:"order_key,omitempty"`
	PaymentData []byte `protobuf:"bytes,2,opt,name=payment_data,json=paymentData,proto3" json:"payment_data,omitempty"` // 序列化的PaymentDB数据
}

func (x *G2M_SavePaymentData) Reset() {
	*x = G2M_SavePaymentData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_SavePaymentData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_SavePaymentData) ProtoMessage() {}

func (x *G2M_SavePaymentData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_SavePaymentData.ProtoReflect.Descriptor instead.
func (*G2M_SavePaymentData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{22}
}

func (x *G2M_SavePaymentData) GetOrderKey() string {
	if x != nil {
		return x.OrderKey
	}
	return ""
}

func (x *G2M_SavePaymentData) GetPaymentData() []byte {
	if x != nil {
		return x.PaymentData
	}
	return nil
}

type M2G_SavePaymentData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *M2G_SavePaymentData) Reset() {
	*x = M2G_SavePaymentData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *M2G_SavePaymentData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*M2G_SavePaymentData) ProtoMessage() {}

func (x *M2G_SavePaymentData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use M2G_SavePaymentData.ProtoReflect.Descriptor instead.
func (*M2G_SavePaymentData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{23}
}

func (x *M2G_SavePaymentData) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

// 分批次加载玩家数据
type G2M_LoadUserSnapDataBatch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LastUid uint64 `protobuf:"varint,1,opt,name=last_uid,json=lastUid,proto3" json:"last_uid,omitempty"`
	Limit   int64  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *G2M_LoadUserSnapDataBatch) Reset() {
	*x = G2M_LoadUserSnapDataBatch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_LoadUserSnapDataBatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_LoadUserSnapDataBatch) ProtoMessage() {}

func (x *G2M_LoadUserSnapDataBatch) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_LoadUserSnapDataBatch.ProtoReflect.Descriptor instead.
func (*G2M_LoadUserSnapDataBatch) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{24}
}

func (x *G2M_LoadUserSnapDataBatch) GetLastUid() uint64 {
	if x != nil {
		return x.LastUid
	}
	return 0
}

func (x *G2M_LoadUserSnapDataBatch) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

// 加载所有玩家数据
type M2G_LoadUserSnapDataBatch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code     uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	UserData []byte `protobuf:"bytes,2,opt,name=user_data,json=userData,proto3" json:"user_data,omitempty"` // 序列化的整个UserData数据
	LastUid  uint64 `protobuf:"varint,3,opt,name=last_uid,json=lastUid,proto3" json:"last_uid,omitempty"`
	HasMore  bool   `protobuf:"varint,4,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
}

func (x *M2G_LoadUserSnapDataBatch) Reset() {
	*x = M2G_LoadUserSnapDataBatch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *M2G_LoadUserSnapDataBatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*M2G_LoadUserSnapDataBatch) ProtoMessage() {}

func (x *M2G_LoadUserSnapDataBatch) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use M2G_LoadUserSnapDataBatch.ProtoReflect.Descriptor instead.
func (*M2G_LoadUserSnapDataBatch) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{25}
}

func (x *M2G_LoadUserSnapDataBatch) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *M2G_LoadUserSnapDataBatch) GetUserData() []byte {
	if x != nil {
		return x.UserData
	}
	return nil
}

func (x *M2G_LoadUserSnapDataBatch) GetLastUid() uint64 {
	if x != nil {
		return x.LastUid
	}
	return 0
}

func (x *M2G_LoadUserSnapDataBatch) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

type G2M_LoadAllAccountMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServerId uint64 `protobuf:"varint,1,opt,name=server_id,json=serverId,proto3" json:"server_id,omitempty"`
}

func (x *G2M_LoadAllAccountMap) Reset() {
	*x = G2M_LoadAllAccountMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_LoadAllAccountMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_LoadAllAccountMap) ProtoMessage() {}

func (x *G2M_LoadAllAccountMap) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_LoadAllAccountMap.ProtoReflect.Descriptor instead.
func (*G2M_LoadAllAccountMap) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{26}
}

func (x *G2M_LoadAllAccountMap) GetServerId() uint64 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

type M2G_LoadAllAccountMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code       uint32            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	AccountMap map[string]uint64 `protobuf:"bytes,2,rep,name=account_map,json=accountMap,proto3" json:"account_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 序列化的整个AccountMap数据
}

func (x *M2G_LoadAllAccountMap) Reset() {
	*x = M2G_LoadAllAccountMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *M2G_LoadAllAccountMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*M2G_LoadAllAccountMap) ProtoMessage() {}

func (x *M2G_LoadAllAccountMap) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use M2G_LoadAllAccountMap.ProtoReflect.Descriptor instead.
func (*M2G_LoadAllAccountMap) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{27}
}

func (x *M2G_LoadAllAccountMap) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *M2G_LoadAllAccountMap) GetAccountMap() map[string]uint64 {
	if x != nil {
		return x.AccountMap
	}
	return nil
}

// 加载举报数据
type G2M_LoadTipOffData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *G2M_LoadTipOffData) Reset() {
	*x = G2M_LoadTipOffData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_LoadTipOffData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_LoadTipOffData) ProtoMessage() {}

func (x *G2M_LoadTipOffData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_LoadTipOffData.ProtoReflect.Descriptor instead.
func (*G2M_LoadTipOffData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{28}
}

type M2G_LoadTipOffData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code       uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	TipOffData []byte `protobuf:"bytes,2,opt,name=tipOff_data,json=tipOffData,proto3" json:"tipOff_data,omitempty"` // 序列化的整个TipOffDat数据
}

func (x *M2G_LoadTipOffData) Reset() {
	*x = M2G_LoadTipOffData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *M2G_LoadTipOffData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*M2G_LoadTipOffData) ProtoMessage() {}

func (x *M2G_LoadTipOffData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use M2G_LoadTipOffData.ProtoReflect.Descriptor instead.
func (*M2G_LoadTipOffData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{29}
}

func (x *M2G_LoadTipOffData) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *M2G_LoadTipOffData) GetTipOffData() []byte {
	if x != nil {
		return x.TipOffData
	}
	return nil
}

// 存储举报数据
type G2M_SaveTipOffData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TipOffData []byte `protobuf:"bytes,2,opt,name=tipOff_data,json=tipOffData,proto3" json:"tipOff_data,omitempty"` // 序列化的TipOffDat数据
}

func (x *G2M_SaveTipOffData) Reset() {
	*x = G2M_SaveTipOffData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_SaveTipOffData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_SaveTipOffData) ProtoMessage() {}

func (x *G2M_SaveTipOffData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_SaveTipOffData.ProtoReflect.Descriptor instead.
func (*G2M_SaveTipOffData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{30}
}

func (x *G2M_SaveTipOffData) GetTipOffData() []byte {
	if x != nil {
		return x.TipOffData
	}
	return nil
}

type M2G_SaveTipOffData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *M2G_SaveTipOffData) Reset() {
	*x = M2G_SaveTipOffData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *M2G_SaveTipOffData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*M2G_SaveTipOffData) ProtoMessage() {}

func (x *M2G_SaveTipOffData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use M2G_SaveTipOffData.ProtoReflect.Descriptor instead.
func (*M2G_SaveTipOffData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{31}
}

func (x *M2G_SaveTipOffData) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

// 加载赛季buff数据
type G2M_LoadSeasonBuffData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *G2M_LoadSeasonBuffData) Reset() {
	*x = G2M_LoadSeasonBuffData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_LoadSeasonBuffData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_LoadSeasonBuffData) ProtoMessage() {}

func (x *G2M_LoadSeasonBuffData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_LoadSeasonBuffData.ProtoReflect.Descriptor instead.
func (*G2M_LoadSeasonBuffData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{32}
}

type M2G_LoadSeasonBuffData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code           uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	SeasonBuffData []byte `protobuf:"bytes,2,opt,name=seasonBuff_data,json=seasonBuffData,proto3" json:"seasonBuff_data,omitempty"` // 序列化的整个赛季buff数据
}

func (x *M2G_LoadSeasonBuffData) Reset() {
	*x = M2G_LoadSeasonBuffData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *M2G_LoadSeasonBuffData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*M2G_LoadSeasonBuffData) ProtoMessage() {}

func (x *M2G_LoadSeasonBuffData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use M2G_LoadSeasonBuffData.ProtoReflect.Descriptor instead.
func (*M2G_LoadSeasonBuffData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{33}
}

func (x *M2G_LoadSeasonBuffData) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *M2G_LoadSeasonBuffData) GetSeasonBuffData() []byte {
	if x != nil {
		return x.SeasonBuffData
	}
	return nil
}

// 存储赛季buff数据
type G2M_SaveSeasonBuffData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeasonBuffData []byte `protobuf:"bytes,2,opt,name=seasonBuff_data,json=seasonBuffData,proto3" json:"seasonBuff_data,omitempty"` // 序列化的整个赛季buff数据
}

func (x *G2M_SaveSeasonBuffData) Reset() {
	*x = G2M_SaveSeasonBuffData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *G2M_SaveSeasonBuffData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2M_SaveSeasonBuffData) ProtoMessage() {}

func (x *G2M_SaveSeasonBuffData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2M_SaveSeasonBuffData.ProtoReflect.Descriptor instead.
func (*G2M_SaveSeasonBuffData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{34}
}

func (x *G2M_SaveSeasonBuffData) GetSeasonBuffData() []byte {
	if x != nil {
		return x.SeasonBuffData
	}
	return nil
}

type M2G_SaveSeasonBuffData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *M2G_SaveSeasonBuffData) Reset() {
	*x = M2G_SaveSeasonBuffData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MongoProtocol_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *M2G_SaveSeasonBuffData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*M2G_SaveSeasonBuffData) ProtoMessage() {}

func (x *M2G_SaveSeasonBuffData) ProtoReflect() protoreflect.Message {
	mi := &file_MongoProtocol_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use M2G_SaveSeasonBuffData.ProtoReflect.Descriptor instead.
func (*M2G_SaveSeasonBuffData) Descriptor() ([]byte, []int) {
	return file_MongoProtocol_proto_rawDescGZIP(), []int{35}
}

func (x *M2G_SaveSeasonBuffData) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_MongoProtocol_proto protoreflect.FileDescriptor

var file_MongoProtocol_proto_rawDesc = []byte{
	0x0a, 0x13, 0x4d, 0x6f, 0x6e, 0x67, 0x6f, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x67, 0x32, 0x6d, 0x1a, 0x10, 0x44, 0x42, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5f, 0x0a, 0x14,
	0x47, 0x32, 0x4d, 0x5f, 0x4c, 0x6f, 0x61, 0x64, 0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x66, 0x0a,
	0x14, 0x4d, 0x32, 0x47, 0x5f, 0x4c, 0x6f, 0x61, 0x64, 0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x62, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x44, 0x42, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x69,
	0x73, 0x4e, 0x65, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x4e, 0x65,
	0x77, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x5a, 0x0a, 0x0c, 0x47, 0x32, 0x4d, 0x5f, 0x53, 0x61, 0x76,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x24, 0x0a, 0x04, 0x75,
	0x73, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x62, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x42, 0x52, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x22, 0x6b, 0x0a, 0x0c, 0x47, 0x32, 0x4d, 0x5f, 0x4c, 0x6f, 0x61, 0x64, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x22, 0x34,
	0x0a, 0x0c, 0x4d, 0x32, 0x47, 0x5f, 0x4c, 0x6f, 0x61, 0x64, 0x55, 0x73, 0x65, 0x72, 0x12, 0x24,
	0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64,
	0x62, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x42, 0x52, 0x04,
	0x75, 0x73, 0x65, 0x72, 0x22, 0x4a, 0x0a, 0x11, 0x47, 0x32, 0x4d, 0x5f, 0x4c, 0x6f, 0x61, 0x64,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64,
	0x22, 0x39, 0x0a, 0x11, 0x4d, 0x32, 0x47, 0x5f, 0x4c, 0x6f, 0x61, 0x64, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x55, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x3f, 0x0a, 0x0c, 0x47,
	0x32, 0x4d, 0x5f, 0x53, 0x61, 0x76, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x2b, 0x0a, 0x0c,
	0x47, 0x32, 0x4d, 0x5f, 0x4c, 0x6f, 0x61, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x22, 0x22, 0x0a, 0x0c, 0x4d, 0x32, 0x47,
	0x5f, 0x4c, 0x6f, 0x61, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x2d, 0x0a,
	0x0e, 0x47, 0x32, 0x4d, 0x5f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x1b, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x22, 0x30, 0x0a, 0x1a,
	0x47, 0x32, 0x4d, 0x5f, 0x46, 0x69, 0x6e, 0x64, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x2e,
	0x0a, 0x1a, 0x4d, 0x32, 0x47, 0x5f, 0x46, 0x69, 0x6e, 0x64, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03,
	0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0x55,
	0x0a, 0x12, 0x47, 0x32, 0x4d, 0x5f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x6c,
	0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x6c,
	0x64, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x13, 0x0a, 0x11, 0x47, 0x32, 0x4d, 0x5f, 0x4c, 0x6f, 0x61,
	0x64, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x44, 0x61, 0x74, 0x61, 0x22, 0x46, 0x0a, 0x11, 0x4d, 0x32,
	0x47, 0x5f, 0x4c, 0x6f, 0x61, 0x64, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x44, 0x61,
	0x74, 0x61, 0x22, 0x4d, 0x0a, 0x11, 0x47, 0x32, 0x4d, 0x5f, 0x53, 0x61, 0x76, 0x65, 0x47, 0x75,
	0x69, 0x6c, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x75, 0x69, 0x6c, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x44, 0x61, 0x74,
	0x61, 0x22, 0x27, 0x0a, 0x11, 0x4d, 0x32, 0x47, 0x5f, 0x53, 0x61, 0x76, 0x65, 0x47, 0x75, 0x69,
	0x6c, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x30, 0x0a, 0x13, 0x47, 0x32,
	0x4d, 0x5f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x22, 0x29, 0x0a, 0x13,
	0x4d, 0x32, 0x47, 0x5f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x15, 0x0a, 0x13, 0x47, 0x32, 0x4d, 0x5f, 0x4c,
	0x6f, 0x61, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x22, 0x4c,
	0x0a, 0x13, 0x4d, 0x32, 0x47, 0x5f, 0x4c, 0x6f, 0x61, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x22, 0x55, 0x0a, 0x13,
	0x47, 0x32, 0x4d, 0x5f, 0x53, 0x61, 0x76, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4b, 0x65, 0x79,
	0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x22, 0x29, 0x0a, 0x13, 0x4d, 0x32, 0x47, 0x5f, 0x53, 0x61, 0x76, 0x65, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x4c,
	0x0a, 0x19, 0x47, 0x32, 0x4d, 0x5f, 0x4c, 0x6f, 0x61, 0x64, 0x55, 0x73, 0x65, 0x72, 0x53, 0x6e,
	0x61, 0x70, 0x44, 0x61, 0x74, 0x61, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6c,
	0x61, 0x73, 0x74, 0x55, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x82, 0x01, 0x0a,
	0x19, 0x4d, 0x32, 0x47, 0x5f, 0x4c, 0x6f, 0x61, 0x64, 0x55, 0x73, 0x65, 0x72, 0x53, 0x6e, 0x61,
	0x70, 0x44, 0x61, 0x74, 0x61, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6c,
	0x61, 0x73, 0x74, 0x55, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x5f, 0x6d, 0x6f,
	0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72,
	0x65, 0x22, 0x34, 0x0a, 0x15, 0x47, 0x32, 0x4d, 0x5f, 0x4c, 0x6f, 0x61, 0x64, 0x41, 0x6c, 0x6c,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x22, 0xb7, 0x01, 0x0a, 0x15, 0x4d, 0x32, 0x47, 0x5f,
	0x4c, 0x6f, 0x61, 0x64, 0x41, 0x6c, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61,
	0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x4b, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x67, 0x32, 0x6d,
	0x2e, 0x4d, 0x32, 0x47, 0x5f, 0x4c, 0x6f, 0x61, 0x64, 0x41, 0x6c, 0x6c, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61,
	0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d,
	0x61, 0x70, 0x1a, 0x3d, 0x0a, 0x0f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x14, 0x0a, 0x12, 0x47, 0x32, 0x4d, 0x5f, 0x4c, 0x6f, 0x61, 0x64, 0x54, 0x69, 0x70,
	0x4f, 0x66, 0x66, 0x44, 0x61, 0x74, 0x61, 0x22, 0x49, 0x0a, 0x12, 0x4d, 0x32, 0x47, 0x5f, 0x4c,
	0x6f, 0x61, 0x64, 0x54, 0x69, 0x70, 0x4f, 0x66, 0x66, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x4f, 0x66, 0x66, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x74, 0x69, 0x70, 0x4f, 0x66, 0x66, 0x44, 0x61,
	0x74, 0x61, 0x22, 0x35, 0x0a, 0x12, 0x47, 0x32, 0x4d, 0x5f, 0x53, 0x61, 0x76, 0x65, 0x54, 0x69,
	0x70, 0x4f, 0x66, 0x66, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x4f,
	0x66, 0x66, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x74,
	0x69, 0x70, 0x4f, 0x66, 0x66, 0x44, 0x61, 0x74, 0x61, 0x22, 0x28, 0x0a, 0x12, 0x4d, 0x32, 0x47,
	0x5f, 0x53, 0x61, 0x76, 0x65, 0x54, 0x69, 0x70, 0x4f, 0x66, 0x66, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x22, 0x18, 0x0a, 0x16, 0x47, 0x32, 0x4d, 0x5f, 0x4c, 0x6f, 0x61, 0x64, 0x53,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x75, 0x66, 0x66, 0x44, 0x61, 0x74, 0x61, 0x22, 0x55, 0x0a,
	0x16, 0x4d, 0x32, 0x47, 0x5f, 0x4c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42,
	0x75, 0x66, 0x66, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x73,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x75, 0x66, 0x66, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x75, 0x66, 0x66,
	0x44, 0x61, 0x74, 0x61, 0x22, 0x41, 0x0a, 0x16, 0x47, 0x32, 0x4d, 0x5f, 0x53, 0x61, 0x76, 0x65,
	0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x75, 0x66, 0x66, 0x44, 0x61, 0x74, 0x61, 0x12, 0x27,
	0x0a, 0x0f, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x75, 0x66, 0x66, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42,
	0x75, 0x66, 0x66, 0x44, 0x61, 0x74, 0x61, 0x22, 0x2c, 0x0a, 0x16, 0x4d, 0x32, 0x47, 0x5f, 0x53,
	0x61, 0x76, 0x65, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x75, 0x66, 0x66, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x26, 0x5a, 0x24, 0x6c, 0x69, 0x74, 0x65, 0x66, 0x72, 0x61,
	0x6d, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x67, 0x32, 0x6d, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MongoProtocol_proto_rawDescOnce sync.Once
	file_MongoProtocol_proto_rawDescData = file_MongoProtocol_proto_rawDesc
)

func file_MongoProtocol_proto_rawDescGZIP() []byte {
	file_MongoProtocol_proto_rawDescOnce.Do(func() {
		file_MongoProtocol_proto_rawDescData = protoimpl.X.CompressGZIP(file_MongoProtocol_proto_rawDescData)
	})
	return file_MongoProtocol_proto_rawDescData
}

var file_MongoProtocol_proto_msgTypes = make([]protoimpl.MessageInfo, 37)
var file_MongoProtocol_proto_goTypes = []interface{}{
	(*G2M_LoadOrCreateUser)(nil),       // 0: g2m.G2M_LoadOrCreateUser
	(*M2G_LoadOrCreateUser)(nil),       // 1: g2m.M2G_LoadOrCreateUser
	(*G2M_SaveUser)(nil),               // 2: g2m.G2M_SaveUser
	(*G2M_LoadUser)(nil),               // 3: g2m.G2M_LoadUser
	(*M2G_LoadUser)(nil),               // 4: g2m.M2G_LoadUser
	(*G2M_LoadCreateUid)(nil),          // 5: g2m.G2M_LoadCreateUid
	(*M2G_LoadCreateUid)(nil),          // 6: g2m.M2G_LoadCreateUid
	(*G2M_SaveData)(nil),               // 7: g2m.G2M_SaveData
	(*G2M_LoadData)(nil),               // 8: g2m.G2M_LoadData
	(*M2G_LoadData)(nil),               // 9: g2m.M2G_LoadData
	(*G2M_DeleteData)(nil),             // 10: g2m.G2M_DeleteData
	(*G2M_FindDuplicateNamesData)(nil), // 11: g2m.G2M_FindDuplicateNamesData
	(*M2G_FindDuplicateNamesData)(nil), // 12: g2m.M2G_FindDuplicateNamesData
	(*G2M_ChangeNameData)(nil),         // 13: g2m.G2M_ChangeNameData
	(*G2M_LoadGuildData)(nil),          // 14: g2m.G2M_LoadGuildData
	(*M2G_LoadGuildData)(nil),          // 15: g2m.M2G_LoadGuildData
	(*G2M_SaveGuildData)(nil),          // 16: g2m.G2M_SaveGuildData
	(*M2G_SaveGuildData)(nil),          // 17: g2m.M2G_SaveGuildData
	(*G2M_DeleteGuildData)(nil),        // 18: g2m.G2M_DeleteGuildData
	(*M2G_DeleteGuildData)(nil),        // 19: g2m.M2G_DeleteGuildData
	(*G2M_LoadPaymentData)(nil),        // 20: g2m.G2M_LoadPaymentData
	(*M2G_LoadPaymentData)(nil),        // 21: g2m.M2G_LoadPaymentData
	(*G2M_SavePaymentData)(nil),        // 22: g2m.G2M_SavePaymentData
	(*M2G_SavePaymentData)(nil),        // 23: g2m.M2G_SavePaymentData
	(*G2M_LoadUserSnapDataBatch)(nil),  // 24: g2m.G2M_LoadUserSnapDataBatch
	(*M2G_LoadUserSnapDataBatch)(nil),  // 25: g2m.M2G_LoadUserSnapDataBatch
	(*G2M_LoadAllAccountMap)(nil),      // 26: g2m.G2M_LoadAllAccountMap
	(*M2G_LoadAllAccountMap)(nil),      // 27: g2m.M2G_LoadAllAccountMap
	(*G2M_LoadTipOffData)(nil),         // 28: g2m.G2M_LoadTipOffData
	(*M2G_LoadTipOffData)(nil),         // 29: g2m.M2G_LoadTipOffData
	(*G2M_SaveTipOffData)(nil),         // 30: g2m.G2M_SaveTipOffData
	(*M2G_SaveTipOffData)(nil),         // 31: g2m.M2G_SaveTipOffData
	(*G2M_LoadSeasonBuffData)(nil),     // 32: g2m.G2M_LoadSeasonBuffData
	(*M2G_LoadSeasonBuffData)(nil),     // 33: g2m.M2G_LoadSeasonBuffData
	(*G2M_SaveSeasonBuffData)(nil),     // 34: g2m.G2M_SaveSeasonBuffData
	(*M2G_SaveSeasonBuffData)(nil),     // 35: g2m.M2G_SaveSeasonBuffData
	nil,                                // 36: g2m.M2G_LoadAllAccountMap.AccountMapEntry
	(*dbstruct.UserDB)(nil),            // 37: dbstruct.UserDB
}
var file_MongoProtocol_proto_depIdxs = []int32{
	37, // 0: g2m.M2G_LoadOrCreateUser.user:type_name -> dbstruct.UserDB
	37, // 1: g2m.G2M_SaveUser.user:type_name -> dbstruct.UserDB
	37, // 2: g2m.M2G_LoadUser.user:type_name -> dbstruct.UserDB
	36, // 3: g2m.M2G_LoadAllAccountMap.account_map:type_name -> g2m.M2G_LoadAllAccountMap.AccountMapEntry
	4,  // [4:4] is the sub-list for method output_type
	4,  // [4:4] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_MongoProtocol_proto_init() }
func file_MongoProtocol_proto_init() {
	if File_MongoProtocol_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_MongoProtocol_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_LoadOrCreateUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*M2G_LoadOrCreateUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_SaveUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_LoadUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*M2G_LoadUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_LoadCreateUid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*M2G_LoadCreateUid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_SaveData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_LoadData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*M2G_LoadData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_DeleteData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_FindDuplicateNamesData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*M2G_FindDuplicateNamesData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_ChangeNameData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_LoadGuildData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*M2G_LoadGuildData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_SaveGuildData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*M2G_SaveGuildData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_DeleteGuildData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*M2G_DeleteGuildData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_LoadPaymentData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*M2G_LoadPaymentData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_SavePaymentData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*M2G_SavePaymentData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_LoadUserSnapDataBatch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*M2G_LoadUserSnapDataBatch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_LoadAllAccountMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*M2G_LoadAllAccountMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_LoadTipOffData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*M2G_LoadTipOffData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_SaveTipOffData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*M2G_SaveTipOffData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_LoadSeasonBuffData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*M2G_LoadSeasonBuffData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*G2M_SaveSeasonBuffData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MongoProtocol_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*M2G_SaveSeasonBuffData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MongoProtocol_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   37,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MongoProtocol_proto_goTypes,
		DependencyIndexes: file_MongoProtocol_proto_depIdxs,
		MessageInfos:      file_MongoProtocol_proto_msgTypes,
	}.Build()
	File_MongoProtocol_proto = out.File
	file_MongoProtocol_proto_rawDesc = nil
	file_MongoProtocol_proto_goTypes = nil
	file_MongoProtocol_proto_depIdxs = nil
}
