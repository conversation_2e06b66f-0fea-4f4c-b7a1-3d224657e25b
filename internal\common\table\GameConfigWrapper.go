package table;
	/**
	 *ID
	 */
	func GetID() int32 {
		return GetTable().TableGameConfig.GetById(1).ID
	}

	/**
	 *转菊花持续时间
	 */
	func GetWaitTime() int32 {
		return GetTable().TableGameConfig.GetById(1).WaitTime
	}

	/**
	 *client心跳间隔
	 */
	func GetHeartBeatCheckTimer() float32 {
		return GetTable().TableGameConfig.GetById(1).HeartBeatCheckTimer
	}

	/**
	 *client断网心跳检车次数
	 */
	func GetOffLineCheckNum() int32 {
		return GetTable().TableGameConfig.GetById(1).OffLineCheckNum
	}

	/**
	 *client切后台后再返回强退游戏间隔(分钟)
	 */
	func GetBackgroudToLoginUI() int32 {
		return GetTable().TableGameConfig.GetById(1).BackgroudToLoginUI
	}

	/**
	 *弱网检测间隔
	 */
	func GetWeakNetCheckInterval() int32 {
		return GetTable().TableGameConfig.GetById(1).WeakNetCheckInterval
	}

	/**
	 *是否显示弱网菊花
	 */
	func GetShowWeakNetworkLoading() int32 {
		return GetTable().TableGameConfig.GetById(1).ShowWeakNetworkLoading
	}

	/**
	 *最大重连次数
	 */
	func GetMaxReconnecTimes() int32 {
		return GetTable().TableGameConfig.GetById(1).MaxReconnecTimes
	}

	/**
	 *重连时间间隔
	 */
	func GetReconnecIntervalTime() int32 {
		return GetTable().TableGameConfig.GetById(1).ReconnecIntervalTime
	}

	/**
	 *断网后是否重连
	 */
	func GetTryReconnect() int32 {
		return GetTable().TableGameConfig.GetById(1).TryReconnect
	}

	/**
	 *Loading进入主界面超时时间
	 */
	func GetShowLoadingWaitTime() float32 {
		return GetTable().TableGameConfig.GetById(1).ShowLoadingWaitTime
	}

	/**
	 *子弹的之间的度值
	 */
	func GetZiDanAngleValue() float32 {
		return GetTable().TableGameConfig.GetById(1).ZiDanAngleValue
	}

	/**
	 *玩家的默认属性id
	 */
	func GetPlayerBaseAttrId() int32 {
		return GetTable().TableGameConfig.GetById(1).PlayerBaseAttrId
	}

	/**
	 *受击泛白的持续时间(单位秒)
	 */
	func GetHurtContinueWhiteTime() float32 {
		return GetTable().TableGameConfig.GetById(1).HurtContinueWhiteTime
	}

	/**
	 *受击泛白的渐变时间
	 */
	func GetHurtGradientTime() float32 {
		return GetTable().TableGameConfig.GetById(1).HurtGradientTime
	}

	/**
	 *失效：受击泛白的颜色
	 */
	func GetHurtWhiteColor() []float32 {
		return GetTable().TableGameConfig.GetById(1).HurtWhiteColor
	}

	/**
	 *受击泛白的程度
	 */
	func GetHurtWhiteColorMaxA() float32 {
		return GetTable().TableGameConfig.GetById(1).HurtWhiteColorMaxA
	}

	/**
	 *受击缩放的比例实际是（1 + 0.2）
	 */
	func GetHurtScaleValueF() float32 {
		return GetTable().TableGameConfig.GetById(1).HurtScaleValueF
	}

	/**
	 *挂机系统最大时长，默认12小时。单位：秒
	 */
	func GetHookMaxTime() int32 {
		return GetTable().TableGameConfig.GetById(1).HookMaxTime
	}

	/**
	 *掉落的停留时间（帧）
	 */
	func GetDropSceneStayTimeInt() []int32 {
		return GetTable().TableGameConfig.GetById(1).DropSceneStayTimeInt
	}

	/**
	 *掉落的飞到角色身上的时间（帧）
	 */
	func GetDropFlyPlayerTimeInt() int32 {
		return GetTable().TableGameConfig.GetById(1).DropFlyPlayerTimeInt
	}

	/**
	 *掉落飞向周围的时间(秒)
	 */
	func GetDropFlyAroundTimeFloat() float32 {
		return GetTable().TableGameConfig.GetById(1).DropFlyAroundTimeFloat
	}

	/**
	 *掉落飞向周围的最小距离最大距离
	 */
	func GetDropFlyAroundMinFMaxF() []float32 {
		return GetTable().TableGameConfig.GetById(1).DropFlyAroundMinFMaxF
	}

	/**
	 *玩家距离指定点的最小距离
	 */
	func GetPlayerToPointMinDis() float32 {
		return GetTable().TableGameConfig.GetById(1).PlayerToPointMinDis
	}

	/**
	 *逃跑的角度范围（偶数方便随机和取一半值）
	 */
	func GetEscapeRandomMaxAngle() int32 {
		return GetTable().TableGameConfig.GetById(1).EscapeRandomMaxAngle
	}

	/**
	 *技能的最小CD时间帧数
	 */
	func GetSkillMinCDTimeValue() int32 {
		return GetTable().TableGameConfig.GetById(1).SkillMinCDTimeValue
	}

	/**
	 *角色出生点背面
	 */
	func GetPlayerBornPositionXYZ() []float32 {
		return GetTable().TableGameConfig.GetById(1).PlayerBornPositionXYZ
	}

	/**
	 *角色出生点正面
	 */
	func GetPlayerFrontBornPositionXYZ() []float32 {
		return GetTable().TableGameConfig.GetById(1).PlayerFrontBornPositionXYZ
	}

	/**
	 *飘血的最大时间
	 */
	func GetDamageBoardMaxTime() float32 {
		return GetTable().TableGameConfig.GetById(1).DamageBoardMaxTime
	}

	/**
	 *副本开始
	 */
	func GetStageBeginID() []int32 {
		return GetTable().TableGameConfig.GetById(1).StageBeginID
	}

	/**
	 *挂机系统额外奖励实际获得倍数数组，分时段取倍数，几个数代表几个小时
	 */
	func GetHookExtraAwardRatio() []string {
		return GetTable().TableGameConfig.GetById(1).HookExtraAwardRatio
	}

	/**
	 *伤害Z坐标信息
	 */
	func GetDamageDepthPositionZ() float32 {
		return GetTable().TableGameConfig.GetById(1).DamageDepthPositionZ
	}

	/**
	 *不在攻击范围以内玩家移动到目标的X的偏移
	 */
	func GetTargetPointPositionDriftX() float32 {
		return GetTable().TableGameConfig.GetById(1).TargetPointPositionDriftX
	}

	/**
	 *血条变化灰色的处理信息延迟时间
	 */
	func GetSceneBloodGreyDelayChangeTime() float32 {
		return GetTable().TableGameConfig.GetById(1).SceneBloodGreyDelayChangeTime
	}

	/**
	 *血条变化灰色的处理信息变化的速度
	 */
	func GetSceneBloodGreyChangeSpeed() float32 {
		return GetTable().TableGameConfig.GetById(1).SceneBloodGreyChangeSpeed
	}

	/**
	 *七日任务进度
	 */
	func GetSevenDayTaskProgress() []int32 {
		return GetTable().TableGameConfig.GetById(1).SevenDayTaskProgress
	}

	/**
	 *七日任务阶段奖励
	 */
	func GetSevenDayTaskStageReward() []int32 {
		return GetTable().TableGameConfig.GetById(1).SevenDayTaskStageReward
	}

	/**
	 *七日任务持续时间
	 */
	func GetSevenDayTaskDuration() int32 {
		return GetTable().TableGameConfig.GetById(1).SevenDayTaskDuration
	}

	/**
	 *掉落飞到角色身上的特效
	 */
	func GetDropScenePlayerEffectId() int32 {
		return GetTable().TableGameConfig.GetById(1).DropScenePlayerEffectId
	}

	/**
	 *掉落飞到角色播放特效的时间间隔
	 */
	func GetDropScenePlayerEffectInterval() float32 {
		return GetTable().TableGameConfig.GetById(1).DropScenePlayerEffectInterval
	}

	/**
	 *主线任务引导小手消失任务
	 */
	func GetMissionFingerVanish() int32 {
		return GetTable().TableGameConfig.GetById(1).MissionFingerVanish
	}

	/**
	 *省电模式无操作进入时间，单位秒
	 */
	func GetNoOperationEntryTime() int32 {
		return GetTable().TableGameConfig.GetById(1).NoOperationEntryTime
	}

	/**
	 *好友邀请奖励
	 */
	func GetFriendRasinReward() []int32 {
		return GetTable().TableGameConfig.GetById(1).FriendRasinReward
	}

	/**
	 *用户协议的URL
	 */
	func GetUserAgreementURL() string {
		return GetTable().TableGameConfig.GetById(1).UserAgreementURL
	}

	/**
	 *各副本储存上限字段，各副本独立使用
	 */
	func GetStorageLimit() []int32 {
		return GetTable().TableGameConfig.GetById(1).StorageLimit
	}

	/**
	 *月卡首次购买直送礼包
	 */
	func GetMonthCardFirstDrop() int32 {
		return GetTable().TableGameConfig.GetById(1).MonthCardFirstDrop
	}

	/**
	 *月卡每日礼包
	 */
	func GetMonthCardDailyDrop() int32 {
		return GetTable().TableGameConfig.GetById(1).MonthCardDailyDrop
	}

	/**
	 *玩家初始头像
	 */
	func GetPlayerInitHead() int32 {
		return GetTable().TableGameConfig.GetById(1).PlayerInitHead
	}

	/**
	 *史莱姆数量对应时间的系数（1,2,3,4）
	 */
	func GetPlayerSlamNumTimeArgsArr() []float32 {
		return GetTable().TableGameConfig.GetById(1).PlayerSlamNumTimeArgsArr
	}

	/**
	 *聊天泡泡的时间显示时间和消失时间
	 */
	func GetChatBubbleShowTimeAndDisappearTime() []float32 {
		return GetTable().TableGameConfig.GetById(1).ChatBubbleShowTimeAndDisappearTime
	}

	/**
	 *现金券数量阈值
	 */
	func GetCashCouponThreshold() int32 {
		return GetTable().TableGameConfig.GetById(1).CashCouponThreshold
	}

	/**
	 *隐私协议的URL
	 */
	func GetPrivateAgreementURL() string {
		return GetTable().TableGameConfig.GetById(1).PrivateAgreementURL
	}

	/**
	 *只受1点伤害怪ID
	 */
	func GetIsOnlyOneDamage() []int32 {
		return GetTable().TableGameConfig.GetById(1).IsOnlyOneDamage
	}

	/**
	 *怪物出生点和目标点的X的比例
	 */
	func GetStartEndXScale() float32 {
		return GetTable().TableGameConfig.GetById(1).StartEndXScale
	}

	/**
	 *怪物终点的Z或是怪物判断距离和终点的Z
	 */
	func GetMonsterTargeEndZ() float32 {
		return GetTable().TableGameConfig.GetById(1).MonsterTargeEndZ
	}

	/**
	 *伤害瓢字的参数信息
	 */
	func GetDamageFlutterArgs() []float32 {
		return GetTable().TableGameConfig.GetById(1).DamageFlutterArgs
	}

	/**
	 *城墙瓢字缩放
	 */
	func GetDamageFlutWallScale() float32 {
		return GetTable().TableGameConfig.GetById(1).DamageFlutWallScale
	}

	/**
	 *玩家身上体力上限信息
	 */
	func GetPowerHaveMaxNum() int32 {
		return GetTable().TableGameConfig.GetById(1).PowerHaveMaxNum
	}

	/**
	 *体力恢复的时间间隔(单位秒)
	 */
	func GetPowerAddTimeInterval() int32 {
		return GetTable().TableGameConfig.GetById(1).PowerAddTimeInterval
	}

	/**
	 *挑战主线关卡消耗的体力
	 */
	func GetMainStagePowerCost() int32 {
		return GetTable().TableGameConfig.GetById(1).MainStagePowerCost
	}

	/**
	 *挑战主队玩法消耗的体力
	 */
	func GetTeamUpPowerCost() int32 {
		return GetTable().TableGameConfig.GetById(1).TeamUpPowerCost
	}

	/**
	 *天气切换的小时（精确到秒）
	 */
	func GetRefreshWeatherHour() []int32 {
		return GetTable().TableGameConfig.GetById(1).RefreshWeatherHour
	}

	/**
	 *体力极限上限
	 */
	func GetPowerLimitNum() int32 {
		return GetTable().TableGameConfig.GetById(1).PowerLimitNum
	}

	/**
	 *每波子弹数量（打多次子弹开始换弹）
	 */
	func GetAttackSkillNum() int32 {
		return GetTable().TableGameConfig.GetById(1).AttackSkillNum
	}

	/**
	 *换弹时间，跟技能表cd读一样的逻辑，单独配置值
	 */
	func GetAttackSkillChange() int32 {
		return GetTable().TableGameConfig.GetById(1).AttackSkillChange
	}

	/**
	 *先锋能量值上限
	 */
	func GetVanguardEnergyLimit() int32 {
		return GetTable().TableGameConfig.GetById(1).VanguardEnergyLimit
	}

	/**
	 *先锋值能量回复每N秒回复X点
	 */
	func GetVanguardSkillRecovery() string {
		return GetTable().TableGameConfig.GetById(1).VanguardSkillRecovery
	}

	/**
	 *机器人模型ID
	 */
	func GetCyberModelID() int32 {
		return GetTable().TableGameConfig.GetById(1).CyberModelID
	}

	/**
	 *机器人坐标
	 */
	func GetCyberBornPositionXYZ() []float32 {
		return GetTable().TableGameConfig.GetById(1).CyberBornPositionXYZ
	}

	/**
	 *攻击几次播待机动画
	 */
	func GetAttackNumPlayIdleAni() int32 {
		return GetTable().TableGameConfig.GetById(1).AttackNumPlayIdleAni
	}

	/**
	 *主界面相机位置
	 */
	func GetMainCameraPosition() []float32 {
		return GetTable().TableGameConfig.GetById(1).MainCameraPosition
	}

	/**
	 *主界面相机角度
	 */
	func GetMainCameraRotation() []float32 {
		return GetTable().TableGameConfig.GetById(1).MainCameraRotation
	}

	/**
	 *主界面相机视角
	 */
	func GetMainCameraView() float32 {
		return GetTable().TableGameConfig.GetById(1).MainCameraView
	}

	/**
	 *战斗界面相机角度
	 */
	func GetBattleCameraPosition() []float32 {
		return GetTable().TableGameConfig.GetById(1).BattleCameraPosition
	}

	/**
	 *战斗界面相机角度
	 */
	func GetBattleCameraRotation() []float32 {
		return GetTable().TableGameConfig.GetById(1).BattleCameraRotation
	}

	/**
	 *战斗界面相机视角
	 */
	func GetBattleCameraView() float32 {
		return GetTable().TableGameConfig.GetById(1).BattleCameraView
	}

	/**
	 *怪物移动速度比例参数
	 */
	func GetMonsterMoveSpeedParm() float32 {
		return GetTable().TableGameConfig.GetById(1).MonsterMoveSpeedParm
	}

	/**
	 *准心的最远距离
	 */
	func GetAimPositionZ() float32 {
		return GetTable().TableGameConfig.GetById(1).AimPositionZ
	}

	/**
	 *飘字距离随机阈值
	 */
	func GetFloatingThreshold() []float32 {
		return GetTable().TableGameConfig.GetById(1).FloatingThreshold
	}

	/**
	 *不需要打断音效的类型
	 */
	func GetNotStopSoundTypes() []int32 {
		return GetTable().TableGameConfig.GetById(1).NotStopSoundTypes
	}

	/**
	 *拍打能量值
	 */
	func GetButtEnergy() int32 {
		return GetTable().TableGameConfig.GetById(1).ButtEnergy
	}

	/**
	 *点击屏幕出现手印的间隔
	 */
	func GetClickScreenInterval() float32 {
		return GetTable().TableGameConfig.GetById(1).ClickScreenInterval
	}

	/**
	 *体力建筑每日刷新时间
	 */
	func GetPowerRefreshTime() []int32 {
		return GetTable().TableGameConfig.GetById(1).PowerRefreshTime
	}

	/**
	 *体力建筑最大储存上限
	 */
	func GetPowerStorageLimit() int32 {
		return GetTable().TableGameConfig.GetById(1).PowerStorageLimit
	}

	/**
	 *体力过期时间（天）
	 */
	func GetPowerPeriodValidity() int32 {
		return GetTable().TableGameConfig.GetById(1).PowerPeriodValidity
	}

	/**
	 *体力生成数量
	 */
	func GetPowerNum() int32 {
		return GetTable().TableGameConfig.GetById(1).PowerNum
	}

	/**
	 *可选角色
	 */
	func GetOptionalPlayer() []int32 {
		return GetTable().TableGameConfig.GetById(1).OptionalPlayer
	}

	/**
	 *挂机图纸
	 */
	func GetDrawingDropGroupId() int32 {
		return GetTable().TableGameConfig.GetById(1).DrawingDropGroupId
	}

	/**
	 *挂机倍率
	 */
	func GetDrawingTimes() []int32 {
		return GetTable().TableGameConfig.GetById(1).DrawingTimes
	}

	/**
	 *最大扫荡次数
	 */
	func GetDrawingMaxTimes() int32 {
		return GetTable().TableGameConfig.GetById(1).DrawingMaxTimes
	}

	/**
	 *精英怪物随机权重
	 */
	func GetEliteWeight() []int32 {
		return GetTable().TableGameConfig.GetById(1).EliteWeight
	}

	/**
	 *BOSS怪物随机权重
	 */
	func GetBOSSWeight() []int32 {
		return GetTable().TableGameConfig.GetById(1).BOSSWeight
	}

	/**
	 *战斗内BUFF随机广告次数
	 */
	func GetFightRandomAdvertisementNum() int32 {
		return GetTable().TableGameConfig.GetById(1).FightRandomAdvertisementNum
	}

	/**
	 *昵称后缀区间
	 */
	func GetNameNumInterval() []int32 {
		return GetTable().TableGameConfig.GetById(1).NameNumInterval
	}

	/**
	 *昵称最大字符限制
	 */
	func GetNameMaxCharacter() int32 {
		return GetTable().TableGameConfig.GetById(1).NameMaxCharacter
	}

	/**
	 *个性签名最大字符限制
	 */
	func GetSignatureMaxCharacter() int32 {
		return GetTable().TableGameConfig.GetById(1).SignatureMaxCharacter
	}

	/**
	 *新手第一组id
	 */
	func GetPlayerStartNewGuildGroupId() int32 {
		return GetTable().TableGameConfig.GetById(1).PlayerStartNewGuildGroupId
	}

	/**
	 *全服邮件的最大存储上限
	 */
	func GetGlobalServerMailMaxNum() int32 {
		return GetTable().TableGameConfig.GetById(1).GlobalServerMailMaxNum
	}

	/**
	 *推送：设置下线时挂机已满，铲子已满 间隔多长时间推送，单位：秒,第1个数为挂机间隔，第2个为铲子间隔
	 */
	func GetPushFullIntervalTimes() []int32 {
		return GetTable().TableGameConfig.GetById(1).PushFullIntervalTimes
	}

	/**
	 *现金券转钻石比例
	 */
	func GetCashCouponRatio() int32 {
		return GetTable().TableGameConfig.GetById(1).CashCouponRatio
	}

	/**
	 *活动列表排序（按照活动类型ActivityType排序）
	 */
	func GetActivityListSortConfig() []int32 {
		return GetTable().TableGameConfig.GetById(1).ActivityListSortConfig
	}

	/**
	 *副本的广告上限
	 */
	func GetStageDailyAdCount() []int32 {
		return GetTable().TableGameConfig.GetById(1).StageDailyAdCount
	}

	/**
	 *副本的消耗ID
	 */
	func GetStageCostItemId() []int32 {
		return GetTable().TableGameConfig.GetById(1).StageCostItemId
	}

	/**
	 *副本的回复数量
	 */
	func GetStageDailyGiveCount() []int32 {
		return GetTable().TableGameConfig.GetById(1).StageDailyGiveCount
	}

	/**
	 *副本广告单次
	 */
	func GetStageOneADGiveCount() int32 {
		return GetTable().TableGameConfig.GetById(1).StageOneADGiveCount
	}

	/**
	 *世界聊天冷却时间
	 */
	func GetWorldChatInterval() int32 {
		return GetTable().TableGameConfig.GetById(1).WorldChatInterval
	}

	/**
	 *好友邀请奖励（填写邀请码）
	 */
	func GetFriendInvitedReward() int32 {
		return GetTable().TableGameConfig.GetById(1).FriendInvitedReward
	}

	/**
	 *好友礼物每日接收上限
	 */
	func GetGiftRecMax() int32 {
		return GetTable().TableGameConfig.GetById(1).GiftRecMax
	}

	/**
	 *抽卡系统：每天可观看广告次数上限
	 */
	func GetGachaWatchMaxCount() int32 {
		return GetTable().TableGameConfig.GetById(1).GachaWatchMaxCount
	}

	/**
	 *抽卡系统：每天可观看广告间隔时间，单位：秒
	 */
	func GetGachaWatchInterTime() int32 {
		return GetTable().TableGameConfig.GetById(1).GachaWatchInterTime
	}

	/**
	 *抽卡系统：第一个是广告档位次数，第二个是500钻石档位，第三个是1500钻石档位，第四个无效
	 */
	func GetGachaDrawRates() []int32 {
		return GetTable().TableGameConfig.GetById(1).GachaDrawRates
	}

	/**
	 *抽卡系统：抽卡一次获得的经验
	 */
	func GetGachaDrawExp() int32 {
		return GetTable().TableGameConfig.GetById(1).GachaDrawExp
	}

	/**
	 *抽卡系统：第一个是广告档位次数，第二个是500钻石档位，第三个是1500钻石档位，第四个无效
	 */
	func GetGachaDrawCostDiamonds() []int32 {
		return GetTable().TableGameConfig.GetById(1).GachaDrawCostDiamonds
	}

	/**
	 *宠物抽卡系统：每天可观看广告间隔时间，单位：秒
	 */
	func GetPetGachaWatchInterTime() int32 {
		return GetTable().TableGameConfig.GetById(1).PetGachaWatchInterTime
	}

	/**
	 *宠物抽卡系统：抽卡一次获得的经验
	 */
	func GetPetGachaDrawExp() int32 {
		return GetTable().TableGameConfig.GetById(1).PetGachaDrawExp
	}

	/**
	 *圣物抽卡系统：观看广告可抽卡次数上限
	 */
	func GetHallowsGachaAdvDrawMaxCount() int32 {
		return GetTable().TableGameConfig.GetById(1).HallowsGachaAdvDrawMaxCount
	}

	/**
	 *圣物抽卡系统：每天可观看广告次数上限
	 */
	func GetHallowsGachaWatchMaxCount() int32 {
		return GetTable().TableGameConfig.GetById(1).HallowsGachaWatchMaxCount
	}

	/**
	 *抽卡系统：观看广告可抽卡次数上限
	 */
	func GetGachaAdvDrawMaxCount() int32 {
		return GetTable().TableGameConfig.GetById(1).GachaAdvDrawMaxCount
	}

	/**
	 *圣物抽卡系统：普通抽卡档位，下标为档位，值为次数，0档为广告初始次数
	 */
	func GetHallowsGachaDrawRates() []int32 {
		return GetTable().TableGameConfig.GetById(1).HallowsGachaDrawRates
	}

	/**
	 *系统赠送头像框
	 */
	func GetPlayerDefaultHeadFrame() int32 {
		return GetTable().TableGameConfig.GetById(1).PlayerDefaultHeadFrame
	}

	/**
	 *邮件列表显示上限
	 */
	func GetMailMaxNum() int32 {
		return GetTable().TableGameConfig.GetById(1).MailMaxNum
	}

	/**
	 *问卷奖励邮件ID
	 */
	func GetQuestionnaireEmail() int32 {
		return GetTable().TableGameConfig.GetById(1).QuestionnaireEmail
	}

	/**
	 *创建账号奖励ID
	 */
	func GetNewaccountEmail() int32 {
		return GetTable().TableGameConfig.GetById(1).NewaccountEmail
	}

	/**
	 *第二天邮件奖励ID
	 */
	func GetNextDayMailReward() int32 {
		return GetTable().TableGameConfig.GetById(1).NextDayMailReward
	}

	/**
	 *充值重复返还比例
	 */
	func GetRechargeRatio() int32 {
		return GetTable().TableGameConfig.GetById(1).RechargeRatio
	}

	/**
	 *日常任务ID
	 */
	func GetDailyTaskArr() []int32 {
		return GetTable().TableGameConfig.GetById(1).DailyTaskArr
	}

	/**
	 *周日常任务ID
	 */
	func GetWeekTaskArr() []int32 {
		return GetTable().TableGameConfig.GetById(1).WeekTaskArr
	}

	/**
	 *好友数量上限
	 */
	func GetFriendMaxNumber() int32 {
		return GetTable().TableGameConfig.GetById(1).FriendMaxNumber
	}

	/**
	 *可拉黑上限
	 */
	func GetBlacklistMax() int32 {
		return GetTable().TableGameConfig.GetById(1).BlacklistMax
	}

	/**
	 *好友送礼物品id|数量
	 */
	func GetGiftItemNum() []int32 {
		return GetTable().TableGameConfig.GetById(1).GiftItemNum
	}

	/**
	 *玩家名字字符最大长度
	 */
	func GetPlayerNameMaxLength() int32 {
		return GetTable().TableGameConfig.GetById(1).PlayerNameMaxLength
	}

	/**
	 *好友礼物每日赠送上限
	 */
	func GetGiftSendMax() int32 {
		return GetTable().TableGameConfig.GetById(1).GiftSendMax
	}

	/**
	 *GM工具发送邮件的过期时间，单位：天
	 */
	func GetGMSendMailMaxDayTime() int32 {
		return GetTable().TableGameConfig.GetById(1).GMSendMailMaxDayTime
	}

	/**
	 *创建角色时默认给玩家的钻石
	 */
	func GetCreatePlayerDefaultDiamond() int32 {
		return GetTable().TableGameConfig.GetById(1).CreatePlayerDefaultDiamond
	}

	/**
	 *创建角色时默认给玩家的金币
	 */
	func GetCreatePlayerDefaultCoin() int32 {
		return GetTable().TableGameConfig.GetById(1).CreatePlayerDefaultCoin
	}

	/**
	 *玩家初始时装（使用称号时装第一个）
	 */
	func GetPlayerInitDress() int32 {
		return GetTable().TableGameConfig.GetById(1).PlayerInitDress
	}

	/**
	 *主线关卡排名低于50%时，根据区间配置固定区间值和下面的增长值要一一对应。
	 */
	func GetMainStagePassBounds() []int32 {
		return GetTable().TableGameConfig.GetById(1).MainStagePassBounds
	}

	/**
	 *主线关卡排名低于50%时，根据区间配置固定增长值。
	 */
	func GetMainStagePassAdds() []int32 {
		return GetTable().TableGameConfig.GetById(1).MainStagePassAdds
	}

	/**
	 *邮件每日领取奖励
	 */
	func GetDailyCollectEmail() []int32 {
		return GetTable().TableGameConfig.GetById(1).DailyCollectEmail
	}

	/**
	 *邮件每周领取奖励
	 */
	func GetWeeklyReceiveEmail() int32 {
		return GetTable().TableGameConfig.GetById(1).WeeklyReceiveEmail
	}

	/**
	 *性别配置（保密|男|女）
	 */
	func GetGenderConfig() []string {
		return GetTable().TableGameConfig.GetById(1).GenderConfig
	}

	/**
	 *修改个人信息钻石配置
	 */
	func GetChangeInforCost() int32 {
		return GetTable().TableGameConfig.GetById(1).ChangeInforCost
	}

	/**
	 *个性签名修改时间限制（秒）
	 */
	func GetSignatureChangeTime() int32 {
		return GetTable().TableGameConfig.GetById(1).SignatureChangeTime
	}

	/**
	 *等级基金
	 */
	func GetGradeFund() int32 {
		return GetTable().TableGameConfig.GetById(1).GradeFund
	}

	/**
	 *公会名上限
	 */
	func GetAllianceNameLen() int32 {
		return GetTable().TableGameConfig.GetById(1).AllianceNameLen
	}

	/**
	 *公会描述上限
	 */
	func GetAllianceNoticeLen() int32 {
		return GetTable().TableGameConfig.GetById(1).AllianceNoticeLen
	}

	/**
	 *创建公会花费
	 */
	func GetAllianceCreateExpend() []int32 {
		return GetTable().TableGameConfig.GetById(1).AllianceCreateExpend
	}

	/**
	 *周卡对应礼包ID
	 */
	func GetWeekCardGiftId() []int32 {
		return GetTable().TableGameConfig.GetById(1).WeekCardGiftId
	}

	/**
	 *每日好友申请数量上限
	 */
	func GetFriendApplyMaxCount() int32 {
		return GetTable().TableGameConfig.GetById(1).FriendApplyMaxCount
	}

	/**
	 *好友数量上限
	 */
	func GetFriendMaxCount() int32 {
		return GetTable().TableGameConfig.GetById(1).FriendMaxCount
	}

	/**
	 *好友黑名单数量上限
	 */
	func GetFriendBlackMaxCount() int32 {
		return GetTable().TableGameConfig.GetById(1).FriendBlackMaxCount
	}

	/**
	 *对战匹配范围查找时间
	 */
	func GetMainRankMatchTime() []int32 {
		return GetTable().TableGameConfig.GetById(1).MainRankMatchTime
	}

	/**
	 *对战每日胜利奖励次数
	 */
	func GetMainBattleDailyWinTimes() int32 {
		return GetTable().TableGameConfig.GetById(1).MainBattleDailyWinTimes
	}

	/**
	 *对战每日失败补给次数
	 */
	func GetMainBattleDailyFailTimes() int32 {
		return GetTable().TableGameConfig.GetById(1).MainBattleDailyFailTimes
	}

	/**
	 *对战初始杯数
	 */
	func GetMainRankScoreInitial() int32 {
		return GetTable().TableGameConfig.GetById(1).MainRankScoreInitial
	}

	/**
	 *宝物抽取每日广告限次
	 */
	func GetTreasureGachaAdTimes() int32 {
		return GetTable().TableGameConfig.GetById(1).TreasureGachaAdTimes
	}

	/**
	 *宝物抽取概率修正系数
	 */
	func GetTreasureGachaProModify() []int32 {
		return GetTable().TableGameConfig.GetById(1).TreasureGachaProModify
	}

	/**
	 *玩家上阵英雄数量
	 */
	func GetHeroLineUpNum() int32 {
		return GetTable().TableGameConfig.GetById(1).HeroLineUpNum
	}

	/**
	 *初始上阵英雄id
	 */
	func GetHeroLineUpId() []int32 {
		return GetTable().TableGameConfig.GetById(1).HeroLineUpId
	}

