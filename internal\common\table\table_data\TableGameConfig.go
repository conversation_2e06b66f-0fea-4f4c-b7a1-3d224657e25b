/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableGameConfig struct {
	// ============= 变量定义 =============
	// ID
	ID int32
	// 转菊花持续时间
	WaitTime int32
	// client心跳间隔
	HeartBeatCheckTimer float32
	// client断网心跳检车次数
	OffLineCheckNum int32
	// client切后台后再返回强退游戏间隔(分钟)
	BackgroudToLoginUI int32
	// 弱网检测间隔
	WeakNetCheckInterval int32
	// 是否显示弱网菊花
	ShowWeakNetworkLoading int32
	// 最大重连次数
	MaxReconnecTimes int32
	// 重连时间间隔
	ReconnecIntervalTime int32
	// 断网后是否重连
	TryReconnect int32
	// Loading进入主界面超时时间
	ShowLoadingWaitTime float32
	// 子弹的之间的度值
	ZiDanAngleValue float32
	// 玩家的默认属性id
	PlayerBaseAttrId int32
	// 受击泛白的持续时间(单位秒)
	HurtContinueWhiteTime float32
	// 受击泛白的渐变时间
	HurtGradientTime float32
	// 失效：受击泛白的颜色
	HurtWhiteColor []float32
	// 受击泛白的程度
	HurtWhiteColorMaxA float32
	// 受击缩放的比例实际是（1 + 0.2）
	HurtScaleValueF float32
	// 挂机系统最大时长，默认12小时。单位：秒
	HookMaxTime int32
	// 掉落的停留时间（帧）
	DropSceneStayTimeInt []int32
	// 掉落的飞到角色身上的时间（帧）
	DropFlyPlayerTimeInt int32
	// 掉落飞向周围的时间(秒)
	DropFlyAroundTimeFloat float32
	// 掉落飞向周围的最小距离最大距离
	DropFlyAroundMinFMaxF []float32
	// 玩家距离指定点的最小距离
	PlayerToPointMinDis float32
	// 逃跑的角度范围（偶数方便随机和取一半值）
	EscapeRandomMaxAngle int32
	// 技能的最小CD时间帧数
	SkillMinCDTimeValue int32
	// 角色出生点背面
	PlayerBornPositionXYZ []float32
	// 角色出生点正面
	PlayerFrontBornPositionXYZ []float32
	// 飘血的最大时间
	DamageBoardMaxTime float32
	// 副本开始
	StageBeginID []int32
	// 挂机系统额外奖励实际获得倍数数组，分时段取倍数，几个数代表几个小时
	HookExtraAwardRatio []string
	// 伤害Z坐标信息
	DamageDepthPositionZ float32
	// 不在攻击范围以内玩家移动到目标的X的偏移
	TargetPointPositionDriftX float32
	// 血条变化灰色的处理信息延迟时间
	SceneBloodGreyDelayChangeTime float32
	// 血条变化灰色的处理信息变化的速度
	SceneBloodGreyChangeSpeed float32
	// 七日任务进度
	SevenDayTaskProgress []int32
	// 七日任务阶段奖励
	SevenDayTaskStageReward []int32
	// 七日任务持续时间
	SevenDayTaskDuration int32
	// 掉落飞到角色身上的特效
	DropScenePlayerEffectId int32
	// 掉落飞到角色播放特效的时间间隔
	DropScenePlayerEffectInterval float32
	// 主线任务引导小手消失任务
	MissionFingerVanish int32
	// 省电模式无操作进入时间，单位秒
	NoOperationEntryTime int32
	// 好友邀请奖励
	FriendRasinReward []int32
	// 用户协议的URL
	UserAgreementURL string
	// 各副本储存上限字段，各副本独立使用
	StorageLimit []int32
	// 月卡首次购买直送礼包
	MonthCardFirstDrop int32
	// 月卡每日礼包
	MonthCardDailyDrop int32
	// 玩家初始头像
	PlayerInitHead int32
	// 史莱姆数量对应时间的系数（1,2,3,4）
	PlayerSlamNumTimeArgsArr []float32
	// 聊天泡泡的时间显示时间和消失时间
	ChatBubbleShowTimeAndDisappearTime []float32
	// 现金券数量阈值
	CashCouponThreshold int32
	// 隐私协议的URL
	PrivateAgreementURL string
	// 只受1点伤害怪ID
	IsOnlyOneDamage []int32
	// 怪物出生点和目标点的X的比例
	StartEndXScale float32
	// 怪物终点的Z或是怪物判断距离和终点的Z
	MonsterTargeEndZ float32
	// 伤害瓢字的参数信息
	DamageFlutterArgs []float32
	// 城墙瓢字缩放
	DamageFlutWallScale float32
	// 玩家身上体力上限信息
	PowerHaveMaxNum int32
	// 体力恢复的时间间隔(单位秒)
	PowerAddTimeInterval int32
	// 挑战主线关卡消耗的体力
	MainStagePowerCost int32
	// 挑战主队玩法消耗的体力
	TeamUpPowerCost int32
	// 天气切换的小时（精确到秒）
	RefreshWeatherHour []int32
	// 体力极限上限
	PowerLimitNum int32
	// 每波子弹数量（打多次子弹开始换弹）
	AttackSkillNum int32
	// 换弹时间，跟技能表cd读一样的逻辑，单独配置值
	AttackSkillChange int32
	// 先锋能量值上限
	VanguardEnergyLimit int32
	// 先锋值能量回复每N秒回复X点
	VanguardSkillRecovery string
	// 机器人模型ID
	CyberModelID int32
	// 机器人坐标
	CyberBornPositionXYZ []float32
	// 攻击几次播待机动画
	AttackNumPlayIdleAni int32
	// 主界面相机位置
	MainCameraPosition []float32
	// 主界面相机角度
	MainCameraRotation []float32
	// 主界面相机视角
	MainCameraView float32
	// 战斗界面相机角度
	BattleCameraPosition []float32
	// 战斗界面相机角度
	BattleCameraRotation []float32
	// 战斗界面相机视角
	BattleCameraView float32
	// 怪物移动速度比例参数
	MonsterMoveSpeedParm float32
	// 准心的最远距离
	AimPositionZ float32
	// 飘字距离随机阈值
	FloatingThreshold []float32
	// 不需要打断音效的类型
	NotStopSoundTypes []int32
	// 拍打能量值
	ButtEnergy int32
	// 点击屏幕出现手印的间隔
	ClickScreenInterval float32
	// 体力建筑每日刷新时间
	PowerRefreshTime []int32
	// 体力建筑最大储存上限
	PowerStorageLimit int32
	// 体力过期时间（天）
	PowerPeriodValidity int32
	// 体力生成数量
	PowerNum int32
	// 可选角色
	OptionalPlayer []int32
	// 挂机图纸
	DrawingDropGroupId int32
	// 挂机倍率
	DrawingTimes []int32
	// 最大扫荡次数
	DrawingMaxTimes int32
	// 精英怪物随机权重
	EliteWeight []int32
	// BOSS怪物随机权重
	BOSSWeight []int32
	// 战斗内BUFF随机广告次数
	FightRandomAdvertisementNum int32
	// 昵称后缀区间
	NameNumInterval []int32
	// 昵称最大字符限制
	NameMaxCharacter int32
	// 个性签名最大字符限制
	SignatureMaxCharacter int32
	// 新手第一组id
	PlayerStartNewGuildGroupId int32
	// 全服邮件的最大存储上限
	GlobalServerMailMaxNum int32
	// 推送：设置下线时挂机已满，铲子已满 间隔多长时间推送，单位：秒,第1个数为挂机间隔，第2个为铲子间隔
	PushFullIntervalTimes []int32
	// 现金券转钻石比例
	CashCouponRatio int32
	// 活动列表排序（按照活动类型ActivityType排序）
	ActivityListSortConfig []int32
	// 副本的广告上限
	StageDailyAdCount []int32
	// 副本的消耗ID
	StageCostItemId []int32
	// 副本的回复数量
	StageDailyGiveCount []int32
	// 副本广告单次
	StageOneADGiveCount int32
	// 世界聊天冷却时间
	WorldChatInterval int32
	// 好友邀请奖励（填写邀请码）
	FriendInvitedReward int32
	// 好友礼物每日接收上限
	GiftRecMax int32
	// 抽卡系统：每天可观看广告次数上限
	GachaWatchMaxCount int32
	// 抽卡系统：每天可观看广告间隔时间，单位：秒
	GachaWatchInterTime int32
	// 抽卡系统：第一个是广告档位次数，第二个是500钻石档位，第三个是1500钻石档位，第四个无效
	GachaDrawRates []int32
	// 抽卡系统：抽卡一次获得的经验
	GachaDrawExp int32
	// 抽卡系统：第一个是广告档位次数，第二个是500钻石档位，第三个是1500钻石档位，第四个无效
	GachaDrawCostDiamonds []int32
	// 宠物抽卡系统：每天可观看广告间隔时间，单位：秒
	PetGachaWatchInterTime int32
	// 宠物抽卡系统：抽卡一次获得的经验
	PetGachaDrawExp int32
	// 圣物抽卡系统：观看广告可抽卡次数上限
	HallowsGachaAdvDrawMaxCount int32
	// 圣物抽卡系统：每天可观看广告次数上限
	HallowsGachaWatchMaxCount int32
	// 抽卡系统：观看广告可抽卡次数上限
	GachaAdvDrawMaxCount int32
	// 圣物抽卡系统：普通抽卡档位，下标为档位，值为次数，0档为广告初始次数
	HallowsGachaDrawRates []int32
	// 系统赠送头像框
	PlayerDefaultHeadFrame int32
	// 邮件列表显示上限
	MailMaxNum int32
	// 问卷奖励邮件ID
	QuestionnaireEmail int32
	// 创建账号奖励ID
	NewaccountEmail int32
	// 第二天邮件奖励ID
	NextDayMailReward int32
	// 充值重复返还比例
	RechargeRatio int32
	// 日常任务ID
	DailyTaskArr []int32
	// 周日常任务ID
	WeekTaskArr []int32
	// 好友数量上限
	FriendMaxNumber int32
	// 可拉黑上限
	BlacklistMax int32
	// 好友送礼物品id|数量
	GiftItemNum []int32
	// 玩家名字字符最大长度
	PlayerNameMaxLength int32
	// 好友礼物每日赠送上限
	GiftSendMax int32
	// GM工具发送邮件的过期时间，单位：天
	GMSendMailMaxDayTime int32
	// 创建角色时默认给玩家的钻石
	CreatePlayerDefaultDiamond int32
	// 创建角色时默认给玩家的金币
	CreatePlayerDefaultCoin int32
	// 玩家初始时装（使用称号时装第一个）
	PlayerInitDress int32
	// 主线关卡排名低于50%时，根据区间配置固定区间值和下面的增长值要一一对应。
	MainStagePassBounds []int32
	// 主线关卡排名低于50%时，根据区间配置固定增长值。
	MainStagePassAdds []int32
	// 邮件每日领取奖励
	DailyCollectEmail []int32
	// 邮件每周领取奖励
	WeeklyReceiveEmail int32
	// 性别配置（保密|男|女）
	GenderConfig []string
	// 修改个人信息钻石配置
	ChangeInforCost int32
	// 个性签名修改时间限制（秒）
	SignatureChangeTime int32
	// 等级基金
	GradeFund int32
	// 公会名上限
	AllianceNameLen int32
	// 公会描述上限
	AllianceNoticeLen int32
	// 创建公会花费
	AllianceCreateExpend []int32
	// 周卡对应礼包ID
	WeekCardGiftId []int32
	// 每日好友申请数量上限
	FriendApplyMaxCount int32
	// 好友数量上限
	FriendMaxCount int32
	// 好友黑名单数量上限
	FriendBlackMaxCount int32
	// 对战匹配范围查找时间
	MainRankMatchTime []int32
	// 对战每日胜利奖励次数
	MainBattleDailyWinTimes int32
	// 对战每日失败补给次数
	MainBattleDailyFailTimes int32
	// 对战初始杯数
	MainRankScoreInitial int32
	// 宝物抽取每日广告限次
	TreasureGachaAdTimes int32
	// 宝物抽取概率修正系数
	TreasureGachaProModify []int32
	// 玩家上阵英雄数量
	HeroLineUpNum int32
	// 初始上阵英雄id
	HeroLineUpId []int32
}




// TableGameConfigData 表格
type TableGameConfigData struct {
	file    string
	dataMap map[int32]*TableGameConfig
	Data    []*TableGameConfig
	md5     string
}

// load 加载
func (tb *TableGameConfigData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableGameConfig{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableGameConfig, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableGameConfig)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableGameConfig, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableGameConfigData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableGameConfig{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableGameConfig, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableGameConfig)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableGameConfigData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableGameConfigData) GetById(id int32) *TableGameConfig {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableGameConfigData) GetCloneById(id int32) *TableGameConfig {
	v := tb.dataMap[id]
	out := &TableGameConfig{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableGameConfigData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableGameConfigData) Foreach(call func(*TableGameConfig) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableGameConfigData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableGameConfigData) Clone() ITable {
	ntb := &TableGameConfigData{
		file:    tb.file,
		dataMap: make(map[int32]*TableGameConfig),
		Data:    make([]*TableGameConfig, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableGameConfig{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
