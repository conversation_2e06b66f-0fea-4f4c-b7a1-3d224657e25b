/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableSynergy struct {
	// ============= 变量定义 =============
	// 羁绊ID
	ID int32
	// 名称
	Name int32
	// 生效阶段
	Stage int32
	// 激活动效
	ActiveVFX int32
	// 类型
	Type int32
	// 类型参数
	Param string
	// 档位描述
	Desc []int32
	// 档位局内技能List
	BattleSkillList []int32
	// 档位技能生效目标List
	TargetList []int32
}




// TableSynergyData 表格
type TableSynergyData struct {
	file    string
	dataMap map[int32]*TableSynergy
	Data    []*TableSynergy
	md5     string
}

// load 加载
func (tb *TableSynergyData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableSynergy{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableSynergy, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableSynergy)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableSynergy, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableSynergyData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableSynergy{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableSynergy, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableSynergy)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableSynergyData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableSynergyData) GetById(id int32) *TableSynergy {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableSynergyData) GetCloneById(id int32) *TableSynergy {
	v := tb.dataMap[id]
	out := &TableSynergy{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableSynergyData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableSynergyData) Foreach(call func(*TableSynergy) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableSynergyData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableSynergyData) Clone() ITable {
	ntb := &TableSynergyData{
		file:    tb.file,
		dataMap: make(map[int32]*TableSynergy),
		Data:    make([]*TableSynergy, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableSynergy{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
