package player

import (
	"liteframe/internal/common/protos/dbstruct"
)

type Module interface {
	InitDB(db *dbstruct.UserDB) // TODO 排查  注意：各 module 里的运行时数据只能是缓存 db 数据，而不能用于修改 db 数据！！！
	OnCrossDay(natural bool, nowUnix int64)
}

type moduleGroup struct {
	modules []Module
}

func newModuleGroup() *moduleGroup {
	return &moduleGroup{}
}

func (m *moduleGroup) register(module Module) {
	m.modules = append(m.modules, module)
}

func (p *Player) initModuleGroup() {
	p.moduleGroup = newModuleGroup()

	// 初始化道具模块
	p.item = NewItem(p)
	p.register(p.item)

	// 初始化背包模块
	p.bag = NewBag(p)
	p.register(p.bag)

	// 初始化货币模块
	p.money = NewMoney(p)
	p.register(p.money)

	// 初始化玩家属性模块
	p.playerAttr = NewPlayerAttr(p)

	// 初始化玩家事件模块
	p.playerEvent = NewEventSystem(p)

	// 初始化掉落模块
	p.drop = NewDrop(p)

	// 好有模块初始化
	p.friend = NewFriends(p)

	// 初始化邮件模块
	p.mail = NewMail(p)
	p.register(p.mail)

	// 礼包码初始化
	p.redeemCode = NewRedeemCode(p)

	// 初始化玩家公会模块
	p.guild = NewGuild(p)
	p.register(p.guild)
	// 玩家商城
	p.shop = NewShop(p)
	p.register(p.shop)

	// 初始化玩家头像模块
	p.settings = NewSettings(p)
	p.register(p.settings)

	// 初始化七日签到模块
	p.sevenSignIn = NewSevenSignin(p)
	p.register(p.sevenSignIn)

	// 首冲
	p.firstCharge = NewFirstCharge(p)
	p.register(p.firstCharge)

	// 初始化充值返利模块
	p.topUpRebate = NewTopupRebate(p)
	p.register(p.topUpRebate)

	// 初始化月卡模块
	p.monthlyCard = NewMonthlyCard(p)
	p.register(p.monthlyCard)

	// 初始化等级基金模块
	p.gradedFund = NewGradedFund(p)
	p.register(p.gradedFund)

	// 初始化任务模块
	p.mission = NewMission(p)
	p.register(p.mission)

	// 初始化功能开放
	p.funcOpen = NewFuncOpen(p)
	p.register(p.funcOpen)

	// 初始化活动模块
	p.activity = NewActivity(p)
	p.register(p.activity)

	// 初始化常用宝箱模块
	p.commonBoxReward = NewCommonBoxReward(p)
	p.register(p.commonBoxReward)

	// 初始化月卡2.0模块
	p.monthlyCardNew = NewMonthlyCardNew(p)
	p.register(p.monthlyCardNew)

	// 初始化限时商店(日、周、月)礼包模块
	p.timeShop = NewTimeShop(p)
	p.register(p.timeShop)

	// 初始化千抽模块
	p.gachaBonus = NewGachaBonus(p)
	p.register(p.gachaBonus)

	// 初始化问卷模块
	p.questionnaire = NewQuestionnaire(p)
	p.register(p.questionnaire)

	// 初始化新手引导
	p.newGuide = NewNewGuide(p)
	p.register(p.newGuide)

	// 初始化爬塔
	p.tower = NewTowerData(p)
	p.register(p.tower)

	// 初始化日进斗金
	p.totalRecharge = NewTotalRecharge(p)
	p.register(p.totalRecharge)
	// 初始化好友邀请
	p.inviteTask = NewInviteTask(p)
	p.register(p.inviteTask)
	// 初始化竞技场模块
	p.arena = NewArena(p)
	p.register(p.arena)
	// 初始化天道修为
	p.heavenlyDao = NewHeavenlyDao(p)
	p.register(p.heavenlyDao)

	// 初始化特惠礼包
	p.weekCardData = NewWeekCardData(p)
	p.register(p.weekCardData)

	// 初始化挂机奖励
	p.hook = NewHook(p)
	p.register(p.hook)

	// 初始化英雄
	p.hero = NewHero(p)
	p.register(p.hero)

	// 初始化阵容
	p.lineup = NewLineup(p)
	p.register(p.lineup)

	// 初始化赛季Buff
	p.seasonBuff = &SeasonBuff{}
	p.seasonBuff.Init(p)
	p.register(p.seasonBuff)

	// 初始化战斗模块
	p.battle = NewBattle(p)
	p.register(p.battle)

	// 初始化奖杯模块
	p.trophy = NewTrophy(p)
	p.register(p.trophy)

	// 初始化宝物模块
	p.treasure = NewTreasure(p)
	p.register(p.treasure)

	// 初始化红点系统
	p.redDot = NewRedDot(p)
	p.register(p.redDot)
}

var (
	_ Module = (*Bag)(nil)
)

func (p *Player) Item() *Item {
	return p.item
}

func (p *Player) Bag() *Bag {
	return p.bag
}

func (p *Player) Money() *Money {
	return p.money
}

func (p *Player) Drop() *Drop {
	return p.drop
}

func (p *Player) Mail() *Mail {
	return p.mail
}

func (p *Player) Setting() *Settings {
	return p.settings
}

func (p *Player) Shop() *Shop {
	return p.shop
}

func (p *Player) FirstCharge() *FirstCharge {
	return p.firstCharge
}

func (p *Player) SevenSignin() *SevenSignin {
	return p.sevenSignIn
}

func (p *Player) TopupRebate() *TopupRebate {
	return p.topUpRebate
}

func (p *Player) MonthlyCard() *MonthlyCard {
	return p.monthlyCard
}

func (p *Player) GradedFund() *GradedFund {
	return p.gradedFund
}

func (p *Player) Mission() *Mission {
	return p.mission
}

func (p *Player) FuncOpen() *FuncOpen {
	return p.funcOpen
}

func (p *Player) Activity() *Activity {
	return p.activity
}

func (p *Player) RedeemCode() *RedeemCode {
	return p.redeemCode
}

func (p *Player) CommonBoxReward() *CommonBoxReward {
	return p.commonBoxReward
}

func (p *Player) MonthlyCardNew() *MonthlyCardNew {
	return p.monthlyCardNew
}

func (p *Player) TimeShop() *TimeShop {
	return p.timeShop
}

func (p *Player) GachaBonus() *GachaBonus {
	return p.gachaBonus
}

func (p *Player) Questionnaire() *Questionnaire {
	return p.questionnaire
}

func (p *Player) NewGuide() *NewGuide {
	return p.newGuide
}

func (p *Player) Guild() *Guild {
	return p.guild
}

func (p *Player) Tower() *TowerData {
	return p.tower
}

func (p *Player) ToTalRecharge() *TotalRecharge {
	return p.totalRecharge
}

func (p *Player) InviteTask() *InviteTask {
	return p.inviteTask
}

func (p *Player) Arena() *Arena {
	return p.arena
}

func (p *Player) HeavenlyDao() *HeavenlyDao {
	return p.heavenlyDao
}

func (p *Player) WeekCardData() *WeekCardData {
	return p.weekCardData
}

func (p *Player) Hook() *Hook {
	return p.hook
}
func (p *Player) Friends() *Friends {
	return p.friend
}

func (p *Player) Hero() *Hero {
	return p.hero
}

func (p *Player) Lineup() *Lineup {
	return p.lineup
}

func (p *Player) SeasonBuff() *SeasonBuff {
	return p.seasonBuff
}

func (p *Player) GetBattle() *Battle {
	return p.battle
}

func (p *Player) Trophy() *Trophy {
	return p.trophy
}

func (p *Player) Treasure() *Treasure {
	return p.treasure
}

func (p *Player) RedDot() *RedDot {
	return p.redDot
}
