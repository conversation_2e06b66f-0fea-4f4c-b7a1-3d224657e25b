﻿using Game.Core;
using LiteFrame.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static BattleServer.Game.Core.BattleConfig;

namespace BattleServer.Game
{
    public static class MatchManager
    {
        public static void Match(Dictionary<ulong, BattlePlayer> players, List<BattlePlayer> deadPlayers, Scene scene)
        {
            List<BattlePlayer> result = new List<BattlePlayer>();
            // 按照血量排序玩家
            for (int i = 0; i < players.Count; i++)
            {
                var player = players.ElementAt(i).Value;
                if (player.IsDead())
                    continue;
                result.Add(player);
            }

            result.Sort((x, y) => -x.GetHp().CompareTo(y.GetHp()));
            // 如果玩家数小于2，直接返回空列表
            if (result.Count < 2)
            {
                Log.Debug("[MatchManager] Not enough players to match.");
                return;
            }

            if (result.Count % 2 != 0)
            {
                // 如果玩家数为奇数，则把淘汰的玩家补位
                Log.Debug("[MatchManager] Odd number of players, add the Dead player.");
                result.Add(deadPlayers.ElementAt(0));
            }


            int battleIndex = 0;
            for (int i = 0; i < result.Count; i += 2)
            {
                var player1 = result[i];
                var player2 = result[i + 1];
                // 进行匹配逻辑
                Log.Debug($"Matching players: {player1.Info.Uid} vs {player2.Info.Uid}");

                scene.GetBattles().ElementAt(battleIndex).SetPlayers(player1, player2);
                battleIndex++;
            }
        }
    }  
}
