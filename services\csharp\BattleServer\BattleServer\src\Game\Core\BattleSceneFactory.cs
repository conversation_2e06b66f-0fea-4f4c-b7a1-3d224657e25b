using System;
using System.Collections.Generic;
using BattleServer.Game.AutoChess;
using BattleServer.Game.Core;
using LiteFrame.Framework;

namespace BattleServer.Game.Core
{
    /// <summary>
    /// 战斗场景工厂 - 负责创建和配置战斗场景
    /// </summary>
    public static class BattleSceneFactory
    {
        /// <summary>
        /// 创建自走棋战斗场景
        /// </summary>
        /// <param name="battleId">战斗ID</param>
        /// <param name="playerIds">玩家ID列表</param>
        /// <param name="playerLineups">玩家阵容数据</param>
        /// <param name="gameServerId">目标GameServer ID</param>
        /// <returns>配置好的自走棋场景</returns>
        public static AutoChessScene CreateAutoChessScene(
            long battleId,
            List<long> playerIds,
            Dictionary<long, List<int>> playerLineups,
            string gameServerId = "10106")
        {
            // 验证参数
            ValidateParameters(battleId, playerIds, playerLineups);

            try
            {
                // 创建共享事件总线
                var eventBus = new BattleEventBus();

                // 创建场景
                var scene = new AutoChessScene(gameServerId, eventBus);

                // 初始化战斗但不启动状态机（推荐使用SceneManager.CreateAutoChessScene代替此工厂方法）
                scene.InitBattleWithoutStart(battleId, playerIds, playerLineups);

                // 立即启动状态机（保持旧行为兼容性）
                scene.StartBattleStateMachine();

                Log.Info($"[BattleSceneFactory] Created AutoChessScene for battle {battleId} with {playerIds.Count} players");
                return scene;
            }
            catch (Exception ex)
            {
                Log.Error($"[BattleSceneFactory] Failed to create AutoChessScene for battle {battleId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 验证创建场景的参数
        /// </summary>
        private static void ValidateParameters(long battleId, List<long> playerIds, Dictionary<long, List<int>> playerLineups)
        {
            if (battleId <= 0)
                throw new ArgumentException("Battle ID must be positive", nameof(battleId));

            if (playerIds == null || playerIds.Count == 0)
                throw new ArgumentException("Player IDs cannot be null or empty", nameof(playerIds));

            if (playerIds.Count < BattleConfig.Player.MinPlayers)
                throw new ArgumentException($"At least {BattleConfig.Player.MinPlayers} players required", nameof(playerIds));

            if (playerIds.Count > BattleConfig.Player.MaxPlayers)
                throw new ArgumentException($"At most {BattleConfig.Player.MaxPlayers} players allowed", nameof(playerIds));

            // 检查是否有重复的玩家ID
            var uniquePlayerIds = new HashSet<long>(playerIds);
            if (uniquePlayerIds.Count != playerIds.Count)
                throw new ArgumentException("Duplicate player IDs found", nameof(playerIds));

            // playerLineups可以为null，会在PlayerManager中处理
        }

        /// <summary>
        /// 创建测试用的自走棋场景
        /// </summary>
        /// <param name="playerCount">玩家数量</param>
        /// <returns>测试场景</returns>
        public static AutoChessScene CreateTestScene(int playerCount = 2)
        {
            if (playerCount < BattleConfig.Player.MinPlayers || playerCount > BattleConfig.Player.MaxPlayers)
                throw new ArgumentException($"Player count must be between {BattleConfig.Player.MinPlayers} and {BattleConfig.Player.MaxPlayers}");

            var battleId = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var playerIds = new List<long>();
            var playerLineups = new Dictionary<long, List<int>>();

            // 生成测试玩家数据
            for (int i = 0; i < playerCount; i++)
            {
                var playerId = 1000L + i;
                playerIds.Add(playerId);

                // 生成简单的测试阵容
                var lineup = new List<int> { 101 + i, 201 + i, 301 + i };
                playerLineups[playerId] = lineup;
            }

            return CreateAutoChessScene(battleId, playerIds, playerLineups, "test-server");
        }
    }

    /// <summary>
    /// 战斗场景管理器 - 管理多个战斗场景的生命周期
    /// </summary>
    public class BattleSceneManager : IDisposable
    {
        private readonly Dictionary<long, AutoChessScene> _activeScenes = new();
        private readonly object _lock = new();

        /// <summary>
        /// 创建并添加新的战斗场景
        /// </summary>
        public AutoChessScene CreateScene(long battleId, List<long> playerIds, Dictionary<long, List<int>> playerLineups, string gameServerId = "10106")
        {
            lock (_lock)
            {
                if (_activeScenes.ContainsKey(battleId))
                {
                    throw new InvalidOperationException($"Battle {battleId} already exists");
                }

                var scene = BattleSceneFactory.CreateAutoChessScene(battleId, playerIds, playerLineups, gameServerId);
                _activeScenes[battleId] = scene;

                Log.Info($"[BattleSceneManager] Created scene for battle {battleId}, total active scenes: {_activeScenes.Count}");
                return scene;
            }
        }

        /// <summary>
        /// 获取战斗场景
        /// </summary>
        public AutoChessScene GetScene(long battleId)
        {
            lock (_lock)
            {
                return _activeScenes.GetValueOrDefault(battleId);
            }
        }

        /// <summary>
        /// 根据玩家ID获取战斗场景
        /// </summary>
        public AutoChessScene GetSceneByPlayerId(long playerId)
        {
            lock (_lock)
            {
                foreach (var scene in _activeScenes.Values)
                {
                    if (scene.IsPlayerInBattle(playerId))
                    {
                        return scene;
                    }
                }
                return null;
            }
        }

        /// <summary>
        /// 移除并清理战斗场景
        /// </summary>
        public bool RemoveScene(long battleId)
        {
            lock (_lock)
            {
                if (_activeScenes.TryGetValue(battleId, out var scene))
                {
                    scene.Dispose();
                    _activeScenes.Remove(battleId);

                    Log.Info($"[BattleSceneManager] Removed scene for battle {battleId}, remaining active scenes: {_activeScenes.Count}");
                    return true;
                }
                return false;
            }
        }

        /// <summary>
        /// 获取活跃场景数量
        /// </summary>
        public int ActiveSceneCount
        {
            get
            {
                lock (_lock)
                {
                    return _activeScenes.Count;
                }
            }
        }

        /// <summary>
        /// 清理所有场景
        /// </summary>
        public void Dispose()
        {
            lock (_lock)
            {
                foreach (var scene in _activeScenes.Values)
                {
                    scene.Dispose();
                }
                _activeScenes.Clear();

                Log.Info("[BattleSceneManager] All scenes disposed");
            }
        }
    }
}
