using System;
using System.Collections.Generic;
using System.Linq;
using BattleServer.Game.Core;
using LiteFrame.Framework;

namespace BattleServer.Game.Player
{
    /// <summary>
    /// 对手配对管理器 - 负责4人自走棋的对手配对逻辑
    /// </summary>
    public class OpponentPairManager : BattleComponentBase
    {
        private List<long> _playerRanking; // 玩家排名（按血量排序）
        private Dictionary<long, long> _lastOpponents; // 记录上一回合的对手
        private string _logName;

        public OpponentPairManager()
        {
            _playerRanking = new List<long>();
            _lastOpponents = new Dictionary<long, long>();
            _logName = "OpponentPairManager";
        }

        protected override void OnInitialize()
        {
            _playerRanking.Clear();
            _lastOpponents.Clear();
            Log.Info($"[{_logName}] Initialized for battle {BattleId}");
        }

        protected override void OnClear()
        {
            _playerRanking.Clear();
            _lastOpponents.Clear();
        }

        /// <summary>
        /// 简化的配对方法 - 供AutoChessScene使用
        /// </summary>
        /// <param name="activePlayerIds">活跃玩家ID列表</param>
        /// <param name="playerManager">玩家管理器</param>
        /// <returns>对手配对列表</returns>
        public List<(long player1, long player2)> PairOpponents(List<long> activePlayerIds, PlayerManager playerManager)
        {
            // 获取所有玩家的血量信息（包括被淘汰的）
            var playerHealths = new Dictionary<long, int>();
            var allPlayerIds = playerManager.GetAllPlayerIds();

            foreach (var playerId in allPlayerIds)
            {
                playerHealths[playerId] = playerManager.GetPlayerHealth(playerId);
            }

            // 使用现有的配对逻辑
            return GenerateOpponentPairs(playerHealths, 1); // 简化实现，暂时使用回合1的逻辑
        }

        /// <summary>
        /// 生成对手配对 - 根据战斗文档的规则
        /// </summary>
        /// <param name="playerHealths">玩家血量字典</param>
        /// <param name="roundCount">当前回合数</param>
        /// <returns>对手配对列表</returns>
        public List<(long player1, long player2)> GenerateOpponentPairs(Dictionary<long, int> playerHealths, int roundCount)
        {
            var activePlayers = playerHealths.Where(p => p.Value > 0).Select(p => p.Key).ToList();
            var eliminatedPlayers = playerHealths.Where(p => p.Value <= 0).Select(p => p.Key).ToList();
            var playerPairs = new List<(long player1, long player2)>();

            Log.Info($"[{_logName}] Generating opponent pairs for round {roundCount}, active players: {activePlayers.Count}, eliminated: {eliminatedPlayers.Count}");

            // 根据策划文档的淘汰处理规则
            if (eliminatedPlayers.Count >= 3)
            {
                // 3人淘汰：游戏结束，不需要配对
                Log.Info($"[{_logName}] 3+ players eliminated, game should end");
                return playerPairs;
            }
            else if (eliminatedPlayers.Count == 2)
            {
                // 2人淘汰：移出匹配队列，剩余2人对战
                playerPairs = GenerateTwoPlayerPairs(activePlayers);
            }
            else if (eliminatedPlayers.Count == 1)
            {
                // 1人淘汰：保持4人匹配队列，被淘汰者填充匹配位置
                playerPairs = GenerateThreePlayerPairs(activePlayers, eliminatedPlayers, playerHealths, roundCount);
            }
            else
            {
                // 无人淘汰：正常4人配对
                if (roundCount == 1)
                {
                    playerPairs = GenerateRandomPairs(activePlayers);
                }
                else
                {
                    playerPairs = GenerateRankedPairs(activePlayers, playerHealths);
                }
            }

            // 记录本回合的对手关系
            UpdateLastOpponents(playerPairs);

            Log.Info($"[{_logName}] Generated {playerPairs.Count} opponent pairs for round {roundCount}");
            return playerPairs;
        }

        /// <summary>
        /// 第一回合随机配对
        /// </summary>
        private List<(long player1, long player2)> GenerateRandomPairs(List<long> activePlayers)
        {
            var pairs = new List<(long player1, long player2)>();
            var shuffledPlayers = activePlayers.OrderBy(x => Random.Shared.Next()).ToList();

            for (int i = 0; i < shuffledPlayers.Count - 1; i += 2)
            {
                var player1 = shuffledPlayers[i];
                var player2 = shuffledPlayers[i + 1];

                pairs.Add((player1, player2));

                Log.Info($"[{_logName}] Random pair: Player {player1} vs Player {player2}");
            }

            return pairs;
        }

        /// <summary>
        /// 按血量排序配对
        /// </summary>
        private List<(long player1, long player2)> GenerateRankedPairs(List<long> activePlayers, Dictionary<long, int> playerHealths)
        {
            var pairs = new List<(long player1, long player2)>();

            // 按血量排序（血量高的排在前面）
            var rankedPlayers = activePlayers
                .OrderByDescending(p => playerHealths[p])
                .ThenBy(p => p) // 血量相同时按ID排序保持稳定性
                .ToList();

            // 更新排名
            _playerRanking = rankedPlayers;

            // 配对逻辑：No.1 vs No.2, No.3 vs No.4
            for (int i = 0; i < rankedPlayers.Count - 1; i += 2)
            {
                var player1 = rankedPlayers[i];
                var player2 = rankedPlayers[i + 1];

                // 检查是否与上回合对手重复
                if (ShouldAvoidPairing(player1, player2))
                {
                    // 尝试调整配对（简化实现：如果有第3个玩家，与第3个配对）
                    if (i + 2 < rankedPlayers.Count)
                    {
                        var player3 = rankedPlayers[i + 2];
                        pairs.Add((player1, player3));
                        // player2需要与剩余玩家配对，这里先跳过，后续处理

                        Log.Info($"[{_logName}] Adjusted pair to avoid repeat: Player {player1} vs Player {player3}");
                        i++; // 跳过player3
                        continue;
                    }
                }

                pairs.Add((player1, player2));

                Log.Info($"[{_logName}] Ranked pair: Player {player1}(HP:{playerHealths[player1]}) vs Player {player2}(HP:{playerHealths[player2]})");
            }

            // 处理奇数个玩家的情况（3人时）
            if (rankedPlayers.Count == 3)
            {
                HandleThreePlayerPairing(pairs, rankedPlayers, playerHealths);
            }

            return pairs;
        }

        /// <summary>
        /// 生成2人对战配对（2人淘汰情况）
        /// </summary>
        private List<(long player1, long player2)> GenerateTwoPlayerPairs(List<long> activePlayers)
        {
            var pairs = new List<(long player1, long player2)>();

            if (activePlayers.Count == 2)
            {
                var player1 = activePlayers[0];
                var player2 = activePlayers[1];

                pairs.Add((player1, player2));

                Log.Info($"[{_logName}] Two player final: Player {player1} vs Player {player2}");
            }
            else
            {
                Log.Warning($"[{_logName}] Expected 2 active players but found {activePlayers.Count}");
            }

            return pairs;
        }

        /// <summary>
        /// 生成3人配对（1人淘汰情况）- 保持4人队列，被淘汰者填充位置
        /// </summary>
        private List<(long player1, long player2)> GenerateThreePlayerPairs(List<long> activePlayers, List<long> eliminatedPlayers, Dictionary<long, int> playerHealths, int roundCount)
        {
            var pairs = new List<(long player1, long player2)>();

            if (activePlayers.Count != 3)
            {
                Log.Warning($"[{_logName}] Expected 3 active players but found {activePlayers.Count}");
                return pairs;
            }

            // 使用被淘汰玩家填充第4个位置，保持4人配对队列
            var eliminatedPlayer = eliminatedPlayers.FirstOrDefault();
            var allPlayersForPairing = new List<long>(activePlayers);
            if (eliminatedPlayer != 0)
            {
                allPlayersForPairing.Add(eliminatedPlayer);
            }

            // 按血量排序（被淘汰玩家血量为0，排在最后）
            var rankedPlayers = allPlayersForPairing
                .OrderByDescending(p => playerHealths.GetValueOrDefault(p, 0))
                .ThenBy(p => p)
                .ToList();

            Log.Info($"[{_logName}] Three player pairing with eliminated player {eliminatedPlayer} as filler");

            // 修复配对逻辑：确保创建2个完整配对
            // No.1 vs No.2 (血量最高的两个活跃玩家), No.3 vs 被淘汰玩家
            for (int i = 0; i < rankedPlayers.Count; i += 2)
            {
                if (i + 1 < rankedPlayers.Count)
                {
                    var player1 = rankedPlayers[i];
                    var player2 = rankedPlayers[i + 1];

                    pairs.Add((player1, player2));

                    if (eliminatedPlayers.Contains(player1) || eliminatedPlayers.Contains(player2))
                    {
                        Log.Info($"[{_logName}] Pair with eliminated player: Player {player1} vs Player {player2} (one eliminated)");
                    }
                    else
                    {
                        Log.Info($"[{_logName}] Active player pair: Player {player1} vs Player {player2}");
                    }
                }
            }

            return pairs;
        }

        /// <summary>
        /// 处理3人配对情况（旧方法，保留兼容性）
        /// </summary>
        private void HandleThreePlayerPairing(List<(long player1, long player2)> pairs, List<long> rankedPlayers, Dictionary<long, int> playerHealths)
        {
            // 找到没有配对的玩家
            var pairedPlayers = new HashSet<long>();
            foreach (var (player1, player2) in pairs)
            {
                pairedPlayers.Add(player1);
                pairedPlayers.Add(player2);
            }

            var unpairedPlayer = rankedPlayers.FirstOrDefault(p => !pairedPlayers.Contains(p));

            if (unpairedPlayer != 0)
            {
                // 与AI配对（使用0表示AI对手）
                pairs.Add((unpairedPlayer, 0));
                Log.Info($"[{_logName}] Player {unpairedPlayer} paired with AI");
            }
        }

        /// <summary>
        /// 检查是否应该避免配对（与上回合对手重复）
        /// </summary>
        private bool ShouldAvoidPairing(long player1, long player2)
        {
            return _lastOpponents.TryGetValue(player1, out var lastOpponent) && lastOpponent == player2;
        }

        /// <summary>
        /// 更新上回合对手记录
        /// </summary>
        private void UpdateLastOpponents(List<(long player1, long player2)> currentPairs)
        {
            _lastOpponents.Clear();
            foreach (var (player1, player2) in currentPairs)
            {
                _lastOpponents[player1] = player2;
                _lastOpponents[player2] = player1;
            }
        }

        /// <summary>
        /// 获取当前玩家排名
        /// </summary>
        public List<long> GetPlayerRanking()
        {
            return new List<long>(_playerRanking);
        }

        /// <summary>
        /// 获取玩家的上一回合对手
        /// </summary>
        public long GetLastOpponent(long playerId)
        {
            return _lastOpponents.GetValueOrDefault(playerId, 0);
        }
    }
}
