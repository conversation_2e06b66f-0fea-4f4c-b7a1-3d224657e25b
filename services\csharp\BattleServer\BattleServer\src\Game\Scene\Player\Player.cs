﻿using BattleServer.Framework;
using BattleServer.Game.Core;
using BattleServer.Game.Player;
using BattleServer.Nats;
using BattleServer.Service;
using Game.Core;
using LiteFrame.Framework;
using System.Collections.Generic;
using System.Net.NetworkInformation;

namespace BattleServer.Game
{
    public class BattlePlayer
    {
        public int Rank { get; set; } = 0; // 玩家排名，默认0
        private bool IsEnter;
        private int BufferID;
        private bool IsReady;

        public bool IsRobot { get; set; }

        public PBBattlePlayerInfo? Info { get; set; }
        public PBBattleTeamInfo TeamInfo { get; set; }

        private List<int> OptionsBuffer = new List<int>();

        private List<Hero> NewHeroList = new List<Hero>();
        private Dictionary<int, Hero> HeroList = new Dictionary<int, Hero>();
        private List<Hero> LastBattleHeroList = new List<Hero> { }; // 上一场战斗的英雄列表
        // 棋盘上的英雄列表，未开始前的
        private Dictionary<int, Hero> LowerBoardHero = new Dictionary<int, Hero>();

        private List<Hero> CacheHeroList { get; set; } = new List<Hero> { };

        public BattleChess? battle = null;

        private int mStartCellId = 0;
        private int mEndCellId = 0;

        public BattlePlayer(PBBattlePlayerInfo info, PBBattleTeamInfo teamInfo)
        {
            IsEnter = false;
            IsReady = false;
            BufferID = 0;
            Info = info;
            TeamInfo = teamInfo;
            Info.Hp = 3;
        }

        public bool IsEnterBattle()
        {
            return IsEnter;
        }

        public int GetBufferId()
        {
            return BufferID;
        }

        public bool IsSelectBuffer()
        {
            return BufferID > 0;
        }

        public bool IsReadyToBattle()
        {
            return IsReady;
        }

        public void RandomBuffers()
        {
            Log.Debug($"[player] {Info.Uid} RandomBuffers Hp {Info.Hp}");
            Random random = new Random();

            TablePlayMode playMode = TablePlayMode.GetData(1);
            for (int i = 0; i < playMode.BuffList.Length; i++)
            {
                OptionsBuffer.Add(playMode.BuffList[i]);
                if (i >= 2)
                    break;
            }
        }

        public List<int> GetOptionsBuffer()
        {
            return OptionsBuffer;
        }

        public void InitCell(int startId, int endId)
        {
            if (startId < 1 || startId > 60 || endId < 1 || endId > 60)
            {
                Log.Error($"player {Info.Uid} init cell from {startId} to {endId} error");
            }
            mStartCellId = startId;
            mEndCellId = endId;

            Log.Debug($"player {Info.Uid} init cell from {mStartCellId} to {mEndCellId}");

            FlipBoard();
        }

        private void FlipBoard()
        {
            // 棋盘翻转,如过玩家在原来是在下方棋盘，现在在上方棋盘，则需要将所有英雄位置翻转
            // 如果玩家在上方棋盘，现在在下方棋盘，则需要将所有英雄位置翻转
            foreach (var hero in HeroList.Values)
            {
                if (hero.Position <=30 && IsUpperBoard())
                {
                    // 如果英雄在下方棋盘，现在在上方棋盘，则翻转位置
                    hero.Position = FlipToUpper(hero.Position);
                }
                else if (hero.Position > 30 && IsLowerBoard())
                {
                    // 如果英雄在上方棋盘，现在在下方棋盘，则翻转位置
                    hero.Position = FlipToLower(hero.Position);
                }
                Log.Debug($"[player] {Info.Uid} FlipBoard hero position {hero.Position} StartCellId {mStartCellId} EndCellId {mEndCellId}");
            }

            foreach(var hero in LastBattleHeroList)
            {
                if (hero.Position <= 30 && IsUpperBoard())
                {
                    // 如果英雄在下方棋盘，现在在上方棋盘，则翻转位置
                    hero.Position = FlipToUpper(hero.Position);
                }
                else if (hero.Position > 30 && IsLowerBoard())
                {
                    // 如果英雄在上方棋盘，现在在下方棋盘，则翻转位置
                    hero.Position = FlipToLower(hero.Position);
                }
                Log.Debug($"[player] {Info.Uid} FlipBoard last battle hero position {hero.Position} StartCellId {mStartCellId} EndCellId {mEndCellId}");
            }
        }

        public void GeneratedHeros()
        {
            if (IsDead())
            {
                Log.Error($"[player] {Info.Uid} GeneratedHeros but player is dead");
                return;
            }
            Log.Debug($"[player] {Info.Uid} GeneratedHeros Hp {Info.Hp}");

            Scene scene = battle.GetScene();

            int round = scene.GetRound();
            TablePlayMode playMode = TablePlayMode.GetData(1);

            int startRound = 0;
            int endRound = 0;
            int heroCount = 0;
            int level = 0;
            for (int i = 0; i < playMode.AddHero.Length; i++)
            {
                startRound = playMode.AddHero[i][0];
                endRound = playMode.AddHero[i][1];
                heroCount = playMode.AddHero[i][2];
                level = playMode.AddHero[i][3];

                if (round >= startRound && round <= endRound)
                {
                    break;
                }
            }

            int newHeroCount = heroCount;
            Log.Debug($"new hero count {newHeroCount}");

            Random random = new Random();
            for (int i = 0; i < newHeroCount; i++)
            {
                //int heroId = TeamInfo.Heros[random.Next(TeamInfo.Heros.Count)].Id;
                //先暂时从只定的英雄里随机
                int heroId = playMode.HeroList[random.Next(playMode.HeroList.Length)];
                TableHero heroRes = TableHero.GetData(heroId);
                Hero hero = new Hero(heroRes.ID);

                if (LowerBoardHero.Count >= 30)
                {
                    CacheHeroList.Add(hero);
                }
                else
                {
                    int[] positions = BestPosition.GetBestPosition(heroRes.HeroPosition);
                    for (int j = 0; j < positions.Length; j++)
                    {
                        int position = positions[j];
                        if (IsPositionEmpty(position))
                        {
                            SetBoardHero(position, hero);
                            if (IsUpperBoard())
                            {
                                hero.Position = FlipToUpper(position);
                            }
                            else
                            {
                                hero.Position = position;
                            }
                            Log.Debug($"[player] {Info.Uid} GeneratedHeros hero {hero.GetResID()} position {hero.Position}");
                            NewHeroList.Add(hero);
                            HeroList.Add(hero.Position, hero);
                            break;
                        }
                    }
                }
            }
        }

        ////
        //public void UpdateNewHeroToBoard()
        //{
        //    Log.Debug($"[player] {Info.Uid} UpdateNewHeroToBoard Hp {Info.Hp}");
        //    if (NewHeroList.Count <= 0)
        //    {
        //        Log.Error($"[player] {Info.Uid} NewHeroList is empty");
        //        return;
        //    }
        //    foreach (var hero in NewHeroList)
        //    {
        //        //AddHero(hero.Position, hero);
        //        if (HeroList.ContainsKey(hero.Position))
        //        {
        //            Log.Error($"[player] {Info.Uid} UpdateNewHeroToBoard cellId {hero.Position} already has a hero");
        //        }
        //        else
        //        {
        //            HeroList.Add(hero.Position, hero);
        //        }
        //    }
        //    NewHeroList.Clear();
        //}

        private Hero GetHero(int cellID)
        {
            Hero hero = null;
            HeroList.TryGetValue(cellID, out hero);
            if (hero == null)
            {
                return null;
            }

            return hero;
        }

        private void AddHero(int cellId, Hero hero)
        {
            Log.Debug($"[player] {Info.Uid} AddHero cellId {cellId} hero {hero.GetResID()}");
            if (cellId < 1 || cellId > 60)
            {
                Log.Error($"[player] {Info.Uid} AddHero cellId {cellId} is out of range from {1} to {60}");
                return;
            }
            if (HeroList.ContainsKey(cellId))
            {
                Log.Error($"[player] {Info.Uid} AddHero cellId {cellId} already has a hero");
            }
            else
            {
                HeroList.Add(cellId, hero);
                Log.Debug($"[player] {Info.Uid} AddHero cellId {cellId} success");

                if (cellId >= 1 && cellId <= 30)
                {
                    Log.Debug($"[player] {Info.Uid} AddHero cellId {cellId} in LowerBoardHero");
                    SetBoardHero(cellId, hero);
                }
                else if (cellId >= 31 && cellId <= 60)
                {
                    Log.Debug($"[player] {Info.Uid} AddHero cellId {cellId} in UpperBoardHero, flip to lower board");
                    int lowerCellId = FlipToLower(cellId);
                    SetBoardHero(lowerCellId, hero);
                }
                else
                {
                    Log.Error($"[player] {Info.Uid} AddHero cellId {cellId} is out of range from {1} to {60}");
                }
            }
        }

        private void DeleteHero(int cellId)
        {
            Log.Debug($"[player] {Info.Uid} DeleteHero cellId {cellId}");
            if (cellId < 1 || cellId > 60)
            {
                Log.Error($"[player] {Info.Uid} DeleteHero cellId {cellId} is out of range from {1} to {60}");
                return;
            }

            if (HeroList.ContainsKey(cellId))
            {
                HeroList.Remove(cellId);
                Log.Debug($"[player] {Info.Uid} DeleteHero cellId {cellId} success");

                if (cellId >= 1 && cellId <= 30)
                {
                    Log.Debug($"[player] {Info.Uid} DeleteHero cellId {cellId} in LowerBoardHero");
                    DeleteBoardHero(cellId);
                }
                else if (cellId >= 31 && cellId <= 60)
                {
                    Log.Debug($"[player] {Info.Uid} DeleteHero cellId {cellId} in UpperBoardHero, flip to lower board");
                    int lowerCellId = FlipToLower(cellId);
                    DeleteBoardHero(lowerCellId);
                }
            }
            else
            {
                Log.Error($"[player] {Info.Uid} DeleteHero cellId {cellId} not found in LowerBoardHero");
            }
        }

        private void SetBoardHero(int cellID, Hero hero)
        {
            if (cellID < 1 || cellID > 30)
            {
                Log.Error($"[player] {Info.Uid} SetBoardHero cellID {cellID} is out of range from {1} to {30}");
                return;
            }
            if (hero == null)
            {
                Log.Error($"[player] {Info.Uid} SetBoardHero hero is null");
                return;
            }
            if (LowerBoardHero.ContainsKey(cellID))
            {
                Log.Error($"[player] {Info.Uid} SetBoardHero cellID {cellID} already has a hero");
            }
            else
            {
                LowerBoardHero.Add(cellID, hero);
                Log.Debug($"[player] {Info.Uid} SetBoardHero cellID {cellID} hero {hero.GetResID()} success");
            }
        }

        private void DeleteBoardHero(int cellID)
        {
            if (cellID < 1 || cellID > 30)
            {
                Log.Error($"[player] {Info.Uid} DeleteBoardHero cellID {cellID} is out of range from {1} to {30}");
                return;
            }
            if (LowerBoardHero.ContainsKey(cellID))
            {
                LowerBoardHero.Remove(cellID);
                Log.Debug($"[player] {Info.Uid} DeleteBoardHero cellID {cellID} success");
            }
            else
            {
                Log.Error($"[player] {Info.Uid} DeleteBoardHero cellID {cellID} not found in LowerBoardHero");
            }
        }

        private bool IsPositionEmpty(int position)
        {
            if (position < 1 || position > 30)
            {
                Log.Error($"[player] {Info.Uid} IsPositionEmpty position {position} is out of range from {0} to {30}");
                return false;
            }

            bool empty = LowerBoardHero.ContainsKey(position);
            return !empty;
        }

        public void ClearBattle()
        {
            Log.Debug($"[player] {Info.Uid} ClearBattle Hp {Info.Hp}");
            IsEnter = false;
            IsReady = false;
            BufferID = 0;
            OptionsBuffer.Clear();
            battle = null;
            mStartCellId = 0;
            mEndCellId = 0;
            NewHeroList.Clear();
            Rank = 0;
        }

        public bool IsDead()
        {
            return Info.Hp <= 0;
        }

        public int GetHp() { return Info.Hp; }


        // 进入战斗场景
        public void OnEnterBattle()
        {
            Log.Debug($"[player] {Info.Uid} OnEnterBattle Hp {Info.Hp}");
            IsEnter = true;
            IsReady = false;
            BufferID = 0;
        }

        public void OnSelectBuffer(int bufferId)
        {
            Log.Debug($"[player] {Info.Uid} OnSelectBuffer {bufferId} Hp {Info.Hp}");
            BufferID = bufferId;

            // 选完buff后，需要直接生成英雄，以便在消息中通知客户端
            GeneratedHeros();
        }

        public void OnMoveHero(List<PBMoveOperation> moveOps)
        {
            Log.Debug($"[player] {Info.Uid} OnMoveHero moveOps count {moveOps.Count} Hp {Info.Hp}");
            if (IsRobot) return;
            if (IsDead()) return;

            if (moveOps.Count <= 0)
            {
                Log.Error($"[player] {Info.Uid} OnMoveHero moveOps count is zero");
                return;
            }
            foreach (var moveOp in moveOps)
            {
                if (moveOp.FromGridId < mStartCellId || moveOp.FromGridId > mEndCellId || moveOp.ToGridId < mStartCellId || moveOp.ToGridId > mEndCellId)
                {
                    Log.Error($"[player] {Info.Uid} OnMoveHero move from {moveOp.FromGridId} to {moveOp.ToGridId} error self id from {mStartCellId} to {mEndCellId}");
                    continue;
                }
                if (moveOp.ToGridId == moveOp.FromGridId)
                {
                    Log.Debug($"[player] {Info.Uid} OnMoveHero move from {moveOp.FromGridId} to {moveOp.ToGridId} is same, skip");
                    continue;
                }

                if (GetHero(moveOp.FromGridId) == null)
                {
                    Log.Error($"[player] {Info.Uid} OnMoveHero move from {moveOp.FromGridId} to {moveOp.ToGridId} error no hero in cell");
                    continue;
                }

                Log.Debug($"[player] {Info.Uid} OnMoveHero move from {moveOp.FromGridId} to {moveOp.ToGridId}");
                Hero? fromHero = GetHero(moveOp.FromGridId);
                Hero? toHero = GetHero(moveOp.ToGridId);

                //先删除原位置的英雄
                DeleteHero(moveOp.FromGridId);
                DeleteHero(moveOp.ToGridId);
                //再添加到新位置
                fromHero.Position = moveOp.ToGridId;
                AddHero(moveOp.ToGridId, fromHero);
                if (toHero != null)
                {
                    toHero.Position = moveOp.FromGridId; // 如果目标位置有英雄，则交换位置
                    //再添加到新位置
                    AddHero(moveOp.FromGridId, toHero);
                }
            }
        }
        public Hero OnMergeHero(int FromGridId, int ToGridId)
        {
            Log.Debug($"[player] {Info.Uid} OnMergeHero from {FromGridId} to {ToGridId}");
            if (IsRobot) return null;
            if (IsDead()) return null;
            if (FromGridId == ToGridId)
            {
                Log.Error($"[player] {Info.Uid} OnMergeHero from {FromGridId} to {ToGridId} is same, skip");
                return GetHero(FromGridId);
            }

            if (FromGridId < mStartCellId || FromGridId > mEndCellId || ToGridId < mStartCellId || ToGridId > mEndCellId)
            {
                Log.Error($"[player] {Info.Uid} OnMergeHero from {FromGridId} to {ToGridId} error self id from {mStartCellId} to {mEndCellId}");
                return null;
            }

            var srcHero = GetHero(FromGridId);
            var destHero = GetHero(ToGridId);
            if (srcHero == null)
            {
                Log.Error($"[player] {Info.Uid} OnMergeHero from {FromGridId} error no hero in cell");
                return null;
            }
            if (destHero == null)
            {
                Log.Error($"[player] {Info.Uid} OnMergeHero to {ToGridId} error no hero in cell");
                return null;
            }

            var newHero = GetHero(ToGridId);
            newHero.LevelUp();
            DeleteHero(FromGridId); // 删除源英雄

            if (CacheHeroList.Count > 0)
            {
                Hero hero = CacheHeroList[0];
                hero.Position = FromGridId; // 将缓存的英雄位置设置为FromGridId
                AddHero(FromGridId, hero);
                CacheHeroList.RemoveAt(0);
            }

            return newHero;
        }

        public void OnBattleReady()
        {
            Log.Debug($"[player] {Info.Uid} OnReady Hp {Info.Hp}");

            IsReady = true;
        }

        public void OnRoundBattleEnd(bool win)
        {
            if (IsDead())
            {
                Log.Debug($"[player] {Info.Uid} OnRoundBattleEnd but player is already dead");
                return;
            }

            Log.Debug($"[player] {Info.Uid} OnRoundBattleEnd win {win} hp {Info.Hp}");
            if (!win)
            {
                Info.Hp -= 1;
                Log.Debug($"[player] {Info.Uid} OnBattleFailed hp {Info.Hp}");
                if (Info.Hp <= 0)
                {
                    OnDeath();
                }
            }
        }

        private void OnDeath()
        {
            Log.Debug($"[player] {Info.Uid} OnDeath");
            Info.Hp = 0;
            IsEnter = false;
            IsReady = false;
            BufferID = 0;
            OptionsBuffer.Clear();
            battle.GetScene().OnPlayerDeath(Info.Uid);
            Rank = battle.GetScene().GetPlayers().Count + 1; // 死亡后排名为最后一名

            if (IsRobot)
                return;

            var req = new RoundBattleEndReq();
            req.Uid = Info.Uid;
            req.WinUid = 0;
            req.LoseUid = 0;
            req.IsEnd = true; // 设置整场战斗是否即将结束
            req.TimeoutTimestamp = 10; // 结算确认超时
            NatsClient.GameServiceClient.RoundBattleEnd(req, Info.ServerId).ConfigureAwait(false);

            var endReq = new BattleEndReq();
            endReq.Uid = Info.Uid;
            endReq.BattleId = 0;
            endReq.WinStreak = 0;
            endReq.Rank = 1;
            endReq.Heros.Add(ToPBBattleHeroInfoList());
            NatsClient.GameServiceClient.BattleEnd(endReq, Info.ServerId).ConfigureAwait(false);

            SceneManager.Instance.RemovePlayerScene(Info.Uid);
        }

        public PBBattleCampInfo ToPBBattleCampInfo()
        {
            var campInfo = new PBBattleCampInfo();
            campInfo.Player = new PBBattlePlayerInfo
            {
                Uid = Info.Uid,
                Name = $"Player{Info.Uid}",
                Level = 1,
                Throphy = 1000,
                ServerId = Info.ServerId,
                Hp = Info.Hp
            };

            campInfo.BoardInfo.Add(GetHeroBoard());
            return campInfo;
        }

        public List<PBCheckerBoard> GetHeroBoard()
        {
            var list = new List<PBCheckerBoard>();
            foreach (var hero in HeroList.Values)
            {
                var board = new PBCheckerBoard();
                board.GridID = hero.Position;
                board.Hero = hero.ToPB();
                Log.Debug($"[player] {Info.Uid} GetHeroBoard hero {hero.GetResID()} position {hero.Position} cell from {mStartCellId} to {mEndCellId}");
                list.Add(board);
            }

            return list;
        }

        public List<PBCheckerBoard> GetNewHeroBoard()
        {
            var list = new List<PBCheckerBoard>();
            foreach (var hero in NewHeroList)
            {
                var board = new PBCheckerBoard();
                board.GridID = hero.Position;
                board.Hero = hero.ToPB();
                Log.Debug($"[player] {Info.Uid} GetNewHeroBoard hero {hero.GetResID()} position {hero.Position} cell from {mStartCellId} to {mEndCellId}");
                list.Add(board);
            }
            return list;
        }

        public void SaveToLastBattleHeroList()
        {
            Log.Debug($"[player] {Info.Uid} SaveToLastBattleHeroList Hp {Info.Hp}");
            LastBattleHeroList.Clear();
            foreach (var hero in HeroList.Values)
            {
                var lastHero = new Hero(hero.GetResID())
                {
                    Position = hero.Position,
                    Level = hero.Level
                };
                LastBattleHeroList.Add(lastHero);
            }
        }

        public List<PBCheckerBoard> GetLastBattleHeroBoard()
        {
            var list = new List<PBCheckerBoard>();
            foreach (var hero in LastBattleHeroList)
            {
                var board = new PBCheckerBoard();
                board.GridID = hero.Position;
                board.Hero = hero.ToPB();
                Log.Debug($"[player] {Info.Uid} GetLastBattleHeroBoard hero {hero.GetResID()} position {hero.Position} cell from {mStartCellId} to {mEndCellId}");
                list.Add(board);
            }
            return list;
        }

        public List<PBBattleHeroInfo> ToPBBattleHeroInfoList()
        {
            var list = new List<PBBattleHeroInfo>();
            foreach (var hero in HeroList.Values)
            {
                list.Add(hero.ToPB());
            }
            return list;
        }

        private int FlipToUpper(int lowerBoardId)
        {
            if (IsLowerBoard())
            {
                Log.Debug($"[player] FlipToUpper lowerBoardId {lowerBoardId} is in lower board");
                return lowerBoardId; // 如果在上方棋盘，则不需要翻转
            }
            if (lowerBoardId < 1 || lowerBoardId > 30)
            {
                Log.Error($"[player] FlipToUpper lowerBoardId {lowerBoardId} is out of range (1-30)");
                return lowerBoardId; // 如果在下方棋盘，则不需要翻转
            }

            // ID转坐标：行号 = ceil(ID/6)，列号 = (ID-1)%6 + 1
            int row = (int)Math.Ceiling((double)lowerBoardId / 6);
            int col = ((lowerBoardId - 1) % 6) + 1;

            // 翻转：新行号 = 6 - 原行号
            int newRow = 6 - row;

            // 转换为上方ID：(新行号-1) × 6 + 列号 + 30
            int upperBoardId = (newRow - 1) * 6 + col + 30;
            Log.Info($"[player] FlipToUpper lowerBoardId {lowerBoardId} to upperBoardId {upperBoardId}");
            return upperBoardId;
        }

        private int FlipToLower(int upperBoardId)
        {
            if (IsUpperBoard())
            {
                Log.Debug($"[player] FlipToLower upperBoardId {upperBoardId} is in upper board");
                return upperBoardId; // 如果在下方棋盘，则不需要翻转
            }
            if (upperBoardId < 31 || upperBoardId > 60)
            {
                Log.Error($"[player] FlipToLower upperBoardId {upperBoardId} is out of range (31-60)");
                return upperBoardId; // 如果在上方棋盘，则不需要翻转
            }

            // ID转坐标：行号 = ceil(ID/6)，列号 = (ID-1)%6 + 1
            int row = (int)Math.Ceiling((double)(upperBoardId - 30) / 6);
            int col = ((upperBoardId - 30 - 1) % 6) + 1;
            // 翻转：新行号 = 6 - 原行号
            int newRow = 6 - row;
            // 转换为下方ID：(新行号-1) × 6 + 列号 + 0
            int lowerBoardId = (newRow - 1) * 6 + col;
            Log.Info($"[player] FlipToLower upperBoardId {upperBoardId} to lowerBoardId {lowerBoardId}");
            return lowerBoardId;
        }

        public bool IsLowerBoard()
        {
            // 检查cellId是否在下方棋盘范围内
            return mStartCellId >= 1 && mEndCellId <= 30;
        }

        private bool IsUpperBoard()
        {
            // 检查cellId是否在上方棋盘范围内
            return mStartCellId >= 31 && mEndCellId <= 60;
        }
    }
}
