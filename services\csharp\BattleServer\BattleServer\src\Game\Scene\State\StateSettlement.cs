﻿using BattleServer.Service;
using BattleServer.Nats;
using Game.Core;
using LiteFrame.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BattleServer.Game
{
    public class StateSettlement : State
    {
        public StateSettlement(StateComponent stateComponent) : base(stateComponent)
        {
            
        }
        public override void OnInit()
        {
            base.OnInit();
        }

        public override void OnEnter()
        {
            Log.Debug("[StateSettlement] OnEnter");

            var players = _stateComponent.GetScene().GetPlayers();
            foreach (var player in players.Values)
            {
                if (player.IsRobot)
                {
                    // 机器人不需要结算
                    continue;
                }
                //player.NotifyBattleEnd();
                var req = new BattleEndReq();
                req.Uid = player.Info.Uid;
                req.BattleId = _stateComponent.GetScene().SceneID;
                req.WinStreak = 0;
                if (player.IsDead())
                {
                    req.Rank = player.Rank;
                }
                else
                {
                    req.Rank = 1; // 胜利的玩家排名为1
                }
                    req.Heros.Add(player.ToPBBattleHeroInfoList());
                NatsClient.GameServiceClient.BattleEnd(req, player.Info.ServerId).ConfigureAwait(false);
            }

            _stateComponent.GetScene().OnDestory();
        }

        public override void OnUpdate(float deltaTime)
        {
            
        }
    }
}
