﻿
using LiteFrame.Framework;

namespace BattleServer.Game
{
    public class StateStart : State
    {
        public StateStart(StateComponent stateComponent) : base(stateComponent)
        {
            
        }

        public override void OnEnter()
        {
            Log.Error("state enter start");

            _stateComponent.ChangeState(StateType.Wait);
        }

        public override void OnUpdate(float deltaTime)
        {
            
        }
    }
}
