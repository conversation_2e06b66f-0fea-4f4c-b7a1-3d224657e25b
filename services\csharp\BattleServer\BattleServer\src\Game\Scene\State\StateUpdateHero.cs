﻿using BattleServer.Game.Core;
using BattleServer.Game.Player;
using BattleServer.Nats;
using BattleServer.Service;
using Game.Core;
using LiteFrame.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static BattleServer.Game.Core.BattleConfig;
using static System.Formats.Asn1.AsnWriter;

namespace BattleServer.Game
{
    public class StateUpdateHero : State
    {
        private int prepareTimeout;
        private int buffTimeout;
        public StateUpdateHero(StateComponent stateComponent) : base(stateComponent)
        {
        }

        public override void OnInit()
        {
            base.OnInit();
            Log.Debug("[StateUpdateHero] OnInit");

            TablePlayMode playMode = TablePlayMode.GetData(1);
            prepareTimeout = playMode.PreDuration * 1000 + 5; // 准备阶段时间 + 5秒缓冲
            buffTimeout = playMode.BuffDuration * 1000 + 5; // Buff选择时间 + 5秒缓冲
        }

        public override void OnEnter()
        {
            Log.Debug("[StateUpdateHero] OnEnter");
            base.OnEnter();

            int round = _stateComponent.GetScene().GetRound();

            var players = _stateComponent.GetScene().GetPlayers();

            foreach (var player in players.Values)
            {
                player.GeneratedHeros();
            }

            foreach (var battle in _stateComponent.GetScene().GetBattles())
            {
                if (round == 1)
                {
                    battle.OnRoundStart(false);
                }
                else
                {
                    battle.OnRoundStart(true);
                }   
            }

            //var timeoutTimestamp = TimeHelper.GetCurrentTimestampMs() + prepareTimeout;
            //var buffTimeoutTimestamp = timeoutTimestamp + buffTimeout;
            //foreach (var player in players.Values)
            //{
            //    if (player.IsDead()) continue;
            //    if (player.IsRobot) continue;
            //    //player.NotifyRoundStart(timeoutTimestamp, buffTimeoutTimestamp);
            //    var req = new RoundStartReq();
            //    req.TimeoutTimestamp = timeoutTimestamp; // 准备阶段65秒超时
            //    req.BuffTimeoutTimestamp = buffTimeoutTimestamp; // buff选择25秒超时

            //    req.Uid = player.Info.Uid;
            //    req.Buffers.Add(player.GetOptionsBuffer());

            //    req.PlayerBoards.AddRange(player.GetNewHeroBoard());
            //    //req.PlayerBoards.AddRange(player.battle.GetBoardInfo());

            //    NatsClient.GameServiceClient.RoundStart(req, player.Info.ServerId).ConfigureAwait(false);

            //    Log.Debug($"[StateUpdateHero] RoundStartReq sent to player {player.Info.Uid} on server {player.Info.ServerId} prepare timeout {timeoutTimestamp} buffer time out {buffTimeoutTimestamp}");
            //}

            _stateComponent.ChangeState(StateType.Preparation);
        }

        public override void OnUpdate(float deltaTime)
        {
            
        }
    }
}
