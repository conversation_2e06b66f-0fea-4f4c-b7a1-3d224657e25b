﻿//*********************************************************
// PacketAddressHelper
// Author:  caoguoyun
// Desc:    协议地址封装接口
//*********************************************************

using LiteFrame.Framework;
using LiteFrame.Game;
using BattleServer.Game;
using NATS.Client.Core;


namespace LiteFrame.Helper
{
    public static class ServerPacketHelper
    {
        //public static void ToClient(Packet packet)
        //{
        //    if (packet != null)
        //    {
        //        packet.TargetAddress.SetAsClient();
        //    }
        //}
        //#region 发送给World相关
        //public static void ToWorldStage(Packet packet, int nZwid)
        //{
        //    if (packet != null)
        //    {
        //        if (!CheckPacketZwidIsOK(nZwid))
        //        {
        //            Log.Error($"ToWorldStage CheckPacketZwidIsOK==false!!! packetID:{packet.MsgID}, " +
        //                $"selfZwid:{ServerNumHelper.ZoneWorldId}, selfServerNum:{ServerNumHelper.ServerNum}, toZwid:{nZwid}");
        //            return;
        //        }
        //        packet.TargetAddress.SetAsStage(nZwid, ServerNumHelper.World);
        //    }
        //}

        //public static void ToWorldService(Packet packet, int nZwid, ServiceID serviceID)
        //{
        //    if (packet != null)
        //    {
        //        if (!CheckPacketZwidIsOK(nZwid))
        //        {
        //            Log.Error($"ToWorldService CheckPacketZwidIsOK==false!!! packetID:{packet.MsgID}, " +
        //                $"selfZwid:{ServerNumHelper.ZoneWorldId}, selfServerNum:{ServerNumHelper.ServerNum}, toZwid:{nZwid}");
        //            return;
        //        }

        //        packet.TargetAddress.SetAsService(nZwid, ServerNumHelper.World, serviceID);
        //    }
        //}

        //public static void ToWorldGuidAndSend(IMessage msg, int nZwid, long guid)
        //{
        //    Packet beforePkt = Packet.Create(msg);
        //    ServerPacketHelper.ToWorldGuid(beforePkt, nZwid, guid);
        //    PacketDispatcherManager.Dispatch(beforePkt);

        //}

        //public static void ToWorldGuid(Packet packet, int nZwid, long guid)
        //{
        //    if (packet != null)
        //    {
        //        if (!CheckPacketZwidIsOK(nZwid))
        //        {
        //            Log.Error($"ToWorldService CheckPacketZwidIsOK==false!!! packetID:{packet.MsgID}, " +
        //                $"selfZwid:{ServerNumHelper.ZoneWorldId}, selfServerNum:{ServerNumHelper.ServerNum}, toZwid:{nZwid}");
        //            return;
        //        }

        //        packet.TargetAddress.SetAsGuid(nZwid, ServerNumHelper.World, guid);
        //    }
        //}
        //#endregion

        ///// <summary>
        ///// 这个函数一般用于你传送给非本Server进程(同一Server可以通过SS消息发送)并且非本ZoneWorld(不同服)的消息传送
        ///// 用这个时候慎重，因为消息可能会饶一大圈，这个一般来讲用用于大跨服，比如跨服组队之类的
        ///// 之所以说饶一大圈，因为这个传送路径可能如下：你所在的Server-->你的World-->MQ-->他的World-->他所在的Server
        ///// </summary>
        ///// <param name="packet">消息包</param>
        ///// <param name="nTargetZwid">对方的ZoneWorldID</param>
        ///// <param name="guid">对方的guid</param>
        ///// <param name="nMyZwid">你的ZoneWorldID</param>
        //public static void ToOtherZoneWorldGuid(Packet packet, int nTargetZwid, long nTargetGuid, int nMyZwid)
        //{
        //    if (nTargetZwid == 0 || nMyZwid == 0)
        //    {
        //        Log.Error($"ToSpecialWorldGuid nTargetZwid:{nTargetZwid} == 0 || nMyZwid:{nMyZwid} == 0!!! packetID:{packet.MsgID} Error!!!");
        //        return;
        //    }

        //    if (nTargetZwid == nMyZwid)         //这种情况退化成同服的ToWorldGuid
        //    {
        //        ToWorldGuid(packet, nTargetZwid, nTargetGuid);
        //        return;
        //    }

        //    //if (ServerNumHelper.SelfIsTServer())    //如果本身是T服那么直接上MQ(这种做不做待讨论，现在还是做了,毕竟能省一些路径)
        //    //{

        //    //}
        //    //else   //这个是真正的路径
        //    //{
        //    packet.TargetAddress.SetAsOtherZoneWorldGuid(nTargetZwid, ServerNumHelper.World, nTargetGuid, nMyZwid);
        //    //            } 
        //}

        ////用于原服Server-》TServer|KServer 或者 TServer|KServer-》原服Server
        //public static void ToOtherServerService(Packet packet, int nTargetZwid, int nTargetServerNum, ServiceID serviceID)
        //{
        //    if (packet != null)
        //    {
        //        int nZwid = NetWorkHelper.GetServerZwidByServerNum(ServerNumHelper.ServerNum);
        //        if (nZwid == ServerNumHelper.INVALID_ZONE_WORLD_ID)
        //        {
        //            ELog.Error($"ToOtherServerService, nZwid Error!!! toServerNum:{ServerNumHelper.ServerNum}, packetID:{packet.MsgID}",
        //                EFT.TServer);
        //        }
        //        else
        //        {
        //            packet.TargetAddress.SetAsOtherServerService(nTargetZwid, (ushort)nTargetServerNum, serviceID);
        //        }

        //    }
        //}

        ////用于原服Server-》TServer|KServer的玩家 或者 TServer|KServer-》原服Server的玩家
        //public static void ToOtherServerGuid(Packet packet, int nTargetZwid, int nTargetServerNum, long guid)
        //{
        //    if (packet != null)
        //    {
        //        int nZwid = NetWorkHelper.GetServerZwidByServerNum(ServerNumHelper.ServerNum);
        //        if (nTargetZwid == ServerNumHelper.ZoneWorldId)
        //        {
        //            ELog.Error($"ToOtherServerGuid, nTargetZwid == ServerNumHelper.ZoneWorldId Error!!! toServerNum:{ServerNumHelper.ServerNum}, packetID:{packet.MsgID}",
        //               EFT.TServer);
        //            return;
        //        }
        //        if (nZwid == ServerNumHelper.INVALID_ZONE_WORLD_ID)
        //        {
        //            ELog.Error($"ToOtherServerGuid, nZwid Error!!! toServerNum:{ServerNumHelper.ServerNum}, packetID:{packet.MsgID}",
        //                EFT.TServer);
        //        }
        //        else
        //        {
        //            packet.TargetAddress.SetAsOtherServerGuid(nTargetZwid, (ushort)ServerNumHelper.World, guid);
        //        }

        //    }
        //}



        ////用于原服Server-》TServer|KServer 或者 TServer|KServer-》原服Server
        //public static void ToOtherServerGlobalMgr(Packet packet, int nTargetZwid, int nTargetServerNum, GlobalManagerType globalMgrType)
        //{
        //    if (packet != null)
        //    {
        //        int nZwid = NetWorkHelper.GetServerZwidByServerNum(ServerNumHelper.ServerNum);
        //        if (nZwid == ServerNumHelper.INVALID_ZONE_WORLD_ID)
        //        {
        //            ELog.Error($"ToServerGlobalMgr, nZwid Error!!! toServerNum:{ServerNumHelper.ServerNum}, packetID:{packet.MsgID}",
        //                EFT.TServer);
        //        }
        //        else
        //        {
        //            packet.TargetAddress.SetAsOtherGlobalMgr(nTargetZwid, (ushort)nTargetServerNum, globalMgrType);
        //        }
        //    }
        //}

        ////发往匹配服
        //public static void ToMatchServerStage(Packet packet)
        //{
        //    if (packet != null)
        //    {
        //        int nZwid = NetWorkHelper.GetServerZwidByServerNum(ServerNumHelper.ServerNum);
        //        if (nZwid == ServerNumHelper.INVALID_ZONE_WORLD_ID)
        //        {
        //            ELog.Error($"ToServerGlobalMgr, nZwid Error!!! toServerNum:{ServerNumHelper.ServerNum}, packetID:{packet.MsgID}",
        //                EFT.TServer);
        //        }
        //        else
        //        {
        //            packet.TargetAddress.SetAsStage(0, ServerNumHelper.MatchServer);
        //        }
        //    }
        //}


        //public static void ToSelfServerStage(Packet packet)
        //{
        //    if (packet != null)
        //    {
        //        /*
        //        if (!CheckPacketZwidIsOK(nZwid))
        //        {
        //            Log.Error($"ToSelfServerStage CheckPacketZwidIsOK==false!!! packetID:{packet.MsgID}, " +
        //                $"selfZwid:{ServerNumHelper.ZoneWorldId}, selfServerNum:{ServerNumHelper.ServerNum}, toZwid:{nZwid}");
        //            return;
        //        }
        //        */
        //        packet.TargetAddress.SetAsStage(0, ServerNumHelper.ServerNum);
        //    }
        //}


        //public static void ToSelfServerService(Packet packet, ServiceID serviceID)
        //{
        //    if (packet != null)
        //    {
        //        //int nZwid = NetWorkHelper.GetServerZwidByServerNum(ServerNumHelper.ServerNum);
        //        //if (nZwid == ServerNumHelper.INVALID_ZONE_WORLD_ID)
        //        //{
        //        //    ELog.Error($"ToServerGlobalMgr, nZwid Error!!! toServerNum:{ServerNumHelper.ServerNum}, packetID:{packet.MsgID}",
        //        //        EFT.TServer);
        //        //}
        //        //else
        //        //{
        //        //    packet.TargetAddress.SetAsService(nZwid, ServerNumHelper.ServerNum, serviceID);
        //        //}
        //        packet.TargetAddress.SetAsService(nZwid, ServerNumHelper.ServerNum, serviceID);
        //    }
        //}
        public static void ToSelfServerGlobalMgr(Packet packet)
        {
            if (packet != null)
            {
                //int nZwid = NetWorkHelper.GetServerZwidByServerNum(ServerNumHelper.ServerNum);
                //if (nZwid == ServerNumHelper.INVALID_ZONE_WORLD_ID)
                //{
                //    ELog.Error($"ToServerGlobalMgr, nZwid Error!!! toServerNum:{ServerNumHelper.ServerNum}, packetID:{packet.MsgID}",
                //        EFT.TServer);
                //}
                //else
                //{
                //    packet.TargetAddress.SetAsGlobalMgr(nZwid, 0, 1);
                //}

                packet.TargetAddress.SetAsGlobalMgr(0, 0, 1);
            }
        }
        //public static void ToSelfServerScene(Packet packet, ushort nSceneID)
        //{
        //    if (packet != null)
        //    {
        //        //int nZwid = NetWorkHelper.GetServerZwidByServerNum(ServerNumHelper.ServerNum);
        //        //if (nZwid == ServerNumHelper.INVALID_ZONE_WORLD_ID)
        //        //{
        //        //    ELog.Error($"ToServerGlobalMgr, nZwid Error!!! toServerNum:{ServerNumHelper.ServerNum}, packetID:{packet.MsgID}",
        //        //        EFT.TServer);
        //        //}
        //        //else
        //        //{
        //        //    packet.TargetAddress.SetAsScene(nZwid, ServerNumHelper.ServerNum, nSceneID);
        //        //}

        //        packet.TargetAddress.SetAsScene(0, 0, nSceneID);
        //    }
        //}
        //public static void ToSelfServerGamePlayer(Packet packet, uint nPlayerID)
        //{
        //    if (packet != null)
        //    {
        //        int nZwid = NetWorkHelper.GetServerZwidByServerNum(ServerNumHelper.ServerNum);
        //        if (nZwid == ServerNumHelper.INVALID_ZONE_WORLD_ID)
        //        {
        //            ELog.Error($"ToSelfServerGamePlayer, nZwid Error!!! toServerNum:{ServerNumHelper.ServerNum}, packetID:{packet.MsgID}",
        //                EFT.TServer);
        //        }
        //        else
        //        {
        //            packet.TargetAddress.SetAsGamePlayer(nZwid, ServerNumHelper.ServerNum, nPlayerID);
        //        }
        //    }
        //}

        //private static bool CheckPacketZwidIsOK(int nZwid)
        //{
        //    if (ServerNumHelper.SelfIsNormalServer())   //普通Server，如果发送普通消息，只能发送给自己的World 
        //    {
        //        if (nZwid != ServerNumHelper.ZoneWorldId)
        //        {
        //            return false;
        //        }
        //    }
        //    else if (ServerNumHelper.SelfIsKServer())    //KServer只能发给自己战区的World
        //    {
        //        if (!KServerComponent.Instance.IsThisKServerWorld(nZwid))
        //        {
        //            return false;
        //        }
        //    }

        //    return true;
        //}

        //// 调用这个函数给该服务器上所有玩家都广播一个消息，如果参数2填true，这个消息就会发送到所有在线玩家客户端，如果为false，则会在服务器上每个玩家所在的工作单元展开
        //public static void BroadCastToSelfServerAllPlayer(IMessage msg, bool toClient)
        //{
        //    if (msg == null)
        //    {
        //        return;
        //    }

        //    S2S_BroadCastToServerAllPlayer reportMsg = new S2S_BroadCastToServerAllPlayer();
        //    reportMsg.msg = msg;
        //    reportMsg.toClient = toClient;
        //    Packet packet = Packet.Create(reportMsg);
        //    ServerPacketHelper.ToSelfServerGlobalMgr(packet, GlobalManagerType.SERVER_SCENE_MANAGER);
        //    PacketDispatcherManager.Dispatch(packet);
        //}
    }
}