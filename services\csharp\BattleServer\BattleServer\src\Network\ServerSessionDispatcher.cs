﻿//*********************************************************
// Game
// Author:  Jasen
// Date  :  2022-11-30
//*********************************************************

using LiteFrame.Framework;
using BattleServer.Framework;
using BattleServer.Game;
using BattleServer.Server;
using NLog.Targets;
using System.Net.Sockets;
using BattleServer.src.service;

namespace LiteFrame.Game
{
    public class ServerSessionDispatcher : BasePacketDispatcher
    {
        //private Service netService;
        //public Service NetService
        //{
        //    get
        //    {
        //        if (netService == null)
        //        {
        //            netService = ServiceManager.Instance.GetService(ServiceID.NetWorkId);
        //        }
        //        return netService;
        //    }
        //}

        //private Service inCommingService;
        //public Service InCommingService
        //{
        //    get
        //    {
        //        if (inCommingService == null)
        //        {
        //            inCommingService = ServiceManager.Instance.GetService(ServiceID.ServerPlayerIncomming);
        //        }
        //        return inCommingService;
        //    }
        //}

        public override void OnRead(Packet packet, Session session)
        {
            //if (session.Address.m_eType == MessageAddress.EType.GamePlayer)
            //{
            //    packet.SourceAddress.SetAsClient();
            //    packet.TargetAddress.SetAsNone();

            //    ClientProxyComponent clientProxy = session.Parent?.GetComponentInParent<ClientProxyComponent>();
            //    if (clientProxy != null)
            //    {
            //        clientProxy.PacketFromClient(packet);
            //    }
            //}
        }

        public override void OnSend(Packet packet, Session session)
        {
            //if (session.Address.m_eType == MessageAddress.EType.GamePlayer)
            //{
            //    ClientProxyComponent clientProxy = session.Parent?.GetComponentInParent<ClientProxyComponent>();
            //    if (clientProxy != null)
            //    {
            //        clientProxy.PacketToClient(packet);
            //    }
            //}
        }

        protected override WorkUnitBase SeekTargetWorkUnit(Packet packet, out bool bSeekBreak)
        {
            bSeekBreak = false;
            WorkUnitBase targetWorkUnit = null;

            MessageAddress source = packet.SourceAddress;
            MessageAddress target = packet.TargetAddress;

            //if (source.m_eType == MessageAddress.EType.Client)
            //{
            //    targetWorkUnit = ThreadObject.CurrentWorkUnit;
            //}
            //else if (target.m_eType == MessageAddress.EType.Client)
            //{
            //    targetWorkUnit = ThreadObject.CurrentWorkUnit;
            //}
            //else if (source.m_eType == MessageAddress.EType.ClientProxy)
            //{
            //    targetWorkUnit = ThreadObject.CurrentWorkUnit;
            //}
            //else if (target.m_eType == MessageAddress.EType.ClientProxy)
            //{
            //    targetWorkUnit = ThreadObject.CurrentWorkUnit;
            //}

            //else if (ServerNumHelper.IsServer(target.m_nServerNum)
            //    && !PacketHelper.IsToOtherZoneWorld(target))
            //{
            //    //就是要往本Server发
            //    if (ServerNumHelper.ServerNum == target.m_nServerNum)
            //    {
            //        targetWorkUnit = DoSelfServerOpera(this, packet, target, out bSeekBreak);
            //    }
            //    else    //往不同的Server发
            //    {
            //        //Server发往TServer或者KServer 
            //        if (ServerNumHelper.SelfIsNormalServer() && (ServerNumHelper.IsKServer(target.m_nServerNum) ||
            //            ServerNumHelper.IsTServer(target.m_nServerNum)))
            //        {
            //            targetWorkUnit = NetService.WorkUnit;
            //        }
            //        //TServer或者KServer发往Server
            //        else if ((ServerNumHelper.SelfIsKServer() || ServerNumHelper.SelfIsTServer()) && ServerNumHelper.IsNormalServer(target.m_nServerNum))
            //        {
            //            targetWorkUnit = NetService.WorkUnit;
            //        }
            //        else
            //        {
            //            Log.Error($"Seek Target WorkUnit Fail, TargetServerID Is Different!");
            //        }
            //    }
            //}
            //else
            //{
            //    //Service netService = ServiceManager.Instance.GetService(ServiceID.NetWorkId);
            //    targetWorkUnit = NetService.WorkUnit;
            //}

            targetWorkUnit = DoSelfServerOpera(this, packet, target, out bSeekBreak);

            return targetWorkUnit;
        }

        private static WorkUnitBase DoSelfServerOpera(ServerSessionDispatcher dispatcher, Packet packet, MessageAddress target, out bool bSeekBreak)
        {
            bSeekBreak = false;
            WorkUnitBase targetWorkUnit = null;

            switch (target.m_eType)
            {
                //case MessageAddress.EType.GamePlayer:
                //    {
                //        if (target.m_nPlayerID != 0)
                //        {
                //            UnitPlayerComponent unitPlayer = UnitPlayerFinder.Instance.GetUnitPlayer(target.m_nPlayerID);
                //            if (unitPlayer != null)
                //            {
                //                unitPlayer.GetUnitPlayerSceneStatus(out ushort eStatus, out ushort nSceneID);
                //                bool bChangeScene = (eStatus == (ushort)UNIT_PLAYER_COMPONENT_STATUS.IS_CHANGING_SCENE);
                //                if (bChangeScene)
                //                {
                //                    ThreadObject.CurrentWorkUnit.PostPacket(packet);
                //                    bSeekBreak = true;
                //                }
                //                else
                //                {
                //                    if (nSceneID == 0)
                //                    {
                //                        //Service service = ServiceManager.Instance.GetService(ServiceID.ServerPlayerIncomming);
                //                        targetWorkUnit = dispatcher.InCommingService.WorkUnit;
                //                    }
                //                    else
                //                    {
                //                        Scene targetScene = SceneManager.Instance.GetScene(nSceneID);
                //                        if (targetScene != null)
                //                        {
                //                            targetWorkUnit = targetScene.WorkUnit;
                //                        }
                //                        else
                //                        {
                //                            Log.Error($"Seek Target WorkUnit Fail, Game Player Scene Is Not Found!");
                //                        }
                //                    }
                //                }
                //            }
                //            else
                //            {
                //                Log.Error($"Seek Target WorkUnit Fail, Game Player Is Not Found! {target.m_nPlayerID}");
                //            }
                //        }
                //        else
                //        {
                //            Log.Error($"Seek Target WorkUnit Fail, Game Player ID Is Invalid!");
                //        }
                //    }
                //    break;
                //case MessageAddress.EType.Stage:
                //    {
                //        targetWorkUnit = ServerStage.Instance.WorkUnit;
                //    }
                //    break;
                case MessageAddress.EType.Scene:
                    {
                        //if (target.m_nSceneID != 0)
                        //{
                        //    Scene targetScene = SceneManager.Instance.GetScene(target.m_nSceneID);
                        //    if (targetScene != null)
                        //    {
                        //        targetWorkUnit = targetScene.WorkUnit;
                        //    }
                        //    else
                        //    {
                        //        Log.Error($"Seek Target WorkUnit Fail, Scene Is Not Found!");
                        //    }
                        //}
                        //else
                        //{
                        //    Log.Error($"Seek Target WorkUnit Fail, Scene ID Is Invalid!");
                        //}
                        targetWorkUnit = SceneManager.Instance.WorkUnit;
                    }
                    break;
                case MessageAddress.EType.Service:
                    {
                        //ServiceID serviceID;
                        //{
                        //    serviceID.m_eType = (EServiceType)target.m_nServiceType;
                        //    serviceID.m_nIndex = target.m_nServiceIndex;
                        //}
                        //Service targetService = ServiceManager.Instance.GetService(serviceID);
                        //if (targetService != null)
                        //{
                        //    targetWorkUnit = targetService.WorkUnit;
                        //}
                        //else
                        //{
                        //    Log.Error($"Seek Target WorkUnit Fail, Service Is Not Found!");
                        //}
                        targetWorkUnit = ServiceManager.Instance.WorkUnit;
                    }
                    break;
                case MessageAddress.EType.GlobalMgr:
                    {
                        //targetWorkUnit = ServerGlobalManagerGetter.GetGlobalManagerWorkUnit((GlobalManagerType)target.m_nGlobalMgrType);
                        //if (targetWorkUnit == null)
                        //{
                        //    Log.Error($"Seek Target WorkUnit Fail, GlobalMgr Is Not Found!");
                        //}
                        targetWorkUnit = SceneManager.Instance.WorkUnit;
                    }
                    break;
                default:
                    {
                        Log.Error($"Seek Target WorkUnit Fail, Target Address Type Is Invalid!");
                    }
                    break;
            }

            return targetWorkUnit;
        }

        protected override Session SeekTargetSession(Packet packet, Session session, out bool bNeedSend, out bool bSeekBreak, out bool bError)
        {
            bNeedSend = false;
            bSeekBreak = false;
            bError = false;
            Session targetSession = null;

            MessageAddress source = packet.SourceAddress;
            MessageAddress target = packet.TargetAddress;

            switch(target.m_eType)
            { 
                case MessageAddress.EType.Service:
                {
                    //ServiceID serviceID;
                    //{
                    //    serviceID.m_eType = (EServiceType)target.m_nServiceType;
                    //    serviceID.m_nIndex = target.m_nServiceIndex;
                    //}
                    //Service targetService = ServiceManager.Instance.GetService(serviceID);
                    //if (targetService != null)
                    //{
                    //    targetWorkUnit = targetService.WorkUnit;
                    //}
                    //else
                    //{
                    //    Log.Error($"Seek Target WorkUnit Fail, Service Is Not Found!");
                    //}
                    targetSession = ServiceManager.Instance.GetSession();
                }
                break;
            case MessageAddress.EType.GlobalMgr:
                {
                    //targetWorkUnit = ServerGlobalManagerGetter.GetGlobalManagerWorkUnit((GlobalManagerType)target.m_nGlobalMgrType);
                    //if (targetWorkUnit == null)
                    //{
                    //    Log.Error($"Seek Target WorkUnit Fail, GlobalMgr Is Not Found!");
                    //}
                    targetSession = SceneManager.Instance.GetSession();
                }
                break;
            default:
                {
                    Log.Error($"Seek Target Session Fail, Target Address Type Is Invalid!");
                }
                break;
            }

                //if (source.m_eType == MessageAddress.EType.Client)
                //{
                //    targetSession = session;
                //}
                //else if (target.m_eType == MessageAddress.EType.Client)
                //{
                //    bNeedSend = true;
                //    targetSession = session;
                //}
                //else if (source.m_eType == MessageAddress.EType.ClientProxy)
                //{
                //    targetSession = session;
                //}
                //else if (target.m_eType == MessageAddress.EType.ClientProxy)
                //{
                //    targetSession = session;
                //}
                //else if (target.m_nServerNum == ServerNumHelper.ServerNum
                //    && !PacketHelper.IsToOtherZoneWorld(packet.TargetAddress))
                //{
                //    switch (target.m_eType)
                //    {
                //        //case MessageAddress.EType.GamePlayer:
                //        //    {
                //        //        UnitPlayerComponent unitPlayer = UnitPlayerFinder.Instance.GetUnitPlayer(target.m_nPlayerID);
                //        //        if (unitPlayer != null)
                //        //        {
                //        //            ClientSessionComponent clientSession = unitPlayer.ComponentParent.GetComponent<ClientSessionComponent>();
                //        //            if (clientSession != null)
                //        //            {
                //        //                targetSession = clientSession.Session;
                //        //            }
                //        //            else
                //        //            {
                //        //                Log.Error($"Seek Target Session Fail, Game Player Session Is Not Found!");
                //        //                bError = true;
                //        //            }
                //        //        }
                //        //        else
                //        //        {
                //        //            Log.Error($"Seek Target Session Fail, Game Player Is Not Found!");
                //        //            bError = true;
                //        //        }
                //        //    }
                //        //    break;
                //        //case MessageAddress.EType.Stage:
                //        //    {
                //        //        SessionComponent sessionComponent = ServerStage.Instance.GetComponent<SessionComponent>();
                //        //        if (sessionComponent != null)
                //        //        {
                //        //            targetSession = sessionComponent.m_Session;
                //        //        }
                //        //        else
                //        //        {
                //        //            Log.Error($"Seek Target Session Fail, sessionComponent == null!");
                //        //            bError = true;
                //        //        }
                //        //    }
                //        //    break;
                //        case MessageAddress.EType.Scene:
                //            {
                //                Scene scene = SceneManager.Instance.GetScene(target.m_nSceneID);
                //                if (scene != null)
                //                {
                //                    SceneSessionComponent sceneSession = scene.GetComponent<SceneSessionComponent>();
                //                    if (sceneSession != null)
                //                    {
                //                        targetSession = sceneSession.Session;
                //                    }
                //                    else
                //                    {
                //                        Log.Error($"Seek Target Session Fail, Scene Session Is Not Found!");
                //                        bError = true;
                //                    }
                //                }
                //                else
                //                {
                //                    Log.Error($"Seek Target Session Fail, Scene Is Not Found!");
                //                    bError = true;
                //                }
                //            }
                //            break;
                //        //case MessageAddress.EType.Service:
                //        //    {
                //        //        // Do Not Need A Concrete Session For The Moment.
                //        //    }
                //        //    break;
                //        //case MessageAddress.EType.GlobalMgr:
                //        //    {
                //        //        if (target.m_nGlobalMgrType == (byte)GlobalManagerType.SERVER_SCENE_MANAGER)
                //        //        {
                //        //            targetSession = SceneManager.Instance.GetSession();
                //        //        }
                //        //        else if (target.m_nGlobalMgrType == (byte)GlobalManagerType.SERVER_STOP_MANAGER)
                //        //        {
                //        //            targetSession = ServerStopManager.Instance.GetSession();
                //        //        }
                //        //    }
                //        //    break;
                //        default:
                //            {
                //                Log.Error($"Seek Target Session Fail, Target Address Type Is Invalid!");
                //                bError = true;
                //            }
                //            break;
                //    }
                //}
                //else
                //{
                //    bNeedSend = true;
                //    //if (PacketHelper.IsToOtherZoneWorld(packet.TargetAddress))
                //    //{
                //    //    //这个不能取m_nZwid，应该取m_nBakZwid，也就是发送者的Zwid
                //    //    targetSession = NetWorkHelper.GetOrCreateSession(target.m_nBakZwid, target.m_nServerNum);
                //    //}
                //    ////去往MatchServer
                //    //else if (ServerNumHelper.IsMatchServer(target.m_nServerNum))
                //    //{
                //    //    if (ServerNumHelper.IsNormalServer(ServerNumHelper.ServerNum))
                //    //    {
                //    //        targetSession = NetWorkHelper.GetOrCreateSession(ServerNumHelper.ZoneWorldId, ServerNumHelper.World);
                //    //    }
                //    //    //在TServer上 走MQ發送
                //    //    else
                //    //    {

                //    //    }

                //    //}
                //    //else
                //    //{
                //    //    if (ServerNumHelper.World == target.m_nServerNum &&
                //    //    packet.SourceAddress.m_nServerNum == 0)
                //    //    {
                //    //        packet.SourceAddress.m_nServerNum = ServerNumHelper.ServerNum;
                //    //    }
                //    //    targetSession = NetWorkHelper.GetOrCreateSession(target.m_nZwid, target.m_nServerNum);
                //    //}
                //}

                

            return targetSession;
        }

        protected override bool Precheck(Session session, Packet packet)
        {
            //if (session != null && packet != null)
            //{
            //    if (session.Address.m_eType == MessageAddress.EType.GamePlayer)
            //    {
            //        Unit unit = session.Parent?.ComponentParent as Unit;
            //        if (unit != null)
            //        {
            //            if (unit.IsCanLogic == false)
            //            {
            //                Log.Error($"IsCanLogic fail, unit.GUID={unit.GUID}, packet.MsgID={packet.MsgID}");
            //                return false;
            //            }
            //        }
            //        else
            //        {
            //            Log.Error($"IsCanLogic unit is null, packetpacket.MsgID={packet.MsgID}");
            //            return false;
            //        }
            //    }
            //}

            return true;
        }
    }
}
