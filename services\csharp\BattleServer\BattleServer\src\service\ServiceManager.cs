﻿using LiteFrame.Framework;
using BattleServer.Server;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BattleServer.Nats;
using BattleServer.Service;
using BattleServer.config;
using LiteFrame.Game;

namespace BattleServer.src.service
{
    public class ServiceManager : GlobalManager<ServiceManager>
    {
        public NatsServer? mNatsServer;
        private SessionComponent? mSessionComponet;
        private BattleService? battleService;

        private int mCount = 0;

        public bool Init()
        {
            MessageAddress address = new MessageAddress();
            address.SetAsService(0, 0);

            mSessionComponet = new SessionComponent();
            SessionComponentSystem.OnAwake1(mSessionComponet, address);
            return true;
        }

        public override void OnStart()
        {
            base.OnStart();

            // 初始化 NATS 服务端（接收消息）
            mNatsServer = new NatsServer(ConfigHelper.Config.Common.Nats.Servers, ConfigHelper.Config.ServerID);

            //battleService = new BattleService();
            battleService = new BattleService(mSessionComponet);
            mNatsServer.SubscribeServiceAsync(battleService);

            Log.Debug("ServiceManager Start");
        }

        public override void OnStop()
        {
            base.OnStop();

            Log.Debug("ServiceManager Stop");
        }

        public override void Tick(int nDeltaTime)
        {
            base.Tick(nDeltaTime);

            //Log.Debug("ServiceManager Tick");

            if (mCount <= 0)
            {
                mCount++;

                using var cts = new CancellationTokenSource();
                Task.Run(async () =>
                {
                    //battleService.TestMsg();
                },cts.Token);
            }
        }

        public override void OnDestroy()
        {
            base.OnDestroy();
        }

        public Session GetSession()
        {
            return mSessionComponet.m_Session;
        }
    }
}
