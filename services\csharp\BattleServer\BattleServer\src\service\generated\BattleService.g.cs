// <auto-generated />
// Code generated by protoc-gen-rpc-wrap. DO NOT EDIT.
// versions:
// - protoc-gen-rpc-wrap v1.0.0
// - protoc             v3.16.0
// source: BattleService.proto

using BattleServer.Nats;

namespace BattleServer.Service;

public interface IBattleService : INatsService
{
	public CreateBattleResp CreateBattle(CreateBattleReq request);
	public EnterBattleResp EnterBattle(EnterBattleReq request);
	public SelectBufferResp SelectBuffer(SelectBufferReq request);
	public MergeHeroResp MergeHero(MergeHeroReq request);
	public ReadyBattleResp BattleReady(ReadyBattleReq request);
	public EndBattleResp EndBattle(EndBattleReq request);
	public LeaveBattleResp LeaveBattle(LeaveBattleReq request);
}

public partial class BattleService : IBattleService
{
	public void SubjectAsyncAll(NatsServer server)
	{
		server.SubscribeAsync<CreateBattleReq,CreateBattleResp>("/natsrpc.BattleService/CreateBattle","BattleService",this.CreateBattle);
		server.SubscribeAsync<EnterBattleReq,EnterBattleResp>($"/{server.ServerId}/natsrpc.BattleService/EnterBattle","BattleService",this.EnterBattle);
		server.SubscribeAsync<SelectBufferReq,SelectBufferResp>($"/{server.ServerId}/natsrpc.BattleService/SelectBuffer","BattleService",this.SelectBuffer);
		server.SubscribeAsync<MergeHeroReq,MergeHeroResp>($"/{server.ServerId}/natsrpc.BattleService/MergeHero","BattleService",this.MergeHero);
		server.SubscribeAsync<ReadyBattleReq,ReadyBattleResp>($"/{server.ServerId}/natsrpc.BattleService/BattleReady","BattleService",this.BattleReady);
		server.SubscribeAsync<EndBattleReq,EndBattleResp>($"/{server.ServerId}/natsrpc.BattleService/EndBattle","BattleService",this.EndBattle);
		server.SubscribeAsync<LeaveBattleReq,LeaveBattleResp>($"/{server.ServerId}/natsrpc.BattleService/LeaveBattle","BattleService",this.LeaveBattle);
	}
}
