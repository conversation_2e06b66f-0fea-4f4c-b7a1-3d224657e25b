// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: descriptor.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace BattleServer.Service {

  /// <summary>Holder for reflection information generated from descriptor.proto</summary>
  public static partial class DescriptorReflection {

    #region Descriptor
    /// <summary>File descriptor for descriptor.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static DescriptorReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChBkZXNjcmlwdG9yLnByb3RvEgduYXRzcnBjGiBnb29nbGUvcHJvdG9idWYv",
            "ZGVzY3JpcHRvci5wcm90bzo3Cg11c2Vfc2VydmVyX2lkEh4uZ29vZ2xlLnBy",
            "b3RvYnVmLk1ldGhvZE9wdGlvbnMY0IYDIAEoCDoxCgdwdWJsaXNoEh4uZ29v",
            "Z2xlLnByb3RvYnVmLk1ldGhvZE9wdGlvbnMY0oYDIAEoCEI6WiFsaXRlZnJh",
            "bWUvaW50ZXJuYWwvY29tbW9uL25hdHNycGOqAhRCYXR0bGVTZXJ2ZXIuU2Vy",
            "dmljZWIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Google.Protobuf.Reflection.DescriptorReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, new pb::Extension[] { DescriptorExtensions.UseServerId, DescriptorExtensions.Publish }, null));
    }
    #endregion

  }
  /// <summary>Holder for extension identifiers generated from the top level of descriptor.proto</summary>
  public static partial class DescriptorExtensions {
    /// <summary>
    /// 发给指定server
    /// </summary>
    public static readonly pb::Extension<global::Google.Protobuf.Reflection.MethodOptions, bool> UseServerId =
      new pb::Extension<global::Google.Protobuf.Reflection.MethodOptions, bool>(50000, pb::FieldCodec.ForBool(400000, false));
    /// <summary>
    ///bool async_request = 50001; // 异步请求
    /// </summary>
    public static readonly pb::Extension<global::Google.Protobuf.Reflection.MethodOptions, bool> Publish =
      new pb::Extension<global::Google.Protobuf.Reflection.MethodOptions, bool>(50002, pb::FieldCodec.ForBool(400016, false));
  }

}

#endregion Designer generated code
