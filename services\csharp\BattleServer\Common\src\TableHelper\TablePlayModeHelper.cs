﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

//namespace Game.Core
//{

//    class RoundAddHero
//    {
//        public int StartRound;
//        public int EndRound;
//        public int Num;
//        public int Level;
//    }
//    // 不允许出现变量，只能写静态函数
//    public sealed partial class TablePlayMode
//    {
//        public RoundAddHero GetRoundAddHero(int round)
//        {
//            RoundAddHero roundAddHero = new RoundAddHero();
//            for (int i = 0; i < AddHero.Length; i++)
//            {
//                roundAddHero.StartRound = AddHero[i][0];
//                roundAddHero.EndRound = AddHero[i][1];
//                roundAddHero.Num = AddHero[i][2];
//                roundAddHero.Level = AddHero[i][3];
//            }


//            return roundAddHero;
//        }
//    }
//}
