#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableActivity
	{

		public static readonly string TName="Activity.json";

		#region 属性定义
		/// <summary> 
		/// 活动ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 名称 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 类型 
		/// </summary> 
		public int Type {get; set;}
		/// <summary> 
		/// 类型参数 
		/// </summary> 
		public int[][] TypeValue {get; set;}
		/// <summary> 
		/// 开启条件1 
		/// </summary> 
		public int OpenCondition1 {get; set;}
		/// <summary> 
		/// 条件1参数 
		/// </summary> 
		public int OpenCondition1Value {get; set;}
		/// <summary> 
		/// 开启条件2 
		/// </summary> 
		public int OpenCondition2 {get; set;}
		/// <summary> 
		/// 条件2参数 
		/// </summary> 
		public int OpenCondition2Value {get; set;}
		/// <summary> 
		/// 持续时间/S 
		/// </summary> 
		public int Duration {get; set;}
		/// <summary> 
		/// 间隔时间/S 
		/// </summary> 
		public int Interval {get; set;}
		#endregion

		public static TableActivity GetData(int ID)
		{
			return TableManager.ActivityData.Get(ID);
		}

		public static List<TableActivity> GetAllData()
		{
			return TableManager.ActivityData.GetAll();
		}

	}
	public sealed class TableActivityData
	{
		private Dictionary<int, TableActivity> dict = new Dictionary<int, TableActivity>();
		private List<TableActivity> dataList = new List<TableActivity>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableActivity.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableActivity>>(jsonContent);
			foreach (TableActivity config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableActivity Get(int id)
		{
			if (dict.TryGetValue(id, out TableActivity item))
				return item;
			return null;
		}

		public List<TableActivity> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
