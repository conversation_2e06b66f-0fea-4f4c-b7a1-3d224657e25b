#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableActivityCard
	{

		public static readonly string TName="ActivityCard.json";

		#region 属性定义
		/// <summary> 
		/// 卡ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 名称 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 描述1 
		/// </summary> 
		public int Des1 {get; set;}
		/// <summary> 
		/// 描述2 
		/// </summary> 
		public int Des2 {get; set;}
		/// <summary> 
		/// 时间类型 
		/// </summary> 
		public int TimeType {get; set;}
		/// <summary> 
		/// 持续时间 
		/// </summary> 
		public int Duration {get; set;}
		/// <summary> 
		/// 价格 
		/// </summary> 
		public int Price {get; set;}
		/// <summary> 
		/// 一次性奖励 
		/// </summary> 
		public int[][] DisposableDrop {get; set;}
		/// <summary> 
		/// 每日奖励 
		/// </summary> 
		public int[][] DailyDrop {get; set;}
		/// <summary> 
		/// 多次购买时间是否叠加 
		/// </summary> 
		public int StackType {get; set;}
		/// <summary> 
		/// 每日X次胜利多得Y奖杯 
		/// </summary> 
		public int[] Value1 {get; set;}
		/// <summary> 
		/// 连胜额外X奖杯 
		/// </summary> 
		public int Value2 {get; set;}
		/// <summary> 
		/// 阵容槽位提升 
		/// </summary> 
		public int Value3 {get; set;}
		/// <summary> 
		/// PVP忍术卷轴钻石刷新次数提升 
		/// </summary> 
		public int Value4 {get; set;}
		/// <summary> 
		/// 每日商店折扣 
		/// </summary> 
		public int Value5 {get; set;}
		/// <summary> 
		/// 每日商店增加免费刷新次数 
		/// </summary> 
		public int Value6 {get; set;}
		/// <summary> 
		/// 战斗加速 
		/// </summary> 
		public int Value7 {get; set;}
		/// <summary> 
		/// 免广告 
		/// </summary> 
		public int Value8 {get; set;}
		/// <summary> 
		/// 无尽增加次数 
		/// </summary> 
		public int Value9 {get; set;}
		/// <summary> 
		/// 试炼增加次数 
		/// </summary> 
		public int Value10 {get; set;}
		#endregion

		public static TableActivityCard GetData(int ID)
		{
			return TableManager.ActivityCardData.Get(ID);
		}

		public static List<TableActivityCard> GetAllData()
		{
			return TableManager.ActivityCardData.GetAll();
		}

	}
	public sealed class TableActivityCardData
	{
		private Dictionary<int, TableActivityCard> dict = new Dictionary<int, TableActivityCard>();
		private List<TableActivityCard> dataList = new List<TableActivityCard>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableActivityCard.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableActivityCard>>(jsonContent);
			foreach (TableActivityCard config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableActivityCard Get(int id)
		{
			if (dict.TryGetValue(id, out TableActivityCard item))
				return item;
			return null;
		}

		public List<TableActivityCard> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
