#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableActivityFund
	{

		public static readonly string TName="ActivityFund.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 名称 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 描述 
		/// </summary> 
		public int Des {get; set;}
		/// <summary> 
		/// 组ID 
		/// </summary> 
		public int GroupId {get; set;}
		/// <summary> 
		/// 下一级ID 
		/// </summary> 
		public int NextId {get; set;}
		/// <summary> 
		/// 条件 
		/// </summary> 
		public int Condition {get; set;}
		/// <summary> 
		/// 条件参数 
		/// </summary> 
		public int ConditionValue {get; set;}
		/// <summary> 
		/// 免费奖励 
		/// </summary> 
		public int[][] FreeDrop {get; set;}
		/// <summary> 
		/// 付费奖励1 
		/// </summary> 
		public int[][] PayDrop1 {get; set;}
		/// <summary> 
		/// 付费奖励2 
		/// </summary> 
		public int[][] PayDrop2 {get; set;}
		#endregion

		public static TableActivityFund GetData(int ID)
		{
			return TableManager.ActivityFundData.Get(ID);
		}

		public static List<TableActivityFund> GetAllData()
		{
			return TableManager.ActivityFundData.GetAll();
		}

	}
	public sealed class TableActivityFundData
	{
		private Dictionary<int, TableActivityFund> dict = new Dictionary<int, TableActivityFund>();
		private List<TableActivityFund> dataList = new List<TableActivityFund>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableActivityFund.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableActivityFund>>(jsonContent);
			foreach (TableActivityFund config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableActivityFund Get(int id)
		{
			if (dict.TryGetValue(id, out TableActivityFund item))
				return item;
			return null;
		}

		public List<TableActivityFund> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
