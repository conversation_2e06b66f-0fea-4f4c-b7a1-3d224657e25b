#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableActivityLotteryRewards
	{

		public static readonly string TName="ActivityLotteryRewards.json";

		#region 属性定义
		/// <summary> 
		/// 序列ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 名次 
		/// </summary> 
		public int Rank {get; set;}
		/// <summary> 
		/// 排名奖励 
		/// </summary> 
		public int[] RankRewards {get; set;}
		/// <summary> 
		/// 排名图标 
		/// </summary> 
		public int RankTopType {get; set;}
		/// <summary> 
		/// 活动id 
		/// </summary> 
		public int ActivityId {get; set;}
		#endregion

		public static TableActivityLotteryRewards GetData(int ID)
		{
			return TableManager.ActivityLotteryRewardsData.Get(ID);
		}

		public static List<TableActivityLotteryRewards> GetAllData()
		{
			return TableManager.ActivityLotteryRewardsData.GetAll();
		}

	}
	public sealed class TableActivityLotteryRewardsData
	{
		private Dictionary<int, TableActivityLotteryRewards> dict = new Dictionary<int, TableActivityLotteryRewards>();
		private List<TableActivityLotteryRewards> dataList = new List<TableActivityLotteryRewards>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableActivityLotteryRewards.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableActivityLotteryRewards>>(jsonContent);
			foreach (TableActivityLotteryRewards config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableActivityLotteryRewards Get(int id)
		{
			if (dict.TryGetValue(id, out TableActivityLotteryRewards item))
				return item;
			return null;
		}

		public List<TableActivityLotteryRewards> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
