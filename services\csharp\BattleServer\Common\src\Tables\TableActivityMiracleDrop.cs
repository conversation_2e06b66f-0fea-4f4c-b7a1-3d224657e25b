#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableActivityMiracleDrop
	{

		public static readonly string TName="ActivityMiracleDrop.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 轮次 
		/// </summary> 
		public int Round {get; set;}
		/// <summary> 
		/// 解锁奖励 
		/// </summary> 
		public int[][] Drop1 {get; set;}
		/// <summary> 
		/// 限定奖励类型 
		/// </summary> 
		public int Type {get; set;}
		/// <summary> 
		/// 限定奖励内容 
		/// </summary> 
		public int[][] Drop2 {get; set;}
		#endregion

		public static TableActivityMiracleDrop GetData(int ID)
		{
			return TableManager.ActivityMiracleDropData.Get(ID);
		}

		public static List<TableActivityMiracleDrop> GetAllData()
		{
			return TableManager.ActivityMiracleDropData.GetAll();
		}

	}
	public sealed class TableActivityMiracleDropData
	{
		private Dictionary<int, TableActivityMiracleDrop> dict = new Dictionary<int, TableActivityMiracleDrop>();
		private List<TableActivityMiracleDrop> dataList = new List<TableActivityMiracleDrop>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableActivityMiracleDrop.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableActivityMiracleDrop>>(jsonContent);
			foreach (TableActivityMiracleDrop config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableActivityMiracleDrop Get(int id)
		{
			if (dict.TryGetValue(id, out TableActivityMiracleDrop item))
				return item;
			return null;
		}

		public List<TableActivityMiracleDrop> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
