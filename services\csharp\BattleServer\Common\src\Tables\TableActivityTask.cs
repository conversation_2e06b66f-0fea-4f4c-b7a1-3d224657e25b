#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableActivityTask
	{

		public static readonly string TName="ActivityTask.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 名称 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 描述 
		/// </summary> 
		public int Des {get; set;}
		/// <summary> 
		/// 分组ID 
		/// </summary> 
		public int GroupId {get; set;}
		/// <summary> 
		/// 任务类型 
		/// </summary> 
		public int Type {get; set;}
		/// <summary> 
		/// 任务子类型 
		/// </summary> 
		public int SubType {get; set;}
		/// <summary> 
		/// 重置类型 
		/// </summary> 
		public int ResetType {get; set;}
		/// <summary> 
		/// 天数 
		/// </summary> 
		public int OpenDay {get; set;}
		/// <summary> 
		/// 开启类型 
		/// </summary> 
		public int OpenType {get; set;}
		/// <summary> 
		/// 条件 
		/// </summary> 
		public int Condition {get; set;}
		/// <summary> 
		/// 条件参数 
		/// </summary> 
		public int[] ConditionValue {get; set;}
		/// <summary> 
		/// 任务未开启时是否计数 
		/// </summary> 
		public int CountType {get; set;}
		/// <summary> 
		/// 奖励 
		/// </summary> 
		public int[][] Drop {get; set;}
		#endregion

		public static TableActivityTask GetData(int ID)
		{
			return TableManager.ActivityTaskData.Get(ID);
		}

		public static List<TableActivityTask> GetAllData()
		{
			return TableManager.ActivityTaskData.GetAll();
		}

	}
	public sealed class TableActivityTaskData
	{
		private Dictionary<int, TableActivityTask> dict = new Dictionary<int, TableActivityTask>();
		private List<TableActivityTask> dataList = new List<TableActivityTask>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableActivityTask.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableActivityTask>>(jsonContent);
			foreach (TableActivityTask config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableActivityTask Get(int id)
		{
			if (dict.TryGetValue(id, out TableActivityTask item))
				return item;
			return null;
		}

		public List<TableActivityTask> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
