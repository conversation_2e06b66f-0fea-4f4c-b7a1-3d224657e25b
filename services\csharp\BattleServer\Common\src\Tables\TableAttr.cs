#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableAttr
	{

		public static readonly string TName="Attr.json";

		#region 属性定义
		/// <summary> 
		/// 属性类型 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 属性值类型 
		/// </summary> 
		public int AttrType {get; set;}
		/// <summary> 
		/// 属性名 
		/// </summary> 
		public int AttrName {get; set;}
		/// <summary> 
		/// 图标 
		/// </summary> 
		public string AttrIcon {get; set;}
		/// <summary> 
		/// Pvp参数0 
		/// </summary> 
		public int PvpArgs0 {get; set;}
		/// <summary> 
		/// Pvp参数1 
		/// </summary> 
		public int Pvpargs1 {get; set;}
		#endregion

		public static TableAttr GetData(int ID)
		{
			return TableManager.AttrData.Get(ID);
		}

		public static List<TableAttr> GetAllData()
		{
			return TableManager.AttrData.GetAll();
		}

	}
	public sealed class TableAttrData
	{
		private Dictionary<int, TableAttr> dict = new Dictionary<int, TableAttr>();
		private List<TableAttr> dataList = new List<TableAttr>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableAttr.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableAttr>>(jsonContent);
			foreach (TableAttr config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableAttr Get(int id)
		{
			if (dict.TryGetValue(id, out TableAttr item))
				return item;
			return null;
		}

		public List<TableAttr> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
