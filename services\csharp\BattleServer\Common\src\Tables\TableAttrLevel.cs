#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableAttrLevel
	{

		public static readonly string TName="AttrLevel.json";

		#region 属性定义
		/// <summary> 
		/// 属性类型 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 等级上限 
		/// </summary> 
		public int LevelLimit {get; set;}
		/// <summary> 
		/// 升级金币消耗参数1 
		/// </summary> 
		public string CostParam1 {get; set;}
		/// <summary> 
		/// 属性成长参数1 
		/// </summary> 
		public float AttrParam1 {get; set;}
		/// <summary> 
		/// 属性成长参数2 
		/// </summary> 
		public float AttrParam2 {get; set;}
		/// <summary> 
		/// 属性成长参数3 
		/// </summary> 
		public float AttrParam3 {get; set;}
		/// <summary> 
		/// 解锁条件类型 
		/// </summary> 
		public int UnlockType {get; set;}
		/// <summary> 
		/// 解锁条件数值 
		/// </summary> 
		public int UnlockCondition {get; set;}
		#endregion

		public static TableAttrLevel GetData(int ID)
		{
			return TableManager.AttrLevelData.Get(ID);
		}

		public static List<TableAttrLevel> GetAllData()
		{
			return TableManager.AttrLevelData.GetAll();
		}

	}
	public sealed class TableAttrLevelData
	{
		private Dictionary<int, TableAttrLevel> dict = new Dictionary<int, TableAttrLevel>();
		private List<TableAttrLevel> dataList = new List<TableAttrLevel>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableAttrLevel.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableAttrLevel>>(jsonContent);
			foreach (TableAttrLevel config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableAttrLevel Get(int id)
		{
			if (dict.TryGetValue(id, out TableAttrLevel item))
				return item;
			return null;
		}

		public List<TableAttrLevel> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
