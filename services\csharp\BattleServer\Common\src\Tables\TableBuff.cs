#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableBuff
	{

		public static readonly string TName="Buff.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 战斗BUFFID 
		/// </summary> 
		public int BattleBuffID {get; set;}
		#endregion

		public static TableBuff GetData(int ID)
		{
			return TableManager.BuffData.Get(ID);
		}

		public static List<TableBuff> GetAllData()
		{
			return TableManager.BuffData.GetAll();
		}

	}
	public sealed class TableBuffData
	{
		private Dictionary<int, TableBuff> dict = new Dictionary<int, TableBuff>();
		private List<TableBuff> dataList = new List<TableBuff>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableBuff.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableBuff>>(jsonContent);
			foreach (TableBuff config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableBuff Get(int id)
		{
			if (dict.TryGetValue(id, out TableBuff item))
				return item;
			return null;
		}

		public List<TableBuff> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
