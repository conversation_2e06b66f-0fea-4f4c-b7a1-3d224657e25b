#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableChessHero
	{

		public static readonly string TName="ChessHero.json";

		#region 属性定义
		/// <summary> 
		/// heroId 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 名字 
		/// </summary> 
		public string name {get; set;}
		/// <summary> 
		/// 攻击 
		/// </summary> 
		public int attack {get; set;}
		/// <summary> 
		/// 血量 
		/// </summary> 
		public int maxHp {get; set;}
		/// <summary> 
		/// 攻击速度 
		/// </summary> 
		public int attackSpeed {get; set;}
		/// <summary> 
		/// 射程 
		/// </summary> 
		public int attackDistance {get; set;}
		/// <summary> 
		/// 职业 
		/// </summary> 
		public int job {get; set;}
		/// <summary> 
		/// 预制路径 
		/// </summary> 
		public string prefabPath {get; set;}
		/// <summary> 
		/// 技能 
		/// </summary> 
		public int[] skills {get; set;}
		/// <summary> 
		/// 普通攻击加的MP 
		/// </summary> 
		public int mpAddValue {get; set;}
		#endregion

		public static TableChessHero GetData(int ID)
		{
			return TableManager.ChessHeroData.Get(ID);
		}

		public static List<TableChessHero> GetAllData()
		{
			return TableManager.ChessHeroData.GetAll();
		}

	}
	public sealed class TableChessHeroData
	{
		private Dictionary<int, TableChessHero> dict = new Dictionary<int, TableChessHero>();
		private List<TableChessHero> dataList = new List<TableChessHero>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableChessHero.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableChessHero>>(jsonContent);
			foreach (TableChessHero config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableChessHero Get(int id)
		{
			if (dict.TryGetValue(id, out TableChessHero item))
				return item;
			return null;
		}

		public List<TableChessHero> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
