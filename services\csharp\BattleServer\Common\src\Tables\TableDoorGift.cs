#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableDoorGift
	{

		public static readonly string TName="DoorGift.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 礼包名称 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 礼包图标 
		/// </summary> 
		public string Icon {get; set;}
		/// <summary> 
		/// 礼包描述 
		/// </summary> 
		public int GiftDesc {get; set;}
		/// <summary> 
		/// 礼包类型 0.免费1.付费 
		/// </summary> 
		public int GiftType {get; set;}
		/// <summary> 
		/// 奖励ID 
		/// </summary> 
		public int DropGroupId {get; set;}
		/// <summary> 
		/// 支付表ID 
		/// </summary> 
		public int MidasItemId {get; set;}
		#endregion

		public static TableDoorGift GetData(int ID)
		{
			return TableManager.DoorGiftData.Get(ID);
		}

		public static List<TableDoorGift> GetAllData()
		{
			return TableManager.DoorGiftData.GetAll();
		}

	}
	public sealed class TableDoorGiftData
	{
		private Dictionary<int, TableDoorGift> dict = new Dictionary<int, TableDoorGift>();
		private List<TableDoorGift> dataList = new List<TableDoorGift>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableDoorGift.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableDoorGift>>(jsonContent);
			foreach (TableDoorGift config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableDoorGift Get(int id)
		{
			if (dict.TryGetValue(id, out TableDoorGift item))
				return item;
			return null;
		}

		public List<TableDoorGift> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
