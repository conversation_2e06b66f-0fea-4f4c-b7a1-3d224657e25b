#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableDropGroup
	{

		public static readonly string TName="DropGroup.json";

		#region 属性定义
		/// <summary> 
		/// 掉落包ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 最大掉落数量 
		/// </summary> 
		public int DropNum {get; set;}
		/// <summary> 
		/// 每个掉落是否可重复(0不可 1可以) 
		/// </summary> 
		public int IsCanDuplicate {get; set;}
		/// <summary> 
		/// 掉落道具 
		/// </summary> 
		public int[] DropBoxID {get; set;}
		/// <summary> 
		/// 掉落权重 
		/// </summary> 
		public int[] DropWeight {get; set;}
		#endregion

		public static TableDropGroup GetData(int ID)
		{
			return TableManager.DropGroupData.Get(ID);
		}

		public static List<TableDropGroup> GetAllData()
		{
			return TableManager.DropGroupData.GetAll();
		}

	}
	public sealed class TableDropGroupData
	{
		private Dictionary<int, TableDropGroup> dict = new Dictionary<int, TableDropGroup>();
		private List<TableDropGroup> dataList = new List<TableDropGroup>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableDropGroup.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableDropGroup>>(jsonContent);
			foreach (TableDropGroup config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableDropGroup Get(int id)
		{
			if (dict.TryGetValue(id, out TableDropGroup item))
				return item;
			return null;
		}

		public List<TableDropGroup> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
