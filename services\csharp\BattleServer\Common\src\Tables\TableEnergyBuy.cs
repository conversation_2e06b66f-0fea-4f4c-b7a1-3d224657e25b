#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableEnergyBuy
	{

		public static readonly string TName="EnergyBuy.json";

		#region 属性定义
		/// <summary> 
		/// 体力购买类型 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 消耗物品 
		/// </summary> 
		public int[] Item {get; set;}
		/// <summary> 
		/// 获得体力 
		/// </summary> 
		public int Energy {get; set;}
		/// <summary> 
		/// 每日次数 
		/// </summary> 
		public int Times {get; set;}
		/// <summary> 
		/// 最大储存次数 
		/// </summary> 
		public int StorageTimes {get; set;}
		#endregion

		public static TableEnergyBuy GetData(int ID)
		{
			return TableManager.EnergyBuyData.Get(ID);
		}

		public static List<TableEnergyBuy> GetAllData()
		{
			return TableManager.EnergyBuyData.GetAll();
		}

	}
	public sealed class TableEnergyBuyData
	{
		private Dictionary<int, TableEnergyBuy> dict = new Dictionary<int, TableEnergyBuy>();
		private List<TableEnergyBuy> dataList = new List<TableEnergyBuy>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableEnergyBuy.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableEnergyBuy>>(jsonContent);
			foreach (TableEnergyBuy config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableEnergyBuy Get(int id)
		{
			if (dict.TryGetValue(id, out TableEnergyBuy item))
				return item;
			return null;
		}

		public List<TableEnergyBuy> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
