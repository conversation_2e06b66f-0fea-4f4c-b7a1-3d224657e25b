#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableFashionableDress
	{

		public static readonly string TName="FashionableDress.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 类型1.活动 2.晋升称号 
		/// </summary> 
		public int Type {get; set;}
		/// <summary> 
		/// 品质 
		/// </summary> 
		public int Quality {get; set;}
		/// <summary> 
		/// 指定日期类型（0：开服时间戳 1：指定日期） 
		/// </summary> 
		public int ShowTimeType {get; set;}
		/// <summary> 
		/// 指定日期显示（1、指定固定日期2、开服多久开）分钟数或指定日期 
		/// </summary> 
		public string ShowTime {get; set;}
		/// <summary> 
		/// 名字 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 时装描述 
		/// </summary> 
		public int Des {get; set;}
		/// <summary> 
		/// 获取途径 
		/// </summary> 
		public int GetStr {get; set;}
		/// <summary> 
		/// 模型的名字 
		/// </summary> 
		public int ModelId {get; set;}
		/// <summary> 
		/// 拥有属性id_1 
		/// </summary> 
		public int OwnProIdId_1 {get; set;}
		/// <summary> 
		/// 拥有属性数值_1 
		/// </summary> 
		public int[] OwnProId_1 {get; set;}
		/// <summary> 
		/// 拥有属性id_2 
		/// </summary> 
		public int OwnProIdId_2 {get; set;}
		/// <summary> 
		/// 拥有属性数值_2 
		/// </summary> 
		public int[] OwnProId_2 {get; set;}
		/// <summary> 
		/// 星级的拥有属性ID 
		/// </summary> 
		public int[] StarOwnProIdId {get; set;}
		/// <summary> 
		/// 星级的拥有属性数值_2 
		/// </summary> 
		public int[] StarOwnProId {get; set;}
		/// <summary> 
		/// 穿戴后攻击技能 
		/// </summary> 
		public int[] SkillEx {get; set;}
		/// <summary> 
		/// 穿戴后被动技能 
		/// </summary> 
		public int[][] PassiveSkillEx {get; set;}
		/// <summary> 
		/// 模型普攻是否走默认走到怪前面（0走默认的走到怪的对面，1 按照距离来） 
		/// </summary> 
		public int IsDefaultAiAtk {get; set;}
		/// <summary> 
		/// 道具icon 
		/// </summary> 
		public string Icon {get; set;}
		/// <summary> 
		/// 大图标icon1 
		/// </summary> 
		public string Icon1 {get; set;}
		/// <summary> 
		/// 头像表id 
		/// </summary> 
		public int HeadIconId {get; set;}
		/// <summary> 
		/// 排列顺序 
		/// </summary> 
		public int SortOrder {get; set;}
		/// <summary> 
		/// 碎片合成 
		/// </summary> 
		public int[] ChipCompound {get; set;}
		#endregion

		public static TableFashionableDress GetData(int ID)
		{
			return TableManager.FashionableDressData.Get(ID);
		}

		public static List<TableFashionableDress> GetAllData()
		{
			return TableManager.FashionableDressData.GetAll();
		}

	}
	public sealed class TableFashionableDressData
	{
		private Dictionary<int, TableFashionableDress> dict = new Dictionary<int, TableFashionableDress>();
		private List<TableFashionableDress> dataList = new List<TableFashionableDress>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableFashionableDress.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableFashionableDress>>(jsonContent);
			foreach (TableFashionableDress config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableFashionableDress Get(int id)
		{
			if (dict.TryGetValue(id, out TableFashionableDress item))
				return item;
			return null;
		}

		public List<TableFashionableDress> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
