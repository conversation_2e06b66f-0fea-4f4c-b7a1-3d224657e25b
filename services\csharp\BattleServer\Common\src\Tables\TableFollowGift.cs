#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableFollowGift
	{

		public static readonly string TName="FollowGift.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 奖励Id 
		/// </summary> 
		public int GiftID {get; set;}
		/// <summary> 
		/// 跳转链接 
		/// </summary> 
		public string URL {get; set;}
		#endregion

		public static TableFollowGift GetData(int ID)
		{
			return TableManager.FollowGiftData.Get(ID);
		}

		public static List<TableFollowGift> GetAllData()
		{
			return TableManager.FollowGiftData.GetAll();
		}

	}
	public sealed class TableFollowGiftData
	{
		private Dictionary<int, TableFollowGift> dict = new Dictionary<int, TableFollowGift>();
		private List<TableFollowGift> dataList = new List<TableFollowGift>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableFollowGift.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableFollowGift>>(jsonContent);
			foreach (TableFollowGift config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableFollowGift Get(int id)
		{
			if (dict.TryGetValue(id, out TableFollowGift item))
				return item;
			return null;
		}

		public List<TableFollowGift> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
