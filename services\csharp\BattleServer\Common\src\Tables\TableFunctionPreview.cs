#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableFunctionPreview
	{

		public static readonly string TName="FunctionPreview.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 对应FunctionUnlock表ID 
		/// </summary> 
		public int UnlockId {get; set;}
		/// <summary> 
		/// 掉落表id 
		/// </summary> 
		public int DropGroupId {get; set;}
		#endregion

		public static TableFunctionPreview GetData(int ID)
		{
			return TableManager.FunctionPreviewData.Get(ID);
		}

		public static List<TableFunctionPreview> GetAllData()
		{
			return TableManager.FunctionPreviewData.GetAll();
		}

	}
	public sealed class TableFunctionPreviewData
	{
		private Dictionary<int, TableFunctionPreview> dict = new Dictionary<int, TableFunctionPreview>();
		private List<TableFunctionPreview> dataList = new List<TableFunctionPreview>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableFunctionPreview.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableFunctionPreview>>(jsonContent);
			foreach (TableFunctionPreview config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableFunctionPreview Get(int id)
		{
			if (dict.TryGetValue(id, out TableFunctionPreview item))
				return item;
			return null;
		}

		public List<TableFunctionPreview> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
