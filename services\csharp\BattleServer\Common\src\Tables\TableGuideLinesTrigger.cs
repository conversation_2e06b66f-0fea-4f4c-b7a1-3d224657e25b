#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableGuideLinesTrigger
	{

		public static readonly string TName="GuideLinesTrigger.json";

		#region 属性定义
		/// <summary> 
		/// 引导ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 下一个引导 
		/// </summary> 
		public int NextGroupId {get; set;}
		/// <summary> 
		/// 条件：前置引导条件 
		/// </summary> 
		public int PreGroupId {get; set;}
		/// <summary> 
		/// 特定条件1 
		/// </summary> 
		public int RunCondition1 {get; set;}
		/// <summary> 
		/// 特定条件1参数 
		/// </summary> 
		public string RunParam1 {get; set;}
		/// <summary> 
		/// 特定条件2 
		/// </summary> 
		public int RunCondition2 {get; set;}
		/// <summary> 
		/// 特定条件2参数 
		/// </summary> 
		public string RunParam2 {get; set;}
		/// <summary> 
		/// 特定条件3 
		/// </summary> 
		public int RunCondition3 {get; set;}
		/// <summary> 
		/// 特定条件3参数 
		/// </summary> 
		public string RunParam3 {get; set;}
		/// <summary> 
		/// 触发类型 
		/// </summary> 
		public int StartType {get; set;}
		/// <summary> 
		/// 参数信息 
		/// </summary> 
		public int StartParameter {get; set;}
		/// <summary> 
		/// 强制结束引导条件类型 
		/// </summary> 
		public int ForceEndType {get; set;}
		/// <summary> 
		/// 参数信息（结束条件参数） 
		/// </summary> 
		public int[] ForceEndParams {get; set;}
		/// <summary> 
		/// 引导组首ID，只填写该引导组首Id 
		/// </summary> 
		public int GuildStartId {get; set;}
		/// <summary> 
		/// 是否在主场景触发1.不在2.在3.花路主线关卡触发引导时填写 
		/// </summary> 
		public int IsInMainScene {get; set;}
		/// <summary> 
		/// 触发机制是否需要回退 
		/// </summary> 
		public int IsGoBack {get; set;}
		/// <summary> 
		/// 触发引导时需要保留的界面 
		/// </summary> 
		public string[] StayUI {get; set;}
		#endregion

		public static TableGuideLinesTrigger GetData(int ID)
		{
			return TableManager.GuideLinesTriggerData.Get(ID);
		}

		public static List<TableGuideLinesTrigger> GetAllData()
		{
			return TableManager.GuideLinesTriggerData.GetAll();
		}

	}
	public sealed class TableGuideLinesTriggerData
	{
		private Dictionary<int, TableGuideLinesTrigger> dict = new Dictionary<int, TableGuideLinesTrigger>();
		private List<TableGuideLinesTrigger> dataList = new List<TableGuideLinesTrigger>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableGuideLinesTrigger.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableGuideLinesTrigger>>(jsonContent);
			foreach (TableGuideLinesTrigger config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableGuideLinesTrigger Get(int id)
		{
			if (dict.TryGetValue(id, out TableGuideLinesTrigger item))
				return item;
			return null;
		}

		public List<TableGuideLinesTrigger> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
