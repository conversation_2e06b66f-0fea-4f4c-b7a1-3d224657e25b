#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableGuildActivityBoss
	{

		public static readonly string TName="GuildActivityBoss.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 当前周目第几个boss 
		/// </summary> 
		public int GroupId {get; set;}
		/// <summary> 
		/// 战斗ID 
		/// </summary> 
		public int StageId {get; set;}
		/// <summary> 
		/// Boss名称 
		/// </summary> 
		public int TitleName2 {get; set;}
		/// <summary> 
		/// Boss名称(单独) 
		/// </summary> 
		public int TitleName {get; set;}
		/// <summary> 
		/// Boss描述 
		/// </summary> 
		public int TitleDesc {get; set;}
		/// <summary> 
		/// Boss关卡图 
		/// </summary> 
		public string BossIconBig {get; set;}
		/// <summary> 
		/// Boss关卡小图 
		/// </summary> 
		public string BossIconSmall {get; set;}
		/// <summary> 
		/// 配置战力 
		/// </summary> 
		public string Fight {get; set;}
		/// <summary> 
		/// 推荐战力 
		/// </summary> 
		public string Fight2 {get; set;}
		/// <summary> 
		/// Boss说明 
		/// </summary> 
		public int TitleName3 {get; set;}
		/// <summary> 
		/// 推荐技能 
		/// </summary> 
		public int[] Skill {get; set;}
		/// <summary> 
		/// 血条颜色 
		/// </summary> 
		public string[] HpColor1 {get; set;}
		/// <summary> 
		/// 血条过渡颜色 
		/// </summary> 
		public string[] HpColor2 {get; set;}
		/// <summary> 
		/// 血条数量 
		/// </summary> 
		public int HpNum {get; set;}
		#endregion

		public static TableGuildActivityBoss GetData(int ID)
		{
			return TableManager.GuildActivityBossData.Get(ID);
		}

		public static List<TableGuildActivityBoss> GetAllData()
		{
			return TableManager.GuildActivityBossData.GetAll();
		}

	}
	public sealed class TableGuildActivityBossData
	{
		private Dictionary<int, TableGuildActivityBoss> dict = new Dictionary<int, TableGuildActivityBoss>();
		private List<TableGuildActivityBoss> dataList = new List<TableGuildActivityBoss>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableGuildActivityBoss.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableGuildActivityBoss>>(jsonContent);
			foreach (TableGuildActivityBoss config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableGuildActivityBoss Get(int id)
		{
			if (dict.TryGetValue(id, out TableGuildActivityBoss item))
				return item;
			return null;
		}

		public List<TableGuildActivityBoss> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
