#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableGuildDonate
	{

		public static readonly string TName="GuildDonate.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 捐赠物品itemid 
		/// </summary> 
		public int DonateItemId {get; set;}
		/// <summary> 
		/// 单次捐赠数 
		/// </summary> 
		public int SingleDonate {get; set;}
		/// <summary> 
		/// 个人奖励 
		/// </summary> 
		public int DropGroupID {get; set;}
		/// <summary> 
		/// 公会获得经验 
		/// </summary> 
		public int GuildExp {get; set;}
		/// <summary> 
		/// 每日捐赠上限 
		/// </summary> 
		public int MaxDonateCount {get; set;}
		#endregion

		public static TableGuildDonate GetData(int ID)
		{
			return TableManager.GuildDonateData.Get(ID);
		}

		public static List<TableGuildDonate> GetAllData()
		{
			return TableManager.GuildDonateData.GetAll();
		}

	}
	public sealed class TableGuildDonateData
	{
		private Dictionary<int, TableGuildDonate> dict = new Dictionary<int, TableGuildDonate>();
		private List<TableGuildDonate> dataList = new List<TableGuildDonate>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableGuildDonate.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableGuildDonate>>(jsonContent);
			foreach (TableGuildDonate config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableGuildDonate Get(int id)
		{
			if (dict.TryGetValue(id, out TableGuildDonate item))
				return item;
			return null;
		}

		public List<TableGuildDonate> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
