#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableHeadIcon
	{

		public static readonly string TName="HeadIcon.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 品质 
		/// </summary> 
		public int Quality {get; set;}
		/// <summary> 
		/// 名字 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 头像描述 
		/// </summary> 
		public int Des {get; set;}
		/// <summary> 
		/// 获取途径 
		/// </summary> 
		public int GetStr {get; set;}
		/// <summary> 
		/// 指定日期类型（0：开服时间戳 1：指定日期） 
		/// </summary> 
		public int ShowTimeType {get; set;}
		/// <summary> 
		/// 指定日期显示（1、指定固定日期2、开服多久开）分钟数或指定日期 
		/// </summary> 
		public string ShowTime {get; set;}
		/// <summary> 
		/// 掉落表id 
		/// </summary> 
		public int DropGroupId {get; set;}
		/// <summary> 
		/// 道具圆icon 
		/// </summary> 
		public string RoundIcon {get; set;}
		/// <summary> 
		/// 道具方icon(用于道具掉落展示) 
		/// </summary> 
		public string Icon {get; set;}
		/// <summary> 
		/// 类型（0、普通 1、特殊） 
		/// </summary> 
		public int Type {get; set;}
		/// <summary> 
		/// 显示顺序 
		/// </summary> 
		public int SortOrder {get; set;}
		#endregion

		public static TableHeadIcon GetData(int ID)
		{
			return TableManager.HeadIconData.Get(ID);
		}

		public static List<TableHeadIcon> GetAllData()
		{
			return TableManager.HeadIconData.GetAll();
		}

	}
	public sealed class TableHeadIconData
	{
		private Dictionary<int, TableHeadIcon> dict = new Dictionary<int, TableHeadIcon>();
		private List<TableHeadIcon> dataList = new List<TableHeadIcon>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableHeadIcon.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableHeadIcon>>(jsonContent);
			foreach (TableHeadIcon config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableHeadIcon Get(int id)
		{
			if (dict.TryGetValue(id, out TableHeadIcon item))
				return item;
			return null;
		}

		public List<TableHeadIcon> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
