#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableHeavenlyDao
	{

		public static readonly string TName="HeavenlyDao.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 名称(Language表ID) 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 任务ID(Mission表ID) 
		/// </summary> 
		public int[] TaskID {get; set;}
		/// <summary> 
		/// 属性(每个任务完成后提供一个属性加成,在晋升的时候获得) 
		/// </summary> 
		public string Attribute {get; set;}
		#endregion

		public static TableHeavenlyDao GetData(int ID)
		{
			return TableManager.HeavenlyDaoData.Get(ID);
		}

		public static List<TableHeavenlyDao> GetAllData()
		{
			return TableManager.HeavenlyDaoData.GetAll();
		}

	}
	public sealed class TableHeavenlyDaoData
	{
		private Dictionary<int, TableHeavenlyDao> dict = new Dictionary<int, TableHeavenlyDao>();
		private List<TableHeavenlyDao> dataList = new List<TableHeavenlyDao>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableHeavenlyDao.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableHeavenlyDao>>(jsonContent);
			foreach (TableHeavenlyDao config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableHeavenlyDao Get(int id)
		{
			if (dict.TryGetValue(id, out TableHeavenlyDao item))
				return item;
			return null;
		}

		public List<TableHeavenlyDao> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
