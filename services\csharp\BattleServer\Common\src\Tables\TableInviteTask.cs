#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableInviteTask
	{

		public static readonly string TName="InviteTask.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 平台id 
		/// </summary> 
		public int PlatformId {get; set;}
		/// <summary> 
		/// 完成任务所需次数 
		/// </summary> 
		public int Cnt {get; set;}
		/// <summary> 
		/// 掉落表id 
		/// </summary> 
		public int DropGroupId {get; set;}
		#endregion

		public static TableInviteTask GetData(int ID)
		{
			return TableManager.InviteTaskData.Get(ID);
		}

		public static List<TableInviteTask> GetAllData()
		{
			return TableManager.InviteTaskData.GetAll();
		}

	}
	public sealed class TableInviteTaskData
	{
		private Dictionary<int, TableInviteTask> dict = new Dictionary<int, TableInviteTask>();
		private List<TableInviteTask> dataList = new List<TableInviteTask>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableInviteTask.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableInviteTask>>(jsonContent);
			foreach (TableInviteTask config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableInviteTask Get(int id)
		{
			if (dict.TryGetValue(id, out TableInviteTask item))
				return item;
			return null;
		}

		public List<TableInviteTask> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
