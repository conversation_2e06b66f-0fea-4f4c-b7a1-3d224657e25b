#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableItem
	{

		public static readonly string TName="Item.json";

		#region 属性定义
		/// <summary> 
		/// 道具ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 道具名称(对应language表) 
		/// </summary> 
		public int NameID {get; set;}
		/// <summary> 
		/// 道具描述 
		/// </summary> 
		public int Desc {get; set;}
		/// <summary> 
		/// 道具Icon 
		/// </summary> 
		public string Icon {get; set;}
		/// <summary> 
		/// 道具类型 
		/// </summary> 
		public int Type {get; set;}
		/// <summary> 
		/// 子类型 
		/// </summary> 
		public int SubType {get; set;}
		/// <summary> 
		/// 道具品质 
		/// </summary> 
		public int Qulity {get; set;}
		/// <summary> 
		/// 优先级 
		/// </summary> 
		public int SortOrder {get; set;}
		/// <summary> 
		/// 道具售价 
		/// </summary> 
		public int[] Price {get; set;}
		/// <summary> 
		/// 是否在背包展示 
		/// </summary> 
		public int ShowPacket {get; set;}
		/// <summary> 
		/// 等级限制 
		/// </summary> 
		public int LevelLimit {get; set;}
		/// <summary> 
		/// 道具操作列表 
		/// </summary> 
		public int[] OptList {get; set;}
		/// <summary> 
		/// 批量使用上限 
		/// </summary> 
		public int BatchUseMaxCount {get; set;}
		/// <summary> 
		/// 获取途径 
		/// </summary> 
		public int[] FromFunctionList {get; set;}
		/// <summary> 
		/// 跳转使用 
		/// </summary> 
		public int jumpID {get; set;}
		/// <summary> 
		/// 道具参数A 
		/// </summary> 
		public int[] NumParams {get; set;}
		/// <summary> 
		/// 道具参数B 
		/// </summary> 
		public string[] StrParams {get; set;}
		/// <summary> 
		/// 是否使用：用于GM后台邮件(0:未使用1:使用) 
		/// </summary> 
		public int IsUse {get; set;}
		/// <summary> 
		/// 最大可叠加数量 
		/// </summary> 
		public int Stacking {get; set;}
		/// <summary> 
		/// 来源(0：商城1：等级基金2：七日签到3：充值返利) 
		/// </summary> 
		public int Source {get; set;}
		#endregion

		public static TableItem GetData(int ID)
		{
			return TableManager.ItemData.Get(ID);
		}

		public static List<TableItem> GetAllData()
		{
			return TableManager.ItemData.GetAll();
		}

	}
	public sealed class TableItemData
	{
		private Dictionary<int, TableItem> dict = new Dictionary<int, TableItem>();
		private List<TableItem> dataList = new List<TableItem>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableItem.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableItem>>(jsonContent);
			foreach (TableItem config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableItem Get(int id)
		{
			if (dict.TryGetValue(id, out TableItem item))
				return item;
			return null;
		}

		public List<TableItem> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
