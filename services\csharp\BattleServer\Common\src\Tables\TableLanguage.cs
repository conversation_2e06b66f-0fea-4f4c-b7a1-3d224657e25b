#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableLanguage
	{

		public static readonly string TName="Language.json";

		#region 属性定义
		/// <summary> 
		/// 字典id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 字典变量（全部大写并且单词之间用下划线连接） 
		/// </summary> 
		public string valueName {get; set;}
		/// <summary> 
		/// 字典内容中文 
		/// </summary> 
		public string zhCN {get; set;}
		/// <summary> 
		/// 字典内容（英文） 
		/// </summary> 
		public string enUS {get; set;}
		#endregion

		public static TableLanguage GetData(int ID)
		{
			return TableManager.LanguageData.Get(ID);
		}

		public static List<TableLanguage> GetAllData()
		{
			return TableManager.LanguageData.GetAll();
		}

	}
	public sealed class TableLanguageData
	{
		private Dictionary<int, TableLanguage> dict = new Dictionary<int, TableLanguage>();
		private List<TableLanguage> dataList = new List<TableLanguage>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableLanguage.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableLanguage>>(jsonContent);
			foreach (TableLanguage config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableLanguage Get(int id)
		{
			if (dict.TryGetValue(id, out TableLanguage item))
				return item;
			return null;
		}

		public List<TableLanguage> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
