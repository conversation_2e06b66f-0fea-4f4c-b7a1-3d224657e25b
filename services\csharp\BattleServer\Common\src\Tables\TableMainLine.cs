#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableMainLine
	{

		public static readonly string TName="MainLine.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 章节 
		/// </summary> 
		public int ChapterID {get; set;}
		/// <summary> 
		/// 关卡难度（非富文本） 
		/// </summary> 
		public int StagesNamePre2 {get; set;}
		/// <summary> 
		/// 关卡难度（富文本） 
		/// </summary> 
		public int StagesNamePre {get; set;}
		/// <summary> 
		/// 关卡号 
		/// </summary> 
		public string StagesName {get; set;}
		/// <summary> 
		/// 关卡名称 
		/// </summary> 
		public int StagesNamePro {get; set;}
		/// <summary> 
		/// 上一关 
		/// </summary> 
		public int LastId {get; set;}
		/// <summary> 
		/// 下一关 
		/// </summary> 
		public int NextId {get; set;}
		/// <summary> 
		/// 关联战斗 
		/// </summary> 
		public int StageID {get; set;}
		/// <summary> 
		/// 体力 
		/// </summary> 
		public int Stamina {get; set;}
		/// <summary> 
		/// 展示奖励 
		/// </summary> 
		public int Reward {get; set;}
		/// <summary> 
		/// 阶段 
		/// </summary> 
		public int[] StageHealth {get; set;}
		/// <summary> 
		/// 阶段奖励 
		/// </summary> 
		public int[] RewardGroup {get; set;}
		/// <summary> 
		/// 进场动画 1 播 0 不播 
		/// </summary> 
		public int Run {get; set;}
		/// <summary> 
		/// 每分钟挂机金币（单位分钟） 
		/// </summary> 
		public int PlacementGold {get; set;}
		/// <summary> 
		/// 多少秒产一个图纸 
		/// </summary> 
		public int PlacementExp {get; set;}
		/// <summary> 
		/// 挂机产出道具时间（单位秒） 
		/// </summary> 
		public int[] PlacementTime {get; set;}
		/// <summary> 
		/// 挂机掉落奖励 
		/// </summary> 
		public int[] DropGroup {get; set;}
		/// <summary> 
		/// 走计算公式类奖励 
		/// </summary> 
		public int[] DropGroup1 {get; set;}
		/// <summary> 
		/// 走正常掉落组奖励 
		/// </summary> 
		public int[] DropGroup2 {get; set;}
		/// <summary> 
		/// 剧情关卡的剧情id 
		/// </summary> 
		public int StoryStageId {get; set;}
		/// <summary> 
		/// 战前剧情 
		/// </summary> 
		public int PreWarStorylD {get; set;}
		/// <summary> 
		/// 战后剧情 
		/// </summary> 
		public int AfterWarStorylD {get; set;}
		/// <summary> 
		/// 战中剧情 
		/// </summary> 
		public int MidWarStorylD {get; set;}
		/// <summary> 
		/// 关卡展示图片 
		/// </summary> 
		public string Picture {get; set;}
		/// <summary> 
		/// 试用英雄 
		/// </summary> 
		public int SpecifySkin {get; set;}
		#endregion

		public static TableMainLine GetData(int ID)
		{
			return TableManager.MainLineData.Get(ID);
		}

		public static List<TableMainLine> GetAllData()
		{
			return TableManager.MainLineData.GetAll();
		}

	}
	public sealed class TableMainLineData
	{
		private Dictionary<int, TableMainLine> dict = new Dictionary<int, TableMainLine>();
		private List<TableMainLine> dataList = new List<TableMainLine>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableMainLine.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableMainLine>>(jsonContent);
			foreach (TableMainLine config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableMainLine Get(int id)
		{
			if (dict.TryGetValue(id, out TableMainLine item))
				return item;
			return null;
		}

		public List<TableMainLine> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
