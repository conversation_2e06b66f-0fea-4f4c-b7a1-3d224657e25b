#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableMainRankSeason
	{

		public static readonly string TName="MainRankSeason.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 排行区间 
		/// </summary> 
		public int[] Ranking {get; set;}
		/// <summary> 
		/// 奖励内容 
		/// </summary> 
		public int[][] Reward {get; set;}
		#endregion

		public static TableMainRankSeason GetData(int ID)
		{
			return TableManager.MainRankSeasonData.Get(ID);
		}

		public static List<TableMainRankSeason> GetAllData()
		{
			return TableManager.MainRankSeasonData.GetAll();
		}

	}
	public sealed class TableMainRankSeasonData
	{
		private Dictionary<int, TableMainRankSeason> dict = new Dictionary<int, TableMainRankSeason>();
		private List<TableMainRankSeason> dataList = new List<TableMainRankSeason>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableMainRankSeason.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableMainRankSeason>>(jsonContent);
			foreach (TableMainRankSeason config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableMainRankSeason Get(int id)
		{
			if (dict.TryGetValue(id, out TableMainRankSeason item))
				return item;
			return null;
		}

		public List<TableMainRankSeason> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
