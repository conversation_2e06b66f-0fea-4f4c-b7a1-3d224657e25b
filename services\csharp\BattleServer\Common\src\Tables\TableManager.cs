// Code generated by tb_gen. DO NOT EDIT.

using System;
using System.Collections.Generic;
using LiteFrame.Framework;

namespace Game.Core
{
	public sealed class TableManager : Singleton<TableManager>
	{
		public static TableActivityData ActivityData = new TableActivityData();
		public static TableActivityBossDamageData ActivityBossDamageData = new TableActivityBossDamageData();
		public static TableActivityCardData ActivityCardData = new TableActivityCardData();
		public static TableActivityChallengeData ActivityChallengeData = new TableActivityChallengeData();
		public static TableActivityDoorGiftData ActivityDoorGiftData = new TableActivityDoorGiftData();
		public static TableActivityExchangeShopData ActivityExchangeShopData = new TableActivityExchangeShopData();
		public static TableActivityFundData ActivityFundData = new TableActivityFundData();
		public static TableActivityGameData ActivityGameData = new TableActivityGameData();
		public static TableActivityGiftData ActivityGiftData = new TableActivityGiftData();
		public static TableActivityHeroRoadData ActivityHeroRoadData = new TableActivityHeroRoadData();
		public static TableActivityLimitGiftData ActivityLimitGiftData = new TableActivityLimitGiftData();
		public static TableActivityLotteryRewardsData ActivityLotteryRewardsData = new TableActivityLotteryRewardsData();
		public static TableActivityMiracleDropData ActivityMiracleDropData = new TableActivityMiracleDropData();
		public static TableActivitySevenDayTaskData ActivitySevenDayTaskData = new TableActivitySevenDayTaskData();
		public static TableActivityTaskData ActivityTaskData = new TableActivityTaskData();
		public static TableActivityTimeConfigData ActivityTimeConfigData = new TableActivityTimeConfigData();
		public static TableActivityTurntableData ActivityTurntableData = new TableActivityTurntableData();
		public static TableAttrData AttrData = new TableAttrData();
		public static TableAttrLevelData AttrLevelData = new TableAttrLevelData();
		public static TableAttrRangeData AttrRangeData = new TableAttrRangeData();
		public static TableBuffData BuffData = new TableBuffData();
		public static TableChessHeroData ChessHeroData = new TableChessHeroData();
		public static TableCommonBoxRewardData CommonBoxRewardData = new TableCommonBoxRewardData();
		public static TableDoorGiftData DoorGiftData = new TableDoorGiftData();
		public static TableDropBoxData DropBoxData = new TableDropBoxData();
		public static TableDropGroupData DropGroupData = new TableDropGroupData();
		public static TableEnergyBuyData EnergyBuyData = new TableEnergyBuyData();
		public static TableFCMTemplateData FCMTemplateData = new TableFCMTemplateData();
		public static TableFashionableDressData FashionableDressData = new TableFashionableDressData();
		public static TableFirstChargeData FirstChargeData = new TableFirstChargeData();
		public static TableFollowGiftData FollowGiftData = new TableFollowGiftData();
		public static TableFunctionPreviewData FunctionPreviewData = new TableFunctionPreviewData();
		public static TableFunctionUnlockData FunctionUnlockData = new TableFunctionUnlockData();
		public static TableGachaBonusData GachaBonusData = new TableGachaBonusData();
		public static TableGameConfigData GameConfigData = new TableGameConfigData();
		public static TableGiftCodeData GiftCodeData = new TableGiftCodeData();
		public static TableGradedFundData GradedFundData = new TableGradedFundData();
		public static TableGradedFundInfoData GradedFundInfoData = new TableGradedFundInfoData();
		public static TableGuideLinesInfoData GuideLinesInfoData = new TableGuideLinesInfoData();
		public static TableGuideLinesTriggerData GuideLinesTriggerData = new TableGuideLinesTriggerData();
		public static TableGuildActivityBossData GuildActivityBossData = new TableGuildActivityBossData();
		public static TableGuildConfigData GuildConfigData = new TableGuildConfigData();
		public static TableGuildDonateData GuildDonateData = new TableGuildDonateData();
		public static TableGuildLevelData GuildLevelData = new TableGuildLevelData();
		public static TableGuildShopData GuildShopData = new TableGuildShopData();
		public static TableGuildShopListData GuildShopListData = new TableGuildShopListData();
		public static TableGuildTechData GuildTechData = new TableGuildTechData();
		public static TableHeadFrameData HeadFrameData = new TableHeadFrameData();
		public static TableHeadIconData HeadIconData = new TableHeadIconData();
		public static TableHeavenlyDaoData HeavenlyDaoData = new TableHeavenlyDaoData();
		public static TableHeroData HeroData = new TableHeroData();
		public static TableHeroEvoData HeroEvoData = new TableHeroEvoData();
		public static TableHeroLevelData HeroLevelData = new TableHeroLevelData();
		public static TableHookData HookData = new TableHookData();
		public static TableInviteTaskData InviteTaskData = new TableInviteTaskData();
		public static TableIronSourceADConfigData IronSourceADConfigData = new TableIronSourceADConfigData();
		public static TableItemData ItemData = new TableItemData();
		public static TableLanguageData LanguageData = new TableLanguageData();
		public static TableMailTemplateConfigData MailTemplateConfigData = new TableMailTemplateConfigData();
		public static TableMainLineData MainLineData = new TableMainLineData();
		public static TableMainRankData MainRankData = new TableMainRankData();
		public static TableMainRankSeasonData MainRankSeasonData = new TableMainRankSeasonData();
		public static TableMidasDiamondData MidasDiamondData = new TableMidasDiamondData();
		public static TableMidasItemData MidasItemData = new TableMidasItemData();
		public static TableMissionData MissionData = new TableMissionData();
		public static TableModelData ModelData = new TableModelData();
		public static TableMonsterBaseAttrData MonsterBaseAttrData = new TableMonsterBaseAttrData();
		public static TableMonthlyCardData MonthlyCardData = new TableMonthlyCardData();
		public static TableMonthlyCardNewData MonthlyCardNewData = new TableMonthlyCardNewData();
		public static TableMonthlyCardNewExtraRewardData MonthlyCardNewExtraRewardData = new TableMonthlyCardNewExtraRewardData();
		public static TableNewGuideLinesInfoData NewGuideLinesInfoData = new TableNewGuideLinesInfoData();
		public static TableNewGuideLinesTriggerData NewGuideLinesTriggerData = new TableNewGuideLinesTriggerData();
		public static TablePaymentData PaymentData = new TablePaymentData();
		public static TablePlayModeData PlayModeData = new TablePlayModeData();
		public static TablePlayerBaseAttrData PlayerBaseAttrData = new TablePlayerBaseAttrData();
		public static TablePlayerLevelData PlayerLevelData = new TablePlayerLevelData();
		public static TableQuestionnaireData QuestionnaireData = new TableQuestionnaireData();
		public static TableRandomNameData RandomNameData = new TableRandomNameData();
		public static TableRankData RankData = new TableRankData();
		public static TableRankBoardData RankBoardData = new TableRankBoardData();
		public static TableSchedulerConfigData SchedulerConfigData = new TableSchedulerConfigData();
		public static TableServerSkillData ServerSkillData = new TableServerSkillData();
		public static TableSevenSignInData SevenSignInData = new TableSevenSignInData();
		public static TableShopGiftPackData ShopGiftPackData = new TableShopGiftPackData();
		public static TableSignBonusData SignBonusData = new TableSignBonusData();
		public static TableSignInData SignInData = new TableSignInData();
		public static TableSynergyData SynergyData = new TableSynergyData();
		public static TableTeamHoleData TeamHoleData = new TableTeamHoleData();
		public static TableTimeGiftPacksData TimeGiftPacksData = new TableTimeGiftPacksData();
		public static TableTopupRebateData TopupRebateData = new TableTopupRebateData();
		public static TableTotalRechargeData TotalRechargeData = new TableTotalRechargeData();
		public static TableTowerRewardData TowerRewardData = new TableTowerRewardData();
		public static TableTreasureData TreasureData = new TableTreasureData();
		public static TableTreasureGachaData TreasureGachaData = new TableTreasureGachaData();
		public static TableTreasureGachaProData TreasureGachaProData = new TableTreasureGachaProData();
		public static TableTreasureLvData TreasureLvData = new TableTreasureLvData();
		public static TableTreasureStarData TreasureStarData = new TableTreasureStarData();
		public static TableaccruedRewardsData accruedRewardsData = new TableaccruedRewardsData();
		// Load 加载所有表格
		public void InitAll()
		{
			ActivityData.Deserialize();
			ActivityBossDamageData.Deserialize();
			ActivityCardData.Deserialize();
			ActivityChallengeData.Deserialize();
			ActivityDoorGiftData.Deserialize();
			ActivityExchangeShopData.Deserialize();
			ActivityFundData.Deserialize();
			ActivityGameData.Deserialize();
			ActivityGiftData.Deserialize();
			ActivityHeroRoadData.Deserialize();
			ActivityLimitGiftData.Deserialize();
			ActivityLotteryRewardsData.Deserialize();
			ActivityMiracleDropData.Deserialize();
			ActivitySevenDayTaskData.Deserialize();
			ActivityTaskData.Deserialize();
			ActivityTimeConfigData.Deserialize();
			ActivityTurntableData.Deserialize();
			AttrData.Deserialize();
			AttrLevelData.Deserialize();
			AttrRangeData.Deserialize();
			BuffData.Deserialize();
			ChessHeroData.Deserialize();
			CommonBoxRewardData.Deserialize();
			DoorGiftData.Deserialize();
			DropBoxData.Deserialize();
			DropGroupData.Deserialize();
			EnergyBuyData.Deserialize();
			FCMTemplateData.Deserialize();
			FashionableDressData.Deserialize();
			FirstChargeData.Deserialize();
			FollowGiftData.Deserialize();
			FunctionPreviewData.Deserialize();
			FunctionUnlockData.Deserialize();
			GachaBonusData.Deserialize();
			GameConfigData.Deserialize();
			GiftCodeData.Deserialize();
			GradedFundData.Deserialize();
			GradedFundInfoData.Deserialize();
			GuideLinesInfoData.Deserialize();
			GuideLinesTriggerData.Deserialize();
			GuildActivityBossData.Deserialize();
			GuildConfigData.Deserialize();
			GuildDonateData.Deserialize();
			GuildLevelData.Deserialize();
			GuildShopData.Deserialize();
			GuildShopListData.Deserialize();
			GuildTechData.Deserialize();
			HeadFrameData.Deserialize();
			HeadIconData.Deserialize();
			HeavenlyDaoData.Deserialize();
			HeroData.Deserialize();
			HeroEvoData.Deserialize();
			HeroLevelData.Deserialize();
			HookData.Deserialize();
			InviteTaskData.Deserialize();
			IronSourceADConfigData.Deserialize();
			ItemData.Deserialize();
			LanguageData.Deserialize();
			MailTemplateConfigData.Deserialize();
			MainLineData.Deserialize();
			MainRankData.Deserialize();
			MainRankSeasonData.Deserialize();
			MidasDiamondData.Deserialize();
			MidasItemData.Deserialize();
			MissionData.Deserialize();
			ModelData.Deserialize();
			MonsterBaseAttrData.Deserialize();
			MonthlyCardData.Deserialize();
			MonthlyCardNewData.Deserialize();
			MonthlyCardNewExtraRewardData.Deserialize();
			NewGuideLinesInfoData.Deserialize();
			NewGuideLinesTriggerData.Deserialize();
			PaymentData.Deserialize();
			PlayModeData.Deserialize();
			PlayerBaseAttrData.Deserialize();
			PlayerLevelData.Deserialize();
			QuestionnaireData.Deserialize();
			RandomNameData.Deserialize();
			RankData.Deserialize();
			RankBoardData.Deserialize();
			SchedulerConfigData.Deserialize();
			ServerSkillData.Deserialize();
			SevenSignInData.Deserialize();
			ShopGiftPackData.Deserialize();
			SignBonusData.Deserialize();
			SignInData.Deserialize();
			SynergyData.Deserialize();
			TeamHoleData.Deserialize();
			TimeGiftPacksData.Deserialize();
			TopupRebateData.Deserialize();
			TotalRechargeData.Deserialize();
			TowerRewardData.Deserialize();
			TreasureData.Deserialize();
			TreasureGachaData.Deserialize();
			TreasureGachaProData.Deserialize();
			TreasureLvData.Deserialize();
			TreasureStarData.Deserialize();
			accruedRewardsData.Deserialize();
		}
	}
}

