#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableMidasItem
	{

		public static readonly string TName="MidasItem.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 多次购买人民币价格 
		/// </summary> 
		public int RmbPay {get; set;}
		/// <summary> 
		/// 多次购买人民币价格 
		/// </summary> 
		public float RmbPayShow {get; set;}
		/// <summary> 
		/// 消耗多少现金券 
		/// </summary> 
		public int CashCouponPay {get; set;}
		/// <summary> 
		/// 渠道商品ID 
		/// </summary> 
		public string CID {get; set;}
		/// <summary> 
		/// IOS商品ID 
		/// </summary> 
		public string IOSCID {get; set;}
		/// <summary> 
		/// IOS商品ID 
		/// </summary> 
		public string IOSStoreID {get; set;}
		/// <summary> 
		/// Android商品ID 
		/// </summary> 
		public string AndroidStoreID {get; set;}
		/// <summary> 
		/// Midas后台的礼包Id 
		/// </summary> 
		public int PackageId {get; set;}
		/// <summary> 
		/// 各种礼包相关表ID(一一对应，用businessType区分哪张表） 
		/// </summary> 
		public int GiftPacksID {get; set;}
		/// <summary> 
		/// 业务类型（1战令 2、giftpack 5限时充值礼包 6敲门砖礼包） 
		/// </summary> 
		public int businessType {get; set;}
		#endregion

		public static TableMidasItem GetData(int ID)
		{
			return TableManager.MidasItemData.Get(ID);
		}

		public static List<TableMidasItem> GetAllData()
		{
			return TableManager.MidasItemData.GetAll();
		}

	}
	public sealed class TableMidasItemData
	{
		private Dictionary<int, TableMidasItem> dict = new Dictionary<int, TableMidasItem>();
		private List<TableMidasItem> dataList = new List<TableMidasItem>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableMidasItem.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableMidasItem>>(jsonContent);
			foreach (TableMidasItem config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableMidasItem Get(int id)
		{
			if (dict.TryGetValue(id, out TableMidasItem item))
				return item;
			return null;
		}

		public List<TableMidasItem> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
