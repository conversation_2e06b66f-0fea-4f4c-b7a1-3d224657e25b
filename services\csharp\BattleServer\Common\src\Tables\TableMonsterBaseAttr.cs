#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableMonsterBaseAttr
	{

		public static readonly string TName="MonsterBaseAttr.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// Model表ID 
		/// </summary> 
		public int ModelID {get; set;}
		/// <summary> 
		/// 拥有的技能id 
		/// </summary> 
		public int OwnSkillConfigId {get; set;}
		/// <summary> 
		/// 描述 
		/// </summary> 
		public int Describe {get; set;}
		/// <summary> 
		/// 头像 
		/// </summary> 
		public string headIcon {get; set;}
		/// <summary> 
		/// 战斗中头像（小） 
		/// </summary> 
		public string headIconLittle {get; set;}
		/// <summary> 
		/// 怪的名字 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 职业（1、物理    2、冰系  3、电系  4、火系  5、能量   6、风系） 
		/// </summary> 
		public int OccupationType {get; set;}
		/// <summary> 
		/// 攻击距离类型（1近战   2远程） 
		/// </summary> 
		public int AtkDistanceType {get; set;}
		/// <summary> 
		/// 空中类型（ 1、地面 2、空中） 
		/// </summary> 
		public int MidairType {get; set;}
		/// <summary> 
		/// 怪物类型（1、小怪 2、精英、3 boss）  
		/// </summary> 
		public int MonsterType {get; set;}
		/// <summary> 
		/// 攻击 
		/// </summary> 
		public int Attack {get; set;}
		/// <summary> 
		/// 生命 
		/// </summary> 
		public int Hp {get; set;}
		/// <summary> 
		/// 防御 
		/// </summary> 
		public int Def {get; set;}
		/// <summary> 
		/// 命中（万分比） 
		/// </summary> 
		public int HitPre {get; set;}
		/// <summary> 
		/// 闪避（万分比） 
		/// </summary> 
		public int DodgePre {get; set;}
		/// <summary> 
		/// 移速 
		/// </summary> 
		public int MoveSpeed {get; set;}
		/// <summary> 
		/// 基础抗性 
		/// </summary> 
		public int BaseOccupationDef {get; set;}
		/// <summary> 
		/// 抗性类型数组 
		/// </summary> 
		public int[] OccupationDefArr {get; set;}
		/// <summary> 
		/// 抗性的值 
		/// </summary> 
		public int OccupationDefValue {get; set;}
		/// <summary> 
		/// 穿透值 
		/// </summary> 
		public int Penetrate {get; set;}
		/// <summary> 
		/// 穿透率 
		/// </summary> 
		public int PenetratePre {get; set;}
		/// <summary> 
		/// 暴击抵抗 
		/// </summary> 
		public int CriticalResistPre {get; set;}
		/// <summary> 
		/// 警戒范围 
		/// </summary> 
		public float WarnRange {get; set;}
		/// <summary> 
		/// 受击效果（0无任何反应，1、反击，2、逃跑） 
		/// </summary> 
		public int HurtEffectType {get; set;}
		/// <summary> 
		/// 受击触发的概率（1-100） 
		/// </summary> 
		public int HurtEffectPro {get; set;}
		/// <summary> 
		/// 逃跑的速度加成百分比（1 + 当前值 / 10000） * 基础速度 
		/// </summary> 
		public int AddSpeedPre {get; set;}
		/// <summary> 
		/// 是否霸体（0非霸体，1霸体） 
		/// </summary> 
		public int IsSuperArmor {get; set;}
		/// <summary> 
		/// 是否是公会boss 
		/// </summary> 
		public int IsGuildBoss {get; set;}
		/// <summary> 
		/// 怪的拥有经验 
		/// </summary> 
		public int HaveExp {get; set;}
		/// <summary> 
		/// 战斗币 
		/// </summary> 
		public int HaveBattleMoney {get; set;}
		/// <summary> 
		/// 怪的能量值 
		/// </summary> 
		public int HaveBattleEnergy {get; set;}
		#endregion

		public static TableMonsterBaseAttr GetData(int ID)
		{
			return TableManager.MonsterBaseAttrData.Get(ID);
		}

		public static List<TableMonsterBaseAttr> GetAllData()
		{
			return TableManager.MonsterBaseAttrData.GetAll();
		}

	}
	public sealed class TableMonsterBaseAttrData
	{
		private Dictionary<int, TableMonsterBaseAttr> dict = new Dictionary<int, TableMonsterBaseAttr>();
		private List<TableMonsterBaseAttr> dataList = new List<TableMonsterBaseAttr>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableMonsterBaseAttr.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableMonsterBaseAttr>>(jsonContent);
			foreach (TableMonsterBaseAttr config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableMonsterBaseAttr Get(int id)
		{
			if (dict.TryGetValue(id, out TableMonsterBaseAttr item))
				return item;
			return null;
		}

		public List<TableMonsterBaseAttr> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
