#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableMonthlyCard
	{

		public static readonly string TName="MonthlyCard.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 立即掉落表id 
		/// </summary> 
		public int DropGroupId1 {get; set;}
		/// <summary> 
		/// 每日掉落表id 
		/// </summary> 
		public int DropGroupId2 {get; set;}
		/// <summary> 
		/// 掉落表id 
		/// </summary> 
		public int DropGroupId3 {get; set;}
		/// <summary> 
		/// 原价 
		/// </summary> 
		public int PrePrice {get; set;}
		/// <summary> 
		/// 现价 
		/// </summary> 
		public int NowPrice {get; set;}
		/// <summary> 
		/// 立刻获得提示id 
		/// </summary> 
		public int DesId1 {get; set;}
		/// <summary> 
		/// 每日可领提示id 
		/// </summary> 
		public int DesId2 {get; set;}
		/// <summary> 
		/// 首充专属id 
		/// </summary> 
		public int MidasId {get; set;}
		#endregion

		public static TableMonthlyCard GetData(int ID)
		{
			return TableManager.MonthlyCardData.Get(ID);
		}

		public static List<TableMonthlyCard> GetAllData()
		{
			return TableManager.MonthlyCardData.GetAll();
		}

	}
	public sealed class TableMonthlyCardData
	{
		private Dictionary<int, TableMonthlyCard> dict = new Dictionary<int, TableMonthlyCard>();
		private List<TableMonthlyCard> dataList = new List<TableMonthlyCard>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableMonthlyCard.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableMonthlyCard>>(jsonContent);
			foreach (TableMonthlyCard config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableMonthlyCard Get(int id)
		{
			if (dict.TryGetValue(id, out TableMonthlyCard item))
				return item;
			return null;
		}

		public List<TableMonthlyCard> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
