#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableMonthlyCardNewExtraReward
	{

		public static readonly string TName="MonthlyCardNewExtraReward.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 掉落表id 
		/// </summary> 
		public int DropGroupId1 {get; set;}
		/// <summary> 
		/// 获得奖励提示 
		/// </summary> 
		public int[] DesId1 {get; set;}
		/// <summary> 
		/// 获得奖励数值 
		/// </summary> 
		public int[] DesId2 {get; set;}
		#endregion

		public static TableMonthlyCardNewExtraReward GetData(int ID)
		{
			return TableManager.MonthlyCardNewExtraRewardData.Get(ID);
		}

		public static List<TableMonthlyCardNewExtraReward> GetAllData()
		{
			return TableManager.MonthlyCardNewExtraRewardData.GetAll();
		}

	}
	public sealed class TableMonthlyCardNewExtraRewardData
	{
		private Dictionary<int, TableMonthlyCardNewExtraReward> dict = new Dictionary<int, TableMonthlyCardNewExtraReward>();
		private List<TableMonthlyCardNewExtraReward> dataList = new List<TableMonthlyCardNewExtraReward>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableMonthlyCardNewExtraReward.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableMonthlyCardNewExtraReward>>(jsonContent);
			foreach (TableMonthlyCardNewExtraReward config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableMonthlyCardNewExtraReward Get(int id)
		{
			if (dict.TryGetValue(id, out TableMonthlyCardNewExtraReward item))
				return item;
			return null;
		}

		public List<TableMonthlyCardNewExtraReward> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
