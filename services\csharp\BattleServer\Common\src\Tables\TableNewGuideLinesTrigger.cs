#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableNewGuideLinesTrigger
	{

		public static readonly string TName="NewGuideLinesTrigger.json";

		#region 属性定义
		/// <summary> 
		/// 引导步骤ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 启动触发类型：1、领取完主线任务2、完成主线任务3、上一步新手引导完成4、完成副本关卡5、起名之后16、解锁建筑小区块 
		/// </summary> 
		public int StartType {get; set;}
		/// <summary> 
		/// 参数信息 
		/// </summary> 
		public int StartParameter {get; set;}
		/// <summary> 
		/// 结束条件类型（服务器数据）：1、领取主线任务2、完成任务3新手结束触发下一个新手4、金币经验主线成功结束5起名结束7、抽卡结束8获得龙蛋9龙蛋孵化结束  10、挖宝  11 开蛋 12、合成 13、第一次上阵 14、解锁区域 15、领取龙岛金币16、解锁建筑小区块 
		/// </summary> 
		public int EndType {get; set;}
		/// <summary> 
		/// 参数信息（结束条件参数） 
		/// </summary> 
		public int EndParameter {get; set;}
		/// <summary> 
		/// 引导组首ID，只填写该引导组首Id 
		/// </summary> 
		public int GuildStartId {get; set;}
		/// <summary> 
		/// 是否在主场景触发1.不在2.在3.花路主线关卡触发引导时填写 
		/// </summary> 
		public int IsInMainScene {get; set;}
		/// <summary> 
		/// 触发机制是否需要回退 
		/// </summary> 
		public int IsGoBack {get; set;}
		/// <summary> 
		/// 触发引导时需要保留的界面 
		/// </summary> 
		public string[] StayUI {get; set;}
		#endregion

		public static TableNewGuideLinesTrigger GetData(int ID)
		{
			return TableManager.NewGuideLinesTriggerData.Get(ID);
		}

		public static List<TableNewGuideLinesTrigger> GetAllData()
		{
			return TableManager.NewGuideLinesTriggerData.GetAll();
		}

	}
	public sealed class TableNewGuideLinesTriggerData
	{
		private Dictionary<int, TableNewGuideLinesTrigger> dict = new Dictionary<int, TableNewGuideLinesTrigger>();
		private List<TableNewGuideLinesTrigger> dataList = new List<TableNewGuideLinesTrigger>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableNewGuideLinesTrigger.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableNewGuideLinesTrigger>>(jsonContent);
			foreach (TableNewGuideLinesTrigger config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableNewGuideLinesTrigger Get(int id)
		{
			if (dict.TryGetValue(id, out TableNewGuideLinesTrigger item))
				return item;
			return null;
		}

		public List<TableNewGuideLinesTrigger> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
