#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TablePayment
	{

		public static readonly string TName="Payment.json";

		#region 属性定义
		/// <summary> 
		/// 序列id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 名字 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 首充标记 
		/// </summary> 
		public int FirstFlag {get; set;}
		/// <summary> 
		/// 商品类型（1 普通充值 2 普通月卡 3 超级月卡） 
		/// </summary> 
		public int GoodType {get; set;}
		/// <summary> 
		/// 商品价格 
		/// </summary> 
		public int Price {get; set;}
		/// <summary> 
		/// 钻石数量 
		/// </summary> 
		public int DiamondNum {get; set;}
		/// <summary> 
		/// 首冲赠送 
		/// </summary> 
		public int FirstBuy {get; set;}
		/// <summary> 
		/// 非首充赠送 
		/// </summary> 
		public int ExDiamondNum {get; set;}
		/// <summary> 
		/// 米大师表ID 
		/// </summary> 
		public int MidasDiamondId {get; set;}
		/// <summary> 
		/// 商品价格 
		/// </summary> 
		public float PriceShow {get; set;}
		/// <summary> 
		/// 消耗多少现金券 
		/// </summary> 
		public int CashCouponPay {get; set;}
		#endregion

		public static TablePayment GetData(int ID)
		{
			return TableManager.PaymentData.Get(ID);
		}

		public static List<TablePayment> GetAllData()
		{
			return TableManager.PaymentData.GetAll();
		}

	}
	public sealed class TablePaymentData
	{
		private Dictionary<int, TablePayment> dict = new Dictionary<int, TablePayment>();
		private List<TablePayment> dataList = new List<TablePayment>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TablePayment.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TablePayment>>(jsonContent);
			foreach (TablePayment config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TablePayment Get(int id)
		{
			if (dict.TryGetValue(id, out TablePayment item))
				return item;
			return null;
		}

		public List<TablePayment> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
