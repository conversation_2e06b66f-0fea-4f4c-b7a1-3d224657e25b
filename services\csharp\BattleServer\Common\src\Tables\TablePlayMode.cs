#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TablePlayMode
	{

		public static readonly string TName="PlayMode.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 玩法类型枚举 
		/// </summary> 
		public int Type {get; set;}
		/// <summary> 
		/// 战斗场景资源ID 
		/// </summary> 
		public int Scene {get; set;}
		/// <summary> 
		/// 备战时长(秒) 
		/// </summary> 
		public int PreDuration {get; set;}
		/// <summary> 
		/// 战斗时长(秒) 
		/// </summary> 
		public int BattleDuration {get; set;}
		/// <summary> 
		/// 选BUFF时长（秒） 
		/// </summary> 
		public int BuffDuration {get; set;}
		/// <summary> 
		/// 加速开始时刻(秒) 
		/// </summary> 
		public int SpeedUpTime {get; set;}
		/// <summary> 
		/// 提供BUFF回合数 
		/// </summary> 
		public int[] BuffRound {get; set;}
		/// <summary> 
		/// BUFF库 
		/// </summary> 
		public int[] BuffList {get; set;}
		/// <summary> 
		/// 指定杯段时玩家血量 
		/// </summary> 
		public int[][] PlayerHP {get; set;}
		/// <summary> 
		/// 回合开始时提供英雄 
		/// </summary> 
		public int[][] AddHero {get; set;}
		/// <summary> 
		/// 单角色复选最大次数 
		/// </summary> 
		public int CheckTimes {get; set;}
		/// <summary> 
		/// 最大星级 
		/// </summary> 
		public int StarLimit {get; set;}
		/// <summary> 
		/// 上阵角色 
		/// </summary> 
		public int[] HeroList {get; set;}
		#endregion

		public static TablePlayMode GetData(int ID)
		{
			return TableManager.PlayModeData.Get(ID);
		}

		public static List<TablePlayMode> GetAllData()
		{
			return TableManager.PlayModeData.GetAll();
		}

	}
	public sealed class TablePlayModeData
	{
		private Dictionary<int, TablePlayMode> dict = new Dictionary<int, TablePlayMode>();
		private List<TablePlayMode> dataList = new List<TablePlayMode>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TablePlayMode.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TablePlayMode>>(jsonContent);
			foreach (TablePlayMode config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TablePlayMode Get(int id)
		{
			if (dict.TryGetValue(id, out TablePlayMode item))
				return item;
			return null;
		}

		public List<TablePlayMode> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
