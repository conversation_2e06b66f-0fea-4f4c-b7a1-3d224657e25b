#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TablePlayerBaseAttr
	{

		public static readonly string TName="PlayerBaseAttr.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 晴天的模型 
		/// </summary> 
		public int[] SunnyModelIds {get; set;}
		/// <summary> 
		/// 雨天的模型 
		/// </summary> 
		public int[] RainModelIds {get; set;}
		/// <summary> 
		/// 拥有的技能id 
		/// </summary> 
		public int OwnSkillConfigId {get; set;}
		/// <summary> 
		/// 武器挂点名字 
		/// </summary> 
		public string WeaponPoint {get; set;}
		/// <summary> 
		/// 默认的武器模型id 
		/// </summary> 
		public int DefaultWeaponModelId {get; set;}
		/// <summary> 
		/// 角色死亡后尸体存在的时间 
		/// </summary> 
		public float PlayerDeathTime {get; set;}
		/// <summary> 
		/// 攻击 
		/// </summary> 
		public int Attack {get; set;}
		/// <summary> 
		/// 生命 
		/// </summary> 
		public int Hp {get; set;}
		/// <summary> 
		/// 防御 
		/// </summary> 
		public int Def {get; set;}
		/// <summary> 
		/// 命中 
		/// </summary> 
		public int HitPre {get; set;}
		/// <summary> 
		/// 闪避 
		/// </summary> 
		public int DodgePre {get; set;}
		/// <summary> 
		/// 基础抗性 
		/// </summary> 
		public int BaseOccupationDef {get; set;}
		/// <summary> 
		/// 抗性类型数组 
		/// </summary> 
		public int[] OccupationDefArr {get; set;}
		/// <summary> 
		/// 抗性的值 
		/// </summary> 
		public int OccupationDefValue {get; set;}
		/// <summary> 
		/// 穿透值 
		/// </summary> 
		public int Penetrate {get; set;}
		/// <summary> 
		/// 穿透率 
		/// </summary> 
		public int PenetratePre {get; set;}
		/// <summary> 
		/// 暴击抵抗 
		/// </summary> 
		public int CriticalResistPre {get; set;}
		/// <summary> 
		/// 暴击率 
		/// </summary> 
		public int CriticalPre {get; set;}
		/// <summary> 
		/// 暴击倍数 
		/// </summary> 
		public int CriticalMultiplePre {get; set;}
		#endregion

		public static TablePlayerBaseAttr GetData(int ID)
		{
			return TableManager.PlayerBaseAttrData.Get(ID);
		}

		public static List<TablePlayerBaseAttr> GetAllData()
		{
			return TableManager.PlayerBaseAttrData.GetAll();
		}

	}
	public sealed class TablePlayerBaseAttrData
	{
		private Dictionary<int, TablePlayerBaseAttr> dict = new Dictionary<int, TablePlayerBaseAttr>();
		private List<TablePlayerBaseAttr> dataList = new List<TablePlayerBaseAttr>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TablePlayerBaseAttr.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TablePlayerBaseAttr>>(jsonContent);
			foreach (TablePlayerBaseAttr config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TablePlayerBaseAttr Get(int id)
		{
			if (dict.TryGetValue(id, out TablePlayerBaseAttr item))
				return item;
			return null;
		}

		public List<TablePlayerBaseAttr> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
