#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TablePlayerLevel
	{

		public static readonly string TName="PlayerLevel.json";

		#region 属性定义
		/// <summary> 
		/// Id（等级id） 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 等级经验（升至下一等级所需经验值） 
		/// </summary> 
		public int Exp {get; set;}
		/// <summary> 
		/// 对应的功能解锁id（升至下一等级所开启的功能） 
		/// </summary> 
		public int FuncId {get; set;}
		/// <summary> 
		/// 对应的技能解锁id（升至下一等级所开启的技能） 
		/// </summary> 
		public int SkillId {get; set;}
		#endregion

		public static TablePlayerLevel GetData(int ID)
		{
			return TableManager.PlayerLevelData.Get(ID);
		}

		public static List<TablePlayerLevel> GetAllData()
		{
			return TableManager.PlayerLevelData.GetAll();
		}

	}
	public sealed class TablePlayerLevelData
	{
		private Dictionary<int, TablePlayerLevel> dict = new Dictionary<int, TablePlayerLevel>();
		private List<TablePlayerLevel> dataList = new List<TablePlayerLevel>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TablePlayerLevel.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TablePlayerLevel>>(jsonContent);
			foreach (TablePlayerLevel config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TablePlayerLevel Get(int id)
		{
			if (dict.TryGetValue(id, out TablePlayerLevel item))
				return item;
			return null;
		}

		public List<TablePlayerLevel> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
