#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableRandomName
	{

		public static readonly string TName="RandomName.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 姓 
		/// </summary> 
		public string First {get; set;}
		/// <summary> 
		/// 名 
		/// </summary> 
		public string Second {get; set;}
		#endregion

		public static TableRandomName GetData(int ID)
		{
			return TableManager.RandomNameData.Get(ID);
		}

		public static List<TableRandomName> GetAllData()
		{
			return TableManager.RandomNameData.GetAll();
		}

	}
	public sealed class TableRandomNameData
	{
		private Dictionary<int, TableRandomName> dict = new Dictionary<int, TableRandomName>();
		private List<TableRandomName> dataList = new List<TableRandomName>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableRandomName.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableRandomName>>(jsonContent);
			foreach (TableRandomName config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableRandomName Get(int id)
		{
			if (dict.TryGetValue(id, out TableRandomName item))
				return item;
			return null;
		}

		public List<TableRandomName> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
