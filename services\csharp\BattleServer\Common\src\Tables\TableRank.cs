#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableRank
	{

		public static readonly string TName="Rank.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 对应功能 
		/// </summary> 
		public int Type {get; set;}
		/// <summary> 
		/// 排名 
		/// </summary> 
		public int[] Rank {get; set;}
		/// <summary> 
		/// 奖励 
		/// </summary> 
		public int[][] Drop {get; set;}
		#endregion

		public static TableRank GetData(int ID)
		{
			return TableManager.RankData.Get(ID);
		}

		public static List<TableRank> GetAllData()
		{
			return TableManager.RankData.GetAll();
		}

	}
	public sealed class TableRankData
	{
		private Dictionary<int, TableRank> dict = new Dictionary<int, TableRank>();
		private List<TableRank> dataList = new List<TableRank>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableRank.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableRank>>(jsonContent);
			foreach (TableRank config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableRank Get(int id)
		{
			if (dict.TryGetValue(id, out TableRank item))
				return item;
			return null;
		}

		public List<TableRank> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
