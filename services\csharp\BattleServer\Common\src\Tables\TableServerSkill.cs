#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableServerSkill
	{

		public static readonly string TName="ServerSkill.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 触发时机 
		/// </summary> 
		public int TiggerTiming {get; set;}
		/// <summary> 
		/// 触发概率千分比 
		/// </summary> 
		public int TiggerPro {get; set;}
		/// <summary> 
		/// 效果枚举 
		/// </summary> 
		public int LogicType {get; set;}
		/// <summary> 
		/// 效果参数 
		/// </summary> 
		public string LogicParam {get; set;}
		#endregion

		public static TableServerSkill GetData(int ID)
		{
			return TableManager.ServerSkillData.Get(ID);
		}

		public static List<TableServerSkill> GetAllData()
		{
			return TableManager.ServerSkillData.GetAll();
		}

	}
	public sealed class TableServerSkillData
	{
		private Dictionary<int, TableServerSkill> dict = new Dictionary<int, TableServerSkill>();
		private List<TableServerSkill> dataList = new List<TableServerSkill>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableServerSkill.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableServerSkill>>(jsonContent);
			foreach (TableServerSkill config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableServerSkill Get(int id)
		{
			if (dict.TryGetValue(id, out TableServerSkill item))
				return item;
			return null;
		}

		public List<TableServerSkill> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
