#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableSignBonus
	{

		public static readonly string TName="SignBonus.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 物品ID|数量 
		/// </summary> 
		public int[] Rewards {get; set;}
		/// <summary> 
		/// 基础权重 
		/// </summary> 
		public int Weight {get; set;}
		/// <summary> 
		/// lang表ID 
		/// </summary> 
		public int FortuneTips {get; set;}
		/// <summary> 
		/// 底框样式 
		/// </summary> 
		public int BgType {get; set;}
		/// <summary> 
		/// 特殊奖励 
		/// </summary> 
		public int Sp {get; set;}
		/// <summary> 
		/// 奖励位置 
		/// </summary> 
		public int Pos {get; set;}
		#endregion

		public static TableSignBonus GetData(int ID)
		{
			return TableManager.SignBonusData.Get(ID);
		}

		public static List<TableSignBonus> GetAllData()
		{
			return TableManager.SignBonusData.GetAll();
		}

	}
	public sealed class TableSignBonusData
	{
		private Dictionary<int, TableSignBonus> dict = new Dictionary<int, TableSignBonus>();
		private List<TableSignBonus> dataList = new List<TableSignBonus>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableSignBonus.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableSignBonus>>(jsonContent);
			foreach (TableSignBonus config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableSignBonus Get(int id)
		{
			if (dict.TryGetValue(id, out TableSignBonus item))
				return item;
			return null;
		}

		public List<TableSignBonus> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
