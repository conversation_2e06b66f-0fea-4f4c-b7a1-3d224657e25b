#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableSignIn
	{

		public static readonly string TName="SignIn.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 掉落表id 
		/// </summary> 
		public int[] ItemId {get; set;}
		/// <summary> 
		/// bgType 
		/// </summary> 
		public int BgType {get; set;}
		#endregion

		public static TableSignIn GetData(int ID)
		{
			return TableManager.SignInData.Get(ID);
		}

		public static List<TableSignIn> GetAllData()
		{
			return TableManager.SignInData.GetAll();
		}

	}
	public sealed class TableSignInData
	{
		private Dictionary<int, TableSignIn> dict = new Dictionary<int, TableSignIn>();
		private List<TableSignIn> dataList = new List<TableSignIn>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableSignIn.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableSignIn>>(jsonContent);
			foreach (TableSignIn config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableSignIn Get(int id)
		{
			if (dict.TryGetValue(id, out TableSignIn item))
				return item;
			return null;
		}

		public List<TableSignIn> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
