#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableSynergy
	{

		public static readonly string TName="Synergy.json";

		#region 属性定义
		/// <summary> 
		/// 羁绊ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 名称 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 生效阶段 
		/// </summary> 
		public int Stage {get; set;}
		/// <summary> 
		/// 激活动效 
		/// </summary> 
		public int ActiveVFX {get; set;}
		/// <summary> 
		/// 类型 
		/// </summary> 
		public int Type {get; set;}
		/// <summary> 
		/// 类型参数 
		/// </summary> 
		public string Param {get; set;}
		/// <summary> 
		/// 档位描述 
		/// </summary> 
		public int[] Desc {get; set;}
		/// <summary> 
		/// 档位局内技能List 
		/// </summary> 
		public int[] BattleSkillList {get; set;}
		/// <summary> 
		/// 档位局外技能List 
		/// </summary> 
		public int[] SkillList {get; set;}
		/// <summary> 
		/// 档位技能生效目标List 
		/// </summary> 
		public int[] TargetList {get; set;}
		#endregion

		public static TableSynergy GetData(int ID)
		{
			return TableManager.SynergyData.Get(ID);
		}

		public static List<TableSynergy> GetAllData()
		{
			return TableManager.SynergyData.GetAll();
		}

	}
	public sealed class TableSynergyData
	{
		private Dictionary<int, TableSynergy> dict = new Dictionary<int, TableSynergy>();
		private List<TableSynergy> dataList = new List<TableSynergy>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableSynergy.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableSynergy>>(jsonContent);
			foreach (TableSynergy config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableSynergy Get(int id)
		{
			if (dict.TryGetValue(id, out TableSynergy item))
				return item;
			return null;
		}

		public List<TableSynergy> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
