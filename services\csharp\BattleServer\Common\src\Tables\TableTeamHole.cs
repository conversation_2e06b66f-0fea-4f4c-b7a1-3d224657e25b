#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableTeamHole
	{

		public static readonly string TName="TeamHole.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 解锁所需材料 
		/// </summary> 
		public int[] CostItem {get; set;}
		#endregion

		public static TableTeamHole GetData(int ID)
		{
			return TableManager.TeamHoleData.Get(ID);
		}

		public static List<TableTeamHole> GetAllData()
		{
			return TableManager.TeamHoleData.GetAll();
		}

	}
	public sealed class TableTeamHoleData
	{
		private Dictionary<int, TableTeamHole> dict = new Dictionary<int, TableTeamHole>();
		private List<TableTeamHole> dataList = new List<TableTeamHole>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableTeamHole.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableTeamHole>>(jsonContent);
			foreach (TableTeamHole config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableTeamHole Get(int id)
		{
			if (dict.TryGetValue(id, out TableTeamHole item))
				return item;
			return null;
		}

		public List<TableTeamHole> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
