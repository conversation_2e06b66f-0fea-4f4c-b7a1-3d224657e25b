#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableTimeGiftPacks
	{

		public static readonly string TName="TimeGiftPacks.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 礼包类型（GiftPacksType枚举）1-日礼包 2-周礼包 3-月礼包 
		/// </summary> 
		public int GiftPacksType {get; set;}
		/// <summary> 
		/// 礼包名称 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 礼包价格 
		/// </summary> 
		public int NowPrice {get; set;}
		/// <summary> 
		/// 礼包图标 
		/// </summary> 
		public string Icon {get; set;}
		/// <summary> 
		/// 推荐礼包背景图 
		/// </summary> 
		public string GiftBg {get; set;}
		/// <summary> 
		/// 礼包描述 
		/// </summary> 
		public int GiftPacksDesc {get; set;}
		/// <summary> 
		/// 礼包ID（DropGroupId） 
		/// </summary> 
		public int DropGroupId {get; set;}
		/// <summary> 
		/// 终身可购买次数 
		/// </summary> 
		public int LifeLongBuyLimit {get; set;}
		/// <summary> 
		/// 本周期可购买次数 
		/// </summary> 
		public int BuyLimit {get; set;}
		/// <summary> 
		/// 购买类型（GiftPacksBuyType枚举）0-人民币 1-游戏货币 
		/// </summary> 
		public int BuyType {get; set;}
		/// <summary> 
		/// 购买消耗(货币或普通物品 id|数量)，购买类型为游戏货币不填时代表免费 
		/// </summary> 
		public int[] Cost {get; set;}
		/// <summary> 
		/// 折扣 
		/// </summary> 
		public int Discount {get; set;}
		/// <summary> 
		/// 刷新限制条件-开服天数(开服当天是0)（闭区间）(大于等于配一个参数就行) 
		/// </summary> 
		public int[] OpenServerDayLimit {get; set;}
		/// <summary> 
		/// 刷新限制条件-关卡（闭区间）(大于等于配一个参数就行) 
		/// </summary> 
		public int[] StageLimit {get; set;}
		/// <summary> 
		/// 刷新限制条件-前置商城礼包（前置礼包达到终身限购次数才算完成） 
		/// </summary> 
		public int PreGiftPacksLimit {get; set;}
		/// <summary> 
		/// MidasItem表ID 
		/// </summary> 
		public int MidasItemId {get; set;}
		/// <summary> 
		/// 购买条件限制（关卡） 
		/// </summary> 
		public int BuyStageLimit {get; set;}
		#endregion

		public static TableTimeGiftPacks GetData(int ID)
		{
			return TableManager.TimeGiftPacksData.Get(ID);
		}

		public static List<TableTimeGiftPacks> GetAllData()
		{
			return TableManager.TimeGiftPacksData.GetAll();
		}

	}
	public sealed class TableTimeGiftPacksData
	{
		private Dictionary<int, TableTimeGiftPacks> dict = new Dictionary<int, TableTimeGiftPacks>();
		private List<TableTimeGiftPacks> dataList = new List<TableTimeGiftPacks>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableTimeGiftPacks.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableTimeGiftPacks>>(jsonContent);
			foreach (TableTimeGiftPacks config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableTimeGiftPacks Get(int id)
		{
			if (dict.TryGetValue(id, out TableTimeGiftPacks item))
				return item;
			return null;
		}

		public List<TableTimeGiftPacks> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
