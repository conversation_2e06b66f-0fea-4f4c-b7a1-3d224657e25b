#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableTotalRecharge
	{

		public static readonly string TName="TotalRecharge.json";

		#region 属性定义
		/// <summary> 
		/// 属性类型 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// Pvp参数1 
		/// </summary> 
		public int Reward {get; set;}
		#endregion

		public static TableTotalRecharge GetData(int ID)
		{
			return TableManager.TotalRechargeData.Get(ID);
		}

		public static List<TableTotalRecharge> GetAllData()
		{
			return TableManager.TotalRechargeData.GetAll();
		}

	}
	public sealed class TableTotalRechargeData
	{
		private Dictionary<int, TableTotalRecharge> dict = new Dictionary<int, TableTotalRecharge>();
		private List<TableTotalRecharge> dataList = new List<TableTotalRecharge>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableTotalRecharge.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableTotalRecharge>>(jsonContent);
			foreach (TableTotalRecharge config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableTotalRecharge Get(int id)
		{
			if (dict.TryGetValue(id, out TableTotalRecharge item))
				return item;
			return null;
		}

		public List<TableTotalRecharge> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
