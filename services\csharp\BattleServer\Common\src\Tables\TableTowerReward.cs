#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableTowerReward
	{

		public static readonly string TName="TowerReward.json";

		#region 属性定义
		/// <summary> 
		/// id（第几层） 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 奖励id 
		/// </summary> 
		public int DropGroupId {get; set;}
		#endregion

		public static TableTowerReward GetData(int ID)
		{
			return TableManager.TowerRewardData.Get(ID);
		}

		public static List<TableTowerReward> GetAllData()
		{
			return TableManager.TowerRewardData.GetAll();
		}

	}
	public sealed class TableTowerRewardData
	{
		private Dictionary<int, TableTowerReward> dict = new Dictionary<int, TableTowerReward>();
		private List<TableTowerReward> dataList = new List<TableTowerReward>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableTowerReward.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableTowerReward>>(jsonContent);
			foreach (TableTowerReward config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableTowerReward Get(int id)
		{
			if (dict.TryGetValue(id, out TableTowerReward item))
				return item;
			return null;
		}

		public List<TableTowerReward> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
