#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableTreasureGacha
	{

		public static readonly string TName="TreasureGacha.json";

		#region 属性定义
		/// <summary> 
		/// 抽卡ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 消耗 
		/// </summary> 
		public int[] Cost {get; set;}
		/// <summary> 
		/// 卡包 
		/// </summary> 
		public int[][] Bag {get; set;}
		/// <summary> 
		/// 保底卡包 
		/// </summary> 
		public int[][] BagSpecial {get; set;}
		/// <summary> 
		/// 保底次数 
		/// </summary> 
		public int Count {get; set;}
		#endregion

		public static TableTreasureGacha GetData(int ID)
		{
			return TableManager.TreasureGachaData.Get(ID);
		}

		public static List<TableTreasureGacha> GetAllData()
		{
			return TableManager.TreasureGachaData.GetAll();
		}

	}
	public sealed class TableTreasureGachaData
	{
		private Dictionary<int, TableTreasureGacha> dict = new Dictionary<int, TableTreasureGacha>();
		private List<TableTreasureGacha> dataList = new List<TableTreasureGacha>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableTreasureGacha.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableTreasureGacha>>(jsonContent);
			foreach (TableTreasureGacha config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableTreasureGacha Get(int id)
		{
			if (dict.TryGetValue(id, out TableTreasureGacha item))
				return item;
			return null;
		}

		public List<TableTreasureGacha> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
