#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableTreasureLv
	{

		public static readonly string TName="TreasureLv.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 品质 
		/// </summary> 
		public int Quality {get; set;}
		/// <summary> 
		/// 等级 
		/// </summary> 
		public int Lv {get; set;}
		/// <summary> 
		/// 消耗 
		/// </summary> 
		public int[][] Cost {get; set;}
		#endregion

		public static TableTreasureLv GetData(int ID)
		{
			return TableManager.TreasureLvData.Get(ID);
		}

		public static List<TableTreasureLv> GetAllData()
		{
			return TableManager.TreasureLvData.GetAll();
		}

	}
	public sealed class TableTreasureLvData
	{
		private Dictionary<int, TableTreasureLv> dict = new Dictionary<int, TableTreasureLv>();
		private List<TableTreasureLv> dataList = new List<TableTreasureLv>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableTreasureLv.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableTreasureLv>>(jsonContent);
			foreach (TableTreasureLv config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableTreasureLv Get(int id)
		{
			if (dict.TryGetValue(id, out TableTreasureLv item))
				return item;
			return null;
		}

		public List<TableTreasureLv> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
