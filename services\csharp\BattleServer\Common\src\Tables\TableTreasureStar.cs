#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableTreasureStar
	{

		public static readonly string TName="TreasureStar.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 宝物品质 
		/// </summary> 
		public int Quality {get; set;}
		/// <summary> 
		/// 星级 
		/// </summary> 
		public int Star {get; set;}
		/// <summary> 
		/// 消耗宝物数量 
		/// </summary> 
		public int Cost1 {get; set;}
		/// <summary> 
		/// 消耗材料 
		/// </summary> 
		public int[][] Cost2 {get; set;}
		/// <summary> 
		/// 羁绊效果档位 
		/// </summary> 
		public int EffectRelation {get; set;}
		#endregion

		public static TableTreasureStar GetData(int ID)
		{
			return TableManager.TreasureStarData.Get(ID);
		}

		public static List<TableTreasureStar> GetAllData()
		{
			return TableManager.TreasureStarData.GetAll();
		}

	}
	public sealed class TableTreasureStarData
	{
		private Dictionary<int, TableTreasureStar> dict = new Dictionary<int, TableTreasureStar>();
		private List<TableTreasureStar> dataList = new List<TableTreasureStar>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableTreasureStar.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableTreasureStar>>(jsonContent);
			foreach (TableTreasureStar config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableTreasureStar Get(int id)
		{
			if (dict.TryGetValue(id, out TableTreasureStar item))
				return item;
			return null;
		}

		public List<TableTreasureStar> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
