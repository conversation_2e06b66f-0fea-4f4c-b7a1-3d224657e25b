#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableaccruedRewards
	{

		public static readonly string TName="accruedRewards.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 配置组 
		/// </summary> 
		public int Group {get; set;}
		/// <summary> 
		/// 任务类型 
		/// </summary> 
		public int Times {get; set;}
		/// <summary> 
		/// 掉落表id 
		/// </summary> 
		public int DropGroupId {get; set;}
		#endregion

		public static TableaccruedRewards GetData(int ID)
		{
			return TableManager.accruedRewardsData.Get(ID);
		}

		public static List<TableaccruedRewards> GetAllData()
		{
			return TableManager.accruedRewardsData.GetAll();
		}

	}
	public sealed class TableaccruedRewardsData
	{
		private Dictionary<int, TableaccruedRewards> dict = new Dictionary<int, TableaccruedRewards>();
		private List<TableaccruedRewards> dataList = new List<TableaccruedRewards>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableaccruedRewards.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableaccruedRewards>>(jsonContent);
			foreach (TableaccruedRewards config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableaccruedRewards Get(int id)
		{
			if (dict.TryGetValue(id, out TableaccruedRewards item))
				return item;
			return null;
		}

		public List<TableaccruedRewards> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
