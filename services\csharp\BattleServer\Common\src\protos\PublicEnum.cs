// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: PublicEnum.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Game.Core {

  /// <summary>Holder for reflection information generated from PublicEnum.proto</summary>
  public static partial class PublicEnumReflection {

    #region Descriptor
    /// <summary>File descriptor for PublicEnum.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static PublicEnumReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChBQdWJsaWNFbnVtLnByb3RvKmQKC0xvZ2luQnlUeXBlEhUKEUxvZ2luQnlU",
            "eXBlX090aGVyEAASHAoYTG9naW5CeVR5cGVfUVFHYW1lQ2VudGVyEAESIAoc",
            "TG9naW5CeVR5cGVfV2VDaGF0R2FtZUNlbnRlchACKnIKCU1vbmV5VHlwZRIS",
            "Cg5Nb25leVR5cGVfTm9uZRAAEhIKDk1vbmV5VHlwZV9Hb2xkEAESFQoRTW9u",
            "ZXlUeXBlX0RpYW1vbmQQAhITCg9Nb25leVR5cGVfUG93ZXIQAxIRCg1Nb25l",
            "eVR5cGVfRXhwEAQqiQEKDUF3YXJkSXRlbVR5cGUSFwoTQXdhcmRJdGVtVHlw",
            "ZV9Nb25leRAAEhYKEkF3YXJkSXRlbVR5cGVfSXRlbRABEhcKE0F3YXJkSXRl",
            "bVR5cGVfRXF1aXAQAhIXChNBd2FyZEl0ZW1UeXBlX1NraWxsEAMSFQoRQXdh",
            "cmRJdGVtVHlwZV9HZW0QBCpWCgxGdW5jTG9ja1R5cGUSFQoRRnVuY0xvY2tU",
            "eXBlX0xvY2sQABIXChNGdW5jTG9ja1R5cGVfVW5Mb2NrEAESFgoSRnVuY0xv",
            "Y2tUeXBlX0Nsb3NlEAIqQwoNU2NlbmVEcm9wVHlwZRIZChVTY2VuZURyb3BU",
            "eXBlX01vbnN0ZXIQABIXChNTY2VuZURyb3BUeXBlX0hvcnNlEAEq4QEKClJl",
            "ZERvdFR5cGUSEwoPUmVkRG90VHlwZV9Ob25lEAASFAoQUmVkRG90VHlwZV9T",
            "a2lsbBABEhMKD1JlZERvdFR5cGVfTWFpbBACEhUKEVJlZERvdFR5cGVfRnJp",
            "ZW5kEAMSGgoWUmVkRG90VHlwZV9GdW5jUHJldmlldxAEEhoKFlJlZERvdFR5",
            "cGVfSGVhdmVubHlEYW8QBRIcChhSZWREb3RUeXBlX0RhaWx5UGx1c0dpZnQQ",
            "BhISCg5SZWREb3RUeXBlX0JhZxAHEhIKDlJlZERvdFR5cGVfTUFYEAgqLgoL",
            "RUdlbmRlclR5cGUSCwoHRGVmYXVsdBAAEgcKA01hbhABEgkKBVdvbWFuEAIq",
            "hA0KDUF0dHJpYnV0ZVR5cGUSFgoSQXR0cmlidXRlVHlwZV9OT05FEAASGAoU",
            "QXR0cmlidXRlVHlwZV9BdHRhY2sQARIUChBBdHRyaWJ1dGVUeXBlX0hwEAIS",
            "FQoRQXR0cmlidXRlVHlwZV9EZWYQAxIbChdBdHRyaWJ1dGVUeXBlX01vdmVT",
            "cGVlZBAEEhgKFEF0dHJpYnV0ZVR5cGVfR3VuQXRrEAUSHAoYQXR0cmlidXRl",
            "VHlwZV9QaHlzaWNzQXRrEAYSGAoUQXR0cmlidXRlVHlwZV9JY2VBdGsQBxIg",
            "ChxBdHRyaWJ1dGVUeXBlX0VsZWN0cmljaXR5QXRrEAgSGQoVQXR0cmlidXRl",
            "VHlwZV9GaXJlQXRrEAkSGwoXQXR0cmlidXRlVHlwZV9FbmVyZ3lBdGsQChIZ",
            "ChVBdHRyaWJ1dGVUeXBlX1dpbmRBdGsQCxIbChdBdHRyaWJ1dGVUeXBlX1Bl",
            "bmV0cmF0ZRAMEhgKFEF0dHJpYnV0ZVR5cGVfSGl0UHJlEBQSGgoWQXR0cmli",
            "dXRlVHlwZV9Eb2RnZVByZRAVEhsKF0F0dHJpYnV0ZVR5cGVfQXR0YWNrUHJl",
            "EBYSFwoTQXR0cmlidXRlVHlwZV9IcFByZRAXEhgKFEF0dHJpYnV0ZVR5cGVf",
            "RGVmUHJlEBgSHgoaQXR0cmlidXRlVHlwZV9Nb3ZlU3BlZWRQcmUQGRIdChlB",
            "dHRyaWJ1dGVUeXBlX0NyaXRpY2FsUHJlEBoSJQohQXR0cmlidXRlVHlwZV9D",
            "cml0aWNhbE11bHRpcGxlUHJlEBsSIwofQXR0cmlidXRlVHlwZV9Dcml0aWNh",
            "bFJlc2lzdFByZRAcEh8KG0F0dHJpYnV0ZVR5cGVfQmFzZURhbWFnZVByZRAd",
            "Eh4KGkF0dHJpYnV0ZVR5cGVfR3VuRGFtYWdlUHJlEB4SIgoeQXR0cmlidXRl",
            "VHlwZV9QaHlzaWNzRGFtYWdlUHJlEB8SHgoaQXR0cmlidXRlVHlwZV9JY2VE",
            "YW1hZ2VQcmUQIBImCiJBdHRyaWJ1dGVUeXBlX0VsZWN0cmljaXR5RGFtYWdl",
            "UHJlECESHwobQXR0cmlidXRlVHlwZV9GaXJlRGFtYWdlUHJlECISIQodQXR0",
            "cmlidXRlVHlwZV9FbmVyZ3lEYW1hZ2VQcmUQIxIfChtBdHRyaWJ1dGVUeXBl",
            "X1dpbmREYW1hZ2VQcmUQJBIeChpBdHRyaWJ1dGVUeXBlX1BlbmV0cmF0ZVBy",
            "ZRAlEhsKF0F0dHJpYnV0ZVR5cGVfR3VuRGVmUHJlECYSHwobQXR0cmlidXRl",
            "VHlwZV9QaHlzaWNzRGVmUHJlECcSGwoXQXR0cmlidXRlVHlwZV9JY2VEZWZQ",
            "cmUQKBIjCh9BdHRyaWJ1dGVUeXBlX0VsZWN0cmljaXR5RGVmUHJlECkSHAoY",
            "QXR0cmlidXRlVHlwZV9GaXJlRGVmUHJlECoSHgoaQXR0cmlidXRlVHlwZV9F",
            "bmVyZ3lEZWZQcmUQKxIcChhBdHRyaWJ1dGVUeXBlX1dpbmREZWZQcmUQLBIk",
            "CiBBdHRyaWJ1dGVUeXBlX0dyb3VuZERlbWFnZUFkZFByZRAtEiQKIEF0dHJp",
            "YnV0ZVR5cGVfTWlkYWlyRGFtYWdlQWRkUHJlEC4SIgoeQXR0cmlidXRlVHlw",
            "ZV9OZWFyRGFtYWdlQWRkUHJlEC8SJQohQXR0cmlidXRlVHlwZV9Mb25nRGlz",
            "RGFtYWdlQWRkUHJlEDASKgomQXR0cmlidXRlVHlwZV9TbWFsbE1vbnN0ZXJE",
            "YW1hZ2VBZGRQcmUQMRIqCiZBdHRyaWJ1dGVUeXBlX0VsaXRlTW9uc3RlckRh",
            "bWFnZUFkZFByZRAyEikKJUF0dHJpYnV0ZVR5cGVfQm9zc01vbnN0ZXJEYW1h",
            "Z2VBZGRQcmUQMxIfChtBdHRyaWJ1dGVUeXBlX0RlY3JlYXNlR3VuQ0QQNBIh",
            "Ch1BdHRyaWJ1dGVUeXBlX0RlY3JlYXNlU2tpbGxDRBA1Eh4KGkF0dHJpYnV0",
            "ZVR5cGVfR29sZEJvbnVzUHJlEFASHQoZQXR0cmlidXRlVHlwZV9FeHBCb251",
            "c1ByZRBREhwKGEF0dHJpYnV0ZVR5cGVfV2FyQ29pblByZRBSEhoKFkF0dHJp",
            "YnV0ZVR5cGVfTWFnYXppbmUQUxIdChlBdHRyaWJ1dGVUeXBlX01hZ2F6aW5l",
            "UHJlEFQqagoLTW9uc3RlclR5cGUSFAoQTW9uc3RlclR5cGVfTm9uZRAAEhUK",
            "EU1vbnN0ZXJUeXBlX1NtYWxsEAESGAoUTW9uc3RlclR5cGVfQWR2YW5jZWQQ",
            "AhIUChBNb25zdGVyVHlwZV9Cb3NzEAMqQAoKQmF0dGxlVHlwZRIVChFCYXR0",
            "bGVUeXBlX0NvbW1vbhAAEhsKF0JhdHRsZVR5cGVfU3RhbmRpbmdQaWxlEGQq",
            "0gEKC0JhdHRsZVN0YXRlEg4KClNUQVRFX05PTkUQABIVChFTVEFURV9ST1VO",
            "RF9TVEFSVBABEhUKEVNUQVRFX1BSRVBBUkFUSU9OEAISGQoVU1RBVEVfQkFU",
            "VExFX1NUQVJUSU5HEAMSHAoYU1RBVEVfQkFUVExFX0lOX1BST0dSRVNTEAQS",
            "GgoWU1RBVEVfUk9VTkRfU0VUVExFTUVOVBAFEhsKF1NUQVRFX0VMSU1JTkFU",
            "SU9OX0NIRUNLEAYSEwoPU1RBVEVfR0FNRV9PVkVSEAcqfQoNSW5zdGFuY2VT",
            "dGF0ZRIUChBJTlNUQU5DRV9XQUlUSU5HEAASHwobSU5TVEFOQ0VfQkFUVExF",
            "X0lOX1BST0dSRVNTEAESHAoYSU5TVEFOQ0VfQkFUVExFX0ZJTklTSEVEEAIS",
            "FwoTSU5TVEFOQ0VfU0VUVExFTUVOVBADKrMBCgtQbGF5ZXJTdGF0ZRISCg5Q",
            "TEFZRVJfV0FJVElORxAAEhkKFVBMQVlFUl9TRUxFQ1RJTkdfQlVGRhABEhsK",
            "F1BMQVlFUl9SRUNFSVZJTkdfSEVST0VTEAISGQoVUExBWUVSX0ZSRUVfT1BF",
            "UkFUSU9OEAMSEAoMUExBWUVSX1JFQURZEAQSFAoQUExBWUVSX0lOX0JBVFRM",
            "RRAFEhUKEVBMQVlFUl9FTElNSU5BVEVEEAYqVQoMQWRSZXdhcmRUeXBlEhgK",
            "FEJBVFRMRV9BRFJFV0FSRF9OT05FEAASFgoSQkFUVExFX1NVUFBMWV9EUk9Q",
            "EAESEwoPQkFUVExFX0JMRVNTSU5HEAIqYAoJR2FjaGFUeXBlEhMKD0dhY2hh",
            "VHlwZV9FcXVpcBAAEhMKD0dhY2hhVHlwZV9Ta2lsbBABEhUKEUdhY2hhVHlw",
            "ZV9IYWxsb3dzEAISEgoOR2FjaGFUeXBlX1BldHMQAyqGAQoJRXF1aXBUeXBl",
            "Eg4KCkVxdWlwX05PTkUQABIQCgxFcXVpcF9CcmFjZXIQARIQCgxFcXVpcF9I",
            "ZWxtZXQQAhIPCgtFcXVpcF9TaG9lcxADEg8KC0VxdWlwX1BhbnRzEAQSEAoM",
            "RXF1aXBfR2xvdmVzEAUSEQoNRXF1aXBfQ2xvdGhlcxAGKjkKD0Ryb3BFeHBy",
            "ZXNzVHlwZRISCg5FeHByZXNzX05vcm1hbBAAEhIKDkV4cHJlc3NfU3RhZ2Vz",
            "EAEqtwEKC1F1YWxpdHlUeXBlEhAKDFF1YWxpdHlfR3JleRAAEhEKDVF1YWxp",
            "dHlfR3JlZW4QARIQCgxRdWFsaXR5X0JsdWUQAhISCg5RdWFsaXR5X1B1cnBs",
            "ZRADEhAKDFF1YWxpdHlfR29sZBAEEhIKDlF1YWxpdHlfT3JhbmdlEAUSDwoL",
            "UXVhbGl0eV9SZWQQBhITCg9RdWFsaXR5X0RlZXBSZWQQBxIRCg1RdWFsaXR5",
            "X0FyZ3M4EAgqzgIKCE1haWxUeXBlEhYKEk1haWxUeXBlX05vblJld2FyZBAA",
            "EhMKD01haWxUeXBlX1Jld2FyZBABEhkKFU1haWxUeXBlX05vdGlmeVJld2Fy",
            "ZBACEhcKE01haWxUeXBlX1BsYXllckF1dGgQAxIbChdNYWlsVHlwZV9QbGF5",
            "ZXJBdXRoSW5mbxAEEh0KGU1haWxUeXBlX1BsYXllck1haWxSZW1vdmUQBRIU",
            "ChBNYWlsVHlwZV9HaWZ0Uk1CEAYSIQodTWFpbFR5cGVfR2xvYmFsQWN0aXZp",
            "dHlQbGF5ZXIQBxIPCgtNYWlsVHlwZV9BRBAIEhwKGE1haWxUeXBlX0d1aWxk",
            "T2ZmbGluZU1zZxAJEhcKE01haWxUeXBlX1F1ZXN0QXdhcmQQChIkCiBNYWls",
            "VHlwZV9BY3Rpdml0eV9PcGVuU2VydmVyX0VuZBALKl0KDU1haWxTdGF0ZVR5",
            "cGUSGAoUTWFpbFN0YXRlVHlwZV9VblJlYWQQABIWChJNYWlsU3RhdGVUeXBl",
            "X1JlYWQQARIaChZNYWlsU3RhdGVUeXBlX0dvdEF3YXJkEAIqYwoOTWFpbFJl",
            "YXNvblR5cGUSGQoVTWFpbFJlYXNvblR5cGVfVGVzdEdNEAASGwoXTWFpbFJl",
            "YXNvblR5cGVfR01SZW1vdGUQARIZChVNYWlsUmVhc29uVHlwZV9TeXN0ZW0Q",
            "Aip7ChBHdWlkZVRyaWdnZXJUeXBlEggKBE5vbmUQABINCglOZXdQbGF5ZXIQ",
            "ARIKCgZPcGVuVWkQAhILCgdDbG9zZVVpEAMSEAoMVW5sb2NrRnVuY0lkEAQS",
            "EgoOUGFzc2VkTWFpbkxpbmUQBRIPCgtGaW5pc2hHdWlkZRAGKsECChNOZXdH",
            "dWlkZVRyaWdnZXJUeXBlEh4KGk5ld0d1aWRlVHJpZ2dlclR5cGVfTm9ybWFs",
            "EAASJwojTmV3R3VpZGVUcmlnZ2VyVHlwZV9HZXRDb21wbGV0ZVRhc2sQARIl",
            "CiFOZXdHdWlkZVRyaWdnZXJUeXBlX01pc3Npb25GaW5pc2gQAhIjCh9OZXdH",
            "dWlkZVRyaWdnZXJUeXBlX05ld0d1aWxkRW5kEAMSIwofTmV3R3VpZGVUcmln",
            "Z2VyVHlwZV9JbnN0YW5jZUVuZBAEEiIKHk5ld0d1aWRlVHJpZ2dlclR5cGVf",
            "Q3JlYXRlTmFtZRAFEigKJE5ld0d1aWRlVHJpZ2dlclR5cGVfQ29vcGVyYXRp",
            "b25TdGFnZRAGEiIKHk5ld0d1aWRlVHJpZ2dlclR5cGVfTG90dGVyeUVuZBAH",
            "Kk0KD05ld0d1aWRlRnVuVHlwZRIaChZOZXdHdWlkZUZ1blR5cGVfTm9ybWFs",
            "EAASHgoaTmV3R3VpZGVGdW5UeXBlX0NoYW5nZU5hbWUQASp4CgxTaWduSW5U",
            "b1R5cGUSFQoRU2lnbkluVG9UeXBlX05vbmUQABIZChVTaWduSW5Ub1R5cGVf",
            "U2V2ZW5EYXkQARIZChVTaWduSW5Ub1R5cGVfRXZlcnlEYXkQAhIbChdTaWdu",
            "SW5Ub1R5cGVfTW9udGhTdGFnZRADKloKDFJld2FyZFN0YXR1cxIWChJSZXdh",
            "cmRTdGF0dXNfRG9pbmcQABIXChNSZXdhcmRTdGF0dXNfRmluaXNoEAESGQoV",
            "UmV3YXJkU3RhdHVzX1JlY2VpdmVkEAIqWAoKUmV3YXJkVHlwZRIXChNSRVdB",
            "UkRfVFlQRV9VTktOT1dOEAASFAoQUkVXQVJEX1RZUEVfUkFOSxABEhsKF1JF",
            "V0FSRF9UWVBFX1NFQVNPTl9SQU5LEAIqUgoISXRlbVR5cGUSDwoLVW5hdmFp",
            "bGFibGUQABIICgREcm9wEAESEAoMSGFuZ1VwUmV3YXJkEAISDQoJQ2hvb3Nl",
            "Qm94EAMSCgoGRGV2cmlzEAQqQAoVSXRlbVN1YlR5cGVfQ2hvb3NlQm94EhIK",
            "DkNob29zZUJveF9PbmNlEAASEwoPQ2hvb3NlQm94X011bHRpEAEqMwoQSXRl",
            "bVN1YlR5cGVfRHJvcBIOCgpEcm9wX0ZpeGVkEAASDwoLRHJvcF9SYW5kb20Q",
            "ASpaChJJdGVtU3ViVHlwZV9EZXZyaXMSDwoLRGV2cmlzX1NraW4QABISCg5E",
            "ZXZyaXNfRmlyZWFybRABEg8KC0RldnJpc19XYWxsEAISDgoKRGV2cmlzX1Bl",
            "dBADKh0KC0JhZ0l0ZW1UeXBlEg4KCk5vcm1hbEl0ZW0QACrQAQoTU3lzdGVt",
            "U2hvd0Vycm9yVHlwZRIcChhTeXN0ZW1TaG93RXJyb3JUeXBlX05vbmUQABIc",
            "ChhTeXN0ZW1TaG93RXJyb3JUeXBlX0tpY2sQARIfChtTeXN0ZW1TaG93RXJy",
            "b3JUeXBlX1JlcGxhY2UQAhIfChtTeXN0ZW1TaG93RXJyb3JUeXBlX0NoYXRC",
            "YW4QAxIcChhTeXN0ZW1TaG93RXJyb3JUeXBlX1RpcHMQBBIdChlTeXN0ZW1T",
            "aG93RXJyb3JUeXBlX0xvZ2luEAUqsAEKDUdpZnRQYWNrc1R5cGUSFgoSR2lm",
            "dFBhY2tzVHlwZV9Ob25lEAASFwoTR2lmdFBhY2tzVHlwZV9MaW1pdBABEhgK",
            "FEdpZnRQYWNrc1R5cGVfV2Vla2x5EAISGwoXR2lmdFBhY2tzVHlwZV9EYWls",
            "eVBsdXMQAxIaChZHaWZ0UGFja3NUeXBlX1dlZWtDYXJkEAQSGwoXR2lmdFBh",
            "Y2tzVHlwZV9SZWNvbW1lbmQQBSp/ChBHaWZ0UGFja3NCdXlUeXBlEhgKFEdp",
            "ZnRQYWNrc0J1eVR5cGVfQWR2EAASGAoUR2lmdFBhY2tzQnV5VHlwZV9SbWIQ",
            "ARIZChVHaWZ0UGFja3NCdXlUeXBlX0ZyZWUQAhIcChhHaWZ0UGFja3NCdXlU",
            "eXBlX0RpYW1vbmQQAyqNAQoKQ2hhcmdlVHlwZRITCg9DaGFyZ2VUeXBlX05v",
            "bmUQABIWChJDaGFyZ2VUeXBlX0RpYW1vbmQQARIeChpDaGFyZ2VUeXBlX01v",
            "bnRoQ2FyZE5vcm1hbBACEh0KGUNoYXJnZVR5cGVfTW9udGhDYXJkU3VwZXIQ",
            "AxITCg9DaGFyZ2VUeXBlX0l0ZW0QBCp3ChNDb21tb25Cb3hSZXdhcmRUeXBl",
            "EhgKFENvbW1vbkJveFJld2FyZF9Ob25lEAASIAocQ29tbW9uQm94UmV3YXJk",
            "X0RhaWx5TWlzc2lvbhABEiQKIENvbW1vbkJveFJld2FyZF9TZXZlbkRheUFj",
            "dGl2aXR5EAIqXgoOQmF0dGxlUGFzc1R5cGUSFwoTQmF0dGxlUGFzc1R5cGVf",
            "Tk9ORRAAEhcKE0JhdHRsZVBhc3NUeXBlX0JBU0UQARIaChZCYXR0bGVQYXNz",
            "VHlwZV9BRFZBTkNFEAIqegoVQmF0dGxlUGFzc01pc3Npb25UeXBlEh4KGkJh",
            "dHRsZVBhc3NNaXNzaW9uVHlwZV9OT05FEAASHwobQmF0dGxlUGFzc01pc3Np",
            "b25UeXBlX0RBSUxZEAESIAocQmF0dGxlUGFzc01pc3Npb25UeXBlX1dFRUtM",
            "WRACKlsKCkF3YXJkU3RhdGUSGgoWQXdhcmRTdGF0ZV9VTkFWQUlMQUJMRRAA",
            "EhgKFEF3YXJkU3RhdGVfQVZBSUxBQkxFEAESFwoTQXdhcmRTdGF0ZV9SRUNF",
            "SVZFRBACKmMKDEFjdGl2aXR5VHlwZRIbChdBY3Rpdml0eVR5cGVfQmF0dGxl",
            "UGFzcxAAEhoKFkFjdGl2aXR5VHlwZV9TZXZlblRhc2sQARIaChZBY3Rpdml0",
            "eVR5cGVfU2V2ZW5TaWduEAIqXgoOQWN0aXZpdHlTdGF0dXMSGgoWQWN0aXZp",
            "dHlTdGF0dXNfTk9fT1BFThAAEhgKFEFjdGl2aXR5U3RhdHVzX1NUQVJUEAES",
            "FgoSQWN0aXZpdHlTdGF0dXNfRU5EEAIq2AUKFEl0ZW1BY3F1aXNpdGlvblN0",
            "YXRlEh0KGUl0ZW1BY3F1aXNpdGlvblN0YXRlX05PTkUQABIgChxJdGVtQWNx",
            "dWlzaXRpb25TdGF0ZV9EaWFtb25kEAESHQoZSXRlbUFjcXVpc2l0aW9uU3Rh",
            "dGVfU3RhchACEiAKHEl0ZW1BY3F1aXNpdGlvblN0YXRlX0Vzc2VuY2UQAxIk",
            "CiBJdGVtQWNxdWlzaXRpb25TdGF0ZV9BaXJQdXJpZmllchAEEiYKIkl0ZW1B",
            "Y3F1aXNpdGlvblN0YXRlX1VwZ3JhZGVUaWNrZXQQBRIoCiRJdGVtQWNxdWlz",
            "aXRpb25TdGF0ZV9EdXBsaWNhdGVUaWNrZXQQBhIfChtJdGVtQWNxdWlzaXRp",
            "b25TdGF0ZV9TaG92ZWwQBxIiCh5JdGVtQWNxdWlzaXRpb25TdGF0ZV9EcmFn",
            "b25FZ2cQCBIjCh9JdGVtQWNxdWlzaXRpb25TdGF0ZV9NYWdpY0Nsb3RoEAkS",
            "HwobSXRlbUFjcXVpc2l0aW9uU3RhdGVfQ2hpc2VsEAoSMgouSXRlbUFjcXVp",
            "c2l0aW9uU3RhdGVfRHJhZ29uQ29tcG91bmRTbG90VXBncmFkZRALEiUKIUl0",
            "ZW1BY3F1aXNpdGlvblN0YXRlX1NsaW1lUmVjdXJpdBAMEjIKLkl0ZW1BY3F1",
            "aXNpdGlvblN0YXRlX0RyYWdvbkZhY3RvcnlMZXZlbEl0ZW1Sb2IQDRIsCihJ",
            "dGVtQWNxdWlzaXRpb25TdGF0ZV9EcmFnb25CdWlsZFJlc291cmNlEA4SKgom",
            "SXRlbUFjcXVpc2l0aW9uU3RhdGVfRHJhZ29uRmFjdG9yeVRlY2gQDxIpCiVJ",
            "dGVtQWNxdWlzaXRpb25TdGF0ZV9HdWlsZEZhY3RvcnlJdGVtEBASJwojSXRl",
            "bUFjcXVpc2l0aW9uU3RhdGVfRmFzaGlvbkJUU0l0ZW0QESpICg9Jcm9uU291",
            "cmNlQUR5cGUSGAoUSXJvblNvdXJjZUFEeXBlX05PTkUQABIbChdJcm9uU291",
            "cmNlQUR5cGVfT2ZmTGluZRABKncKDUd1aWxkUG9zaXRpb24SGAoUR3VpbGRQ",
            "b3NpdGlvbl9Ob3JtYWwQABIXChNHdWlsZFBvc2l0aW9uX0VsaXRlEAESFgoS",
            "R3VpbGRQb3NpdGlvbl9WaWNlEAISGwoXR3VpbGRQb3NpdGlvbl9QcmVzaWRl",
            "bnQQAyraBAoIR3VpbGRPcHQSEQoNR3VpbGRPcHRfTm9uZRAAEhUKEUd1aWxk",
            "T3B0X0VkaXRJY29uEAESFQoRR3VpbGRPcHRfRWRpdE5hbWUQAhIXChNHdWls",
            "ZE9wdF9FZGl0Tm90aWNlEAMSFgoSR3VpbGRPcHRfRWRpdEFwcGx5EAQSGQoV",
            "R3VpbGRPcHRfQXBwbHlNZ3JMaXN0EAUSGgoWR3VpbGRPcHRfQXBwbHlNZ3JB",
            "Z3JlZRAGEhsKF0d1aWxkT3B0X0FwcGx5TWdyUmVmdXNlEAcSGgoWR3VpbGRP",
            "cHRfTWVtYmVyTWdyTGlzdBAIEh8KG0d1aWxkT3B0X01lbWJlck1nclByZXNp",
            "ZGVudBAJEhoKFkd1aWxkT3B0X01lbWJlck1nclZpY2UQChIbChdHdWlsZE9w",
            "dF9NZW1iZXJNZ3JFbGl0ZRALEhwKGEd1aWxkT3B0X01lbWJlck1nck5vcm1h",
            "bBAMEhoKFkd1aWxkT3B0X01lbWJlck1ncktpY2sQDRIRCg1HdWlsZE9wdF9R",
            "dWl0EA4SFAoQR3VpbGRPcHRfRGlzbWlzcxAPEhQKEEd1aWxkT3B0X0ltcGVh",
            "Y2gQEBIcChhHdWlsZE9wdF9TZW5kV29ybGRJbnZpdGUQERIdChlHdWlsZE9w",
            "dF9TZW5kUGxheWVySW52aXRlEBISHQoZR3VpbGRPcHRfQmFyZ2FpbmluZ05v",
            "dGljZRATEh0KGUd1aWxkT3B0X0VkaXRBbm5vdW5jZW1lbnQQFBIeChpHdWls",
            "ZE9wdF9BcHBseU1nclJlamVjdEFsbBAVKpoCCgxHdWlsZExvZ1R5cGUSFgoS",
            "R3VpbGRMb2dUeXBlX0xlYXZlEAASFQoRR3VpbGRMb2dUeXBlX0pvaW4QARIZ",
            "ChVHdWlsZExvZ1R5cGVfSW1wZWFjaDEQAhIZChVHdWlsZExvZ1R5cGVfSW1w",
            "ZWFjaDIQAxIYChRHdWlsZExvZ1R5cGVfTWdyVmljZRAEEhoKFkd1aWxkTG9n",
            "VHlwZV9NZ3JOb3JtYWwQBRIXChNHdWlsZExvZ1R5cGVfRG9uYXRlEAYSGgoW",
            "R3VpbGRMb2dUeXBlX1ByZXNpZGVudBAHEhwKGEd1aWxkTG9nVHlwZV9BdXRv",
            "Q2hhbmdlMRAIEhwKGEd1aWxkTG9nVHlwZV9BdXRvQ2hhbmdlMhAJKrYCCg9H",
            "dWlsZFVwZGF0ZVR5cGUSGwoXR3VpbGRVcGRhdGVUeXBlX1VOS05PV04QABIg",
            "ChxHdWlsZFVwZGF0ZVR5cGVfSk9JTkVEX0dVSUxEEAESHgoaR3VpbGRVcGRh",
            "dGVUeXBlX0xFRlRfR1VJTEQQAhIlCiFHdWlsZFVwZGF0ZVR5cGVfS0lDS0VE",
            "X0ZST01fR1VJTEQQAxIjCh9HdWlsZFVwZGF0ZVR5cGVfR1VJTERfRElTTUlT",
            "U0VEEAQSJAogR3VpbGRVcGRhdGVUeXBlX1BPU0lUSU9OX0NIQU5HRUQQBRIi",
            "Ch5HdWlsZFVwZGF0ZVR5cGVfR1VJTERfTEVWRUxfVVAQBhIuCipHdWlsZFVw",
            "ZGF0ZVR5cGVfQVBQTFlfUFJPQ0VTU0VEX09SX0VYUElSRUQQByq6AQoWR3Vp",
            "bGRBcHBsaWNhdGlvblN0YXR1cxIeChpBUFBMSUNBVElPTl9TVEFUVVNfVU5L",
            "Tk9XThAAEh4KGkFQUExJQ0FUSU9OX1NUQVRVU19QRU5ESU5HEAESHwobQVBQ",
            "TElDQVRJT05fU1RBVFVTX0FQUFJPVkVEEAISHwobQVBQTElDQVRJT05fU1RB",
            "VFVTX1JFSkVDVEVEEAMSHgoaQVBQTElDQVRJT05fU1RBVFVTX0VYUElSRUQQ",
            "BCqaAQodR3VpbGRTeXN0ZW1JbnRlcm5hbEFjdGlvblR5cGUSGwoXSU5URVJO",
            "QUxfQUNUSU9OX1VOS05PV04QABIpCiVJTlRFUk5BTF9BQ1RJT05fRElTTUlT",
            "U19HVUlMRF9NRU1CRVJTEAESMQotSU5URVJOQUxfQUNUSU9OX0dVSUxEX0xF",
            "VkVMX1VQX05PVElGWV9NRU1CRVJTEAIqSAoIQ2hhdFR5cGUSEgoOQ2hhdFR5",
            "cGVfV29ybGQQABISCg5DaGF0VHlwZV9HdWlsZBABEhQKEENoYXRUeXBlX1By",
            "aXZhdGUQAipYCgtDaGF0TXNnVHlwZRIUChBDaGF0TXNnVHlwZV9UZXh0EAAS",
            "GwoXQ2hhdE1zZ1R5cGVfR3VpbGRJbnZpdGUQARIWChJDaGF0TXNnVHlwZV9T",
            "eXN0ZW0QAipgChZJbkJhdHRsZVNob3dXaW5kb3dUeXBlEhcKE1VJX1NlbGVj",
            "dF9Ob3JtYWxQb3AQABIVChFVSV9TZWxlY3RfU2hvcFBvcBABEhYKElVJX1Nl",
            "bGVjdF9MdWNreVBvcBACKtIBChhHbG9iYWxCcm9hZGNhc3RFdmVudFR5cGUS",
            "IQodR2xvYmFsQnJvYWRjYXN0RXZlbnRUeXBlX05vbmUQABIzCi9HbG9iYWxC",
            "cm9hZGNhc3RFdmVudFR5cGVfQmFuUGxheWVyR3VpbGRCb3NzUmFuaxABEioK",
            "Jkdsb2JhbEJyb2FkY2FzdEV2ZW50VHlwZV9DaGdQbGF5ZXJOYW1lEAISMgou",
            "R2xvYmFsQnJvYWRjYXN0RXZlbnRUeXBlX0NoZ0d1aWxkTmFtZUFuZE5vdGlj",
            "ZRADKkIKC1NpZ25JblN0YXRlEgwKCE5vU2lnbkluEAASEAoMU2lnbmVkSW5O",
            "b1JlEAESEwoPU2lnbmVkSW5BbmRSZWVkEAIqcQoRUGF5bWVudE1vZHVsZVR5",
            "cGUSCAoEU2hvcBAAEg8KC0ZpcnN0Q2hhcmdlEAESDwoLTW9udGhseUNhcmQQ",
            "AhIOCgpHcmFkZWRGdW5kEAMSEgoOTW9udGhseUNhcmROZXcQBBIMCghUaW1l",
            "U2hvcBAFKmEKDE1pc3Npb25TdGF0ZRIbChdNaXNzaW9uU3RhdGVfVW5maW5p",
            "c2hlZBAAEhkKFU1pc3Npb25TdGF0ZV9GaW5pc2hlZBABEhkKFU1pc3Npb25T",
            "dGF0ZV9SZWNlaXZlZBACKo4BCgtNaXNzaW9uVHlwZRIUChBNaXNzaW9uVHlw",
            "ZV9NYWluEAASFQoRTWlzc2lvblR5cGVfRGFpbHkQARIYChRNaXNzaW9uVHlw",
            "ZV9TZXZlbkRheRACEhsKF01pc3Npb25UeXBlX0FjaGlldmVtZW50EAMSGwoX",
            "TWlzc2lvblR5cGVfSGVhdmVubHlEYW8QBCpCChFEQkdhY2hhQm9udXNTdGF0",
            "ZRIKCgZOb1B1bGwQABIOCgpQdWxsZWROb1JlEAESEQoNUHVsbGVkQW5kUmVl",
            "ZBACKsMBChBTZXJ2ZXJSZXN1bHRDb2RlEhUKEVJlc3VsdENvZGVfU1VDQ0VT",
            "EAASFAoQUmVzdWx0Q29kZV9FcnJvchABEiIKHlJlc3VsdENvZGVfQ3VycmVu",
            "Y3lfTm90X0Vub3VnaBACEh4KGlJlc3VsdENvZGVfSXRlbV9Ob3RfRW5vdWdo",
            "EAMSGgoWUmVzdWx0Q29kZV9QQVJBTV9FUlJPUhAEEiIKHlJlc3VsdENvZGVf",
            "Q09ORklHX05PVF9DT05UQUlOUxAFKjQKEEZ1bmNQcmV2aWV3c3RhdGUSCAoE",
            "TG9jaxAAEgkKBUF3YXJkEAESCwoHQXdhcmRlZBACKlEKFVRyZWFzdXJlR2Fj",
            "aGFDb3N0VHlwZRISCg5DT1NUX1RZUEVfTk9ORRAAEhIKDkNPU1RfVFlQRV9J",
            "VEVNEAESEAoMQ09TVF9UWVBFX0FEEAJCNVonbGl0ZWZyYW1lL2ludGVybmFs",
            "L2NvbW1vbi9wcm90b3MvcHVibGljqgIJR2FtZS5Db3JlYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Game.Core.LoginByType), typeof(global::Game.Core.MoneyType), typeof(global::Game.Core.AwardItemType), typeof(global::Game.Core.FuncLockType), typeof(global::Game.Core.SceneDropType), typeof(global::Game.Core.RedDotType), typeof(global::Game.Core.EGenderType), typeof(global::Game.Core.AttributeType), typeof(global::Game.Core.MonsterType), typeof(global::Game.Core.BattleType), typeof(global::Game.Core.BattleState), typeof(global::Game.Core.InstanceState), typeof(global::Game.Core.PlayerState), typeof(global::Game.Core.AdRewardType), typeof(global::Game.Core.GachaType), typeof(global::Game.Core.EquipType), typeof(global::Game.Core.DropExpressType), typeof(global::Game.Core.QualityType), typeof(global::Game.Core.MailType), typeof(global::Game.Core.MailStateType), typeof(global::Game.Core.MailReasonType), typeof(global::Game.Core.GuideTriggerType), typeof(global::Game.Core.NewGuideTriggerType), typeof(global::Game.Core.NewGuideFunType), typeof(global::Game.Core.SignInToType), typeof(global::Game.Core.RewardStatus), typeof(global::Game.Core.RewardType), typeof(global::Game.Core.ItemType), typeof(global::Game.Core.ItemSubType_ChooseBox), typeof(global::Game.Core.ItemSubType_Drop), typeof(global::Game.Core.ItemSubType_Devris), typeof(global::Game.Core.BagItemType), typeof(global::Game.Core.SystemShowErrorType), typeof(global::Game.Core.GiftPacksType), typeof(global::Game.Core.GiftPacksBuyType), typeof(global::Game.Core.ChargeType), typeof(global::Game.Core.CommonBoxRewardType), typeof(global::Game.Core.BattlePassType), typeof(global::Game.Core.BattlePassMissionType), typeof(global::Game.Core.AwardState), typeof(global::Game.Core.ActivityType), typeof(global::Game.Core.ActivityStatus), typeof(global::Game.Core.ItemAcquisitionState), typeof(global::Game.Core.IronSourceADype), typeof(global::Game.Core.GuildPosition), typeof(global::Game.Core.GuildOpt), typeof(global::Game.Core.GuildLogType), typeof(global::Game.Core.GuildUpdateType), typeof(global::Game.Core.GuildApplicationStatus), typeof(global::Game.Core.GuildSystemInternalActionType), typeof(global::Game.Core.ChatType), typeof(global::Game.Core.ChatMsgType), typeof(global::Game.Core.InBattleShowWindowType), typeof(global::Game.Core.GlobalBroadcastEventType), typeof(global::Game.Core.SignInState), typeof(global::Game.Core.PaymentModuleType), typeof(global::Game.Core.MissionState), typeof(global::Game.Core.MissionType), typeof(global::Game.Core.DBGachaBonusState), typeof(global::Game.Core.ServerResultCode), typeof(global::Game.Core.FuncPreviewstate), typeof(global::Game.Core.TreasureGachaCostType), }, null, null));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  ///=====================平台能力开始================================
  /// </summary>
  public enum LoginByType {
    [pbr::OriginalName("LoginByType_Other")] Other = 0,
    [pbr::OriginalName("LoginByType_QQGameCenter")] QqgameCenter = 1,
    [pbr::OriginalName("LoginByType_WeChatGameCenter")] WeChatGameCenter = 2,
  }

  /// <summary>
  ///货币类型
  /// </summary>
  public enum MoneyType {
    /// <summary>
    ///未知的类型
    /// </summary>
    [pbr::OriginalName("MoneyType_None")] None = 0,
    /// <summary>
    ///金币
    /// </summary>
    [pbr::OriginalName("MoneyType_Gold")] Gold = 1,
    /// <summary>
    ///钻石
    /// </summary>
    [pbr::OriginalName("MoneyType_Diamond")] Diamond = 2,
    /// <summary>
    ///体力
    /// </summary>
    [pbr::OriginalName("MoneyType_Power")] Power = 3,
    /// <summary>
    ///经验
    /// </summary>
    [pbr::OriginalName("MoneyType_Exp")] Exp = 4,
  }

  /// <summary>
  ///掉落类型
  /// </summary>
  public enum AwardItemType {
    /// <summary>
    ///货币
    /// </summary>
    [pbr::OriginalName("AwardItemType_Money")] Money = 0,
    /// <summary>
    ///普通物品
    /// </summary>
    [pbr::OriginalName("AwardItemType_Item")] Item = 1,
    /// <summary>
    ///装备
    /// </summary>
    [pbr::OriginalName("AwardItemType_Equip")] Equip = 2,
    /// <summary>
    ///技能
    /// </summary>
    [pbr::OriginalName("AwardItemType_Skill")] Skill = 3,
    /// <summary>
    ///宝石
    /// </summary>
    [pbr::OriginalName("AwardItemType_Gem")] Gem = 4,
  }

  /// <summary>
  ///功能解锁状态
  /// </summary>
  public enum FuncLockType {
    /// <summary>
    ///未解锁
    /// </summary>
    [pbr::OriginalName("FuncLockType_Lock")] Lock = 0,
    /// <summary>
    ///解锁
    /// </summary>
    [pbr::OriginalName("FuncLockType_UnLock")] UnLock = 1,
    /// <summary>
    ///关闭
    /// </summary>
    [pbr::OriginalName("FuncLockType_Close")] Close = 2,
  }

  /// <summary>
  ///场景掉落类型
  /// </summary>
  public enum SceneDropType {
    /// <summary>
    ///小怪
    /// </summary>
    [pbr::OriginalName("SceneDropType_Monster")] Monster = 0,
    /// <summary>
    ///彩虹马
    /// </summary>
    [pbr::OriginalName("SceneDropType_Horse")] Horse = 1,
  }

  /// <summary>
  ///红点枚举
  /// </summary>
  public enum RedDotType {
    /// <summary>
    /// </summary>
    [pbr::OriginalName("RedDotType_None")] None = 0,
    /// <summary>
    ///技能红点		
    /// </summary>
    [pbr::OriginalName("RedDotType_Skill")] Skill = 1,
    /// <summary>
    ///邮件红点		
    /// </summary>
    [pbr::OriginalName("RedDotType_Mail")] Mail = 2,
    /// <summary>
    ///好友红点
    /// </summary>
    [pbr::OriginalName("RedDotType_Friend")] Friend = 3,
    /// <summary>
    ///功能预告
    /// </summary>
    [pbr::OriginalName("RedDotType_FuncPreview")] FuncPreview = 4,
    /// <summary>
    ///天道修为
    /// </summary>
    [pbr::OriginalName("RedDotType_HeavenlyDao")] HeavenlyDao = 5,
    /// <summary>
    ///每日特惠礼包
    /// </summary>
    [pbr::OriginalName("RedDotType_DailyPlusGift")] DailyPlusGift = 6,
    /// <summary>
    ///背包红点
    /// </summary>
    [pbr::OriginalName("RedDotType_Bag")] Bag = 7,
    /// <summary>
    ///放在最后
    /// </summary>
    [pbr::OriginalName("RedDotType_MAX")] Max = 8,
  }

  /// <summary>
  ///性别枚举
  /// </summary>
  public enum EGenderType {
    /// <summary>
    ///神秘
    /// </summary>
    [pbr::OriginalName("Default")] Default = 0,
    /// <summary>
    ///男性
    /// </summary>
    [pbr::OriginalName("Man")] Man = 1,
    /// <summary>
    ///女性
    /// </summary>
    [pbr::OriginalName("Woman")] Woman = 2,
  }

  /// <summary>
  ///属性
  /// </summary>
  public enum AttributeType {
    /// <summary>
    ///空
    /// </summary>
    [pbr::OriginalName("AttributeType_NONE")] None = 0,
    /// <summary>
    ///基础攻击力
    /// </summary>
    [pbr::OriginalName("AttributeType_Attack")] Attack = 1,
    /// <summary>
    ///血量
    /// </summary>
    [pbr::OriginalName("AttributeType_Hp")] Hp = 2,
    /// <summary>
    ///基础防御
    /// </summary>
    [pbr::OriginalName("AttributeType_Def")] Def = 3,
    /// <summary>
    ///移速
    /// </summary>
    [pbr::OriginalName("AttributeType_MoveSpeed")] MoveSpeed = 4,
    /// <summary>
    ///枪械攻击力
    /// </summary>
    [pbr::OriginalName("AttributeType_GunAtk")] GunAtk = 5,
    /// <summary>
    ///物理攻击力
    /// </summary>
    [pbr::OriginalName("AttributeType_PhysicsAtk")] PhysicsAtk = 6,
    /// <summary>
    ///冰攻击力
    /// </summary>
    [pbr::OriginalName("AttributeType_IceAtk")] IceAtk = 7,
    /// <summary>
    ///电攻击力
    /// </summary>
    [pbr::OriginalName("AttributeType_ElectricityAtk")] ElectricityAtk = 8,
    /// <summary>
    ///火攻击力
    /// </summary>
    [pbr::OriginalName("AttributeType_FireAtk")] FireAtk = 9,
    /// <summary>
    ///能量攻击力
    /// </summary>
    [pbr::OriginalName("AttributeType_EnergyAtk")] EnergyAtk = 10,
    /// <summary>
    ///风攻击力
    /// </summary>
    [pbr::OriginalName("AttributeType_WindAtk")] WindAtk = 11,
    /// <summary>
    ///穿透值
    /// </summary>
    [pbr::OriginalName("AttributeType_Penetrate")] Penetrate = 12,
    /// <summary>
    ///命中
    /// </summary>
    [pbr::OriginalName("AttributeType_HitPre")] HitPre = 20,
    /// <summary>
    ///闪避
    /// </summary>
    [pbr::OriginalName("AttributeType_DodgePre")] DodgePre = 21,
    /// <summary>
    ///基础攻击百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_AttackPre")] AttackPre = 22,
    /// <summary>
    ///血量百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_HpPre")] HpPre = 23,
    /// <summary>
    ///基础抗百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_DefPre")] DefPre = 24,
    /// <summary>
    ///移动速度百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_MoveSpeedPre")] MoveSpeedPre = 25,
    /// <summary>
    ///暴击率
    /// </summary>
    [pbr::OriginalName("AttributeType_CriticalPre")] CriticalPre = 26,
    /// <summary>
    ///暴击倍数
    /// </summary>
    [pbr::OriginalName("AttributeType_CriticalMultiplePre")] CriticalMultiplePre = 27,
    /// <summary>
    ///暴击抵抗
    /// </summary>
    [pbr::OriginalName("AttributeType_CriticalResistPre")] CriticalResistPre = 28,
    /// <summary>
    ///输出伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_BaseDamagePre")] BaseDamagePre = 29,
    /// <summary>
    ///枪械伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_GunDamagePre")] GunDamagePre = 30,
    /// <summary>
    ///物理伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_PhysicsDamagePre")] PhysicsDamagePre = 31,
    /// <summary>
    ///冰伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_IceDamagePre")] IceDamagePre = 32,
    /// <summary>
    ///电伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_ElectricityDamagePre")] ElectricityDamagePre = 33,
    /// <summary>
    ///火伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_FireDamagePre")] FireDamagePre = 34,
    /// <summary>
    ///能量伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_EnergyDamagePre")] EnergyDamagePre = 35,
    /// <summary>
    ///风伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_WindDamagePre")] WindDamagePre = 36,
    /// <summary>
    ///穿透值百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_PenetratePre")] PenetratePre = 37,
    /// <summary>
    ///枪械抵抗
    /// </summary>
    [pbr::OriginalName("AttributeType_GunDefPre")] GunDefPre = 38,
    /// <summary>
    ///物理抵抗
    /// </summary>
    [pbr::OriginalName("AttributeType_PhysicsDefPre")] PhysicsDefPre = 39,
    /// <summary>
    ///冰抵抗
    /// </summary>
    [pbr::OriginalName("AttributeType_IceDefPre")] IceDefPre = 40,
    /// <summary>
    ///电抵抗百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_ElectricityDefPre")] ElectricityDefPre = 41,
    /// <summary>
    ///火抵抗百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_FireDefPre")] FireDefPre = 42,
    /// <summary>
    ///能量抵抗百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_EnergyDefPre")] EnergyDefPre = 43,
    /// <summary>
    ///风抵抗百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_WindDefPre")] WindDefPre = 44,
    /// <summary>
    ///地面单位伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_GroundDemageAddPre")] GroundDemageAddPre = 45,
    /// <summary>
    ///空中单位伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_MidairDamageAddPre")] MidairDamageAddPre = 46,
    /// <summary>
    ///近战单位伤害增加
    /// </summary>
    [pbr::OriginalName("AttributeType_NearDamageAddPre")] NearDamageAddPre = 47,
    /// <summary>
    ///远程单位伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_LongDisDamageAddPre")] LongDisDamageAddPre = 48,
    /// <summary>
    ///小怪的伤害加成
    /// </summary>
    [pbr::OriginalName("AttributeType_SmallMonsterDamageAddPre")] SmallMonsterDamageAddPre = 49,
    /// <summary>
    ///精英伤害加成
    /// </summary>
    [pbr::OriginalName("AttributeType_EliteMonsterDamageAddPre")] EliteMonsterDamageAddPre = 50,
    /// <summary>
    ///Boss伤害加成
    /// </summary>
    [pbr::OriginalName("AttributeType_BossMonsterDamageAddPre")] BossMonsterDamageAddPre = 51,
    /// <summary>
    ///减少枪械CD
    /// </summary>
    [pbr::OriginalName("AttributeType_DecreaseGunCD")] DecreaseGunCd = 52,
    /// <summary>
    ///减少技能CD
    /// </summary>
    [pbr::OriginalName("AttributeType_DecreaseSkillCD")] DecreaseSkillCd = 53,
    /// <summary>
    ///金币加成
    /// </summary>
    [pbr::OriginalName("AttributeType_GoldBonusPre")] GoldBonusPre = 80,
    /// <summary>
    ///经验加成
    /// </summary>
    [pbr::OriginalName("AttributeType_ExpBonusPre")] ExpBonusPre = 81,
    /// <summary>
    ///战币加成
    /// </summary>
    [pbr::OriginalName("AttributeType_WarCoinPre")] WarCoinPre = 82,
    /// <summary>
    ///弹夹数量
    /// </summary>
    [pbr::OriginalName("AttributeType_Magazine")] Magazine = 83,
    /// <summary>
    ///弹夹数量百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_MagazinePre")] MagazinePre = 84,
  }

  /// <summary>
  ///怪物的类型
  /// </summary>
  public enum MonsterType {
    /// <summary>
    ///无
    /// </summary>
    [pbr::OriginalName("MonsterType_None")] None = 0,
    /// <summary>
    ///小怪
    /// </summary>
    [pbr::OriginalName("MonsterType_Small")] Small = 1,
    /// <summary>
    ///精英
    /// </summary>
    [pbr::OriginalName("MonsterType_Advanced")] Advanced = 2,
    /// <summary>
    ///Boss
    /// </summary>
    [pbr::OriginalName("MonsterType_Boss")] Boss = 3,
  }

  /// <summary>
  ///战斗的类型或是副本类型
  /// </summary>
  public enum BattleType {
    /// <summary>
    ///主线的关卡
    /// </summary>
    [pbr::OriginalName("BattleType_Common")] Common = 0,
    /// <summary>
    ///主界面站桩的副本信息
    /// </summary>
    [pbr::OriginalName("BattleType_StandingPile")] StandingPile = 100,
  }

  /// <summary>
  /// 战斗状态枚举
  /// </summary>
  public enum BattleState {
    [pbr::OriginalName("STATE_NONE")] StateNone = 0,
    /// <summary>
    /// 回合开始（匹配对手）
    /// </summary>
    [pbr::OriginalName("STATE_ROUND_START")] StateRoundStart = 1,
    /// <summary>
    /// 准备阶段（包含buff选择、英雄生成、自由操作）
    /// </summary>
    [pbr::OriginalName("STATE_PREPARATION")] StatePreparation = 2,
    /// <summary>
    /// 战斗开始（所有玩家准备完毕或超时）
    /// </summary>
    [pbr::OriginalName("STATE_BATTLE_STARTING")] StateBattleStarting = 3,
    /// <summary>
    /// 战斗进行中（两场战斗都在进行）
    /// </summary>
    [pbr::OriginalName("STATE_BATTLE_IN_PROGRESS")] StateBattleInProgress = 4,
    /// <summary>
    /// 回合结算（两场战斗都结束）
    /// </summary>
    [pbr::OriginalName("STATE_ROUND_SETTLEMENT")] StateRoundSettlement = 5,
    /// <summary>
    /// 淘汰检查
    /// </summary>
    [pbr::OriginalName("STATE_ELIMINATION_CHECK")] StateEliminationCheck = 6,
    /// <summary>
    /// 游戏结束
    /// </summary>
    [pbr::OriginalName("STATE_GAME_OVER")] StateGameOver = 7,
  }

  public enum InstanceState {
    /// <summary>
    /// 等待战斗开始
    /// </summary>
    [pbr::OriginalName("INSTANCE_WAITING")] InstanceWaiting = 0,
    /// <summary>
    /// 战斗进行中
    /// </summary>
    [pbr::OriginalName("INSTANCE_BATTLE_IN_PROGRESS")] InstanceBattleInProgress = 1,
    /// <summary>
    /// 战斗结束，等待另一场
    /// </summary>
    [pbr::OriginalName("INSTANCE_BATTLE_FINISHED")] InstanceBattleFinished = 2,
    /// <summary>
    /// 结算中
    /// </summary>
    [pbr::OriginalName("INSTANCE_SETTLEMENT")] InstanceSettlement = 3,
  }

  public enum PlayerState {
    /// <summary>
    /// 等待中
    /// </summary>
    [pbr::OriginalName("PLAYER_WAITING")] PlayerWaiting = 0,
    /// <summary>
    /// 选择Buff中
    /// </summary>
    [pbr::OriginalName("PLAYER_SELECTING_BUFF")] PlayerSelectingBuff = 1,
    /// <summary>
    /// 接收英雄中
    /// </summary>
    [pbr::OriginalName("PLAYER_RECEIVING_HEROES")] PlayerReceivingHeroes = 2,
    /// <summary>
    /// 自由操作中
    /// </summary>
    [pbr::OriginalName("PLAYER_FREE_OPERATION")] PlayerFreeOperation = 3,
    /// <summary>
    /// 已准备
    /// </summary>
    [pbr::OriginalName("PLAYER_READY")] PlayerReady = 4,
    /// <summary>
    /// 战斗中
    /// </summary>
    [pbr::OriginalName("PLAYER_IN_BATTLE")] PlayerInBattle = 5,
    /// <summary>
    /// 已淘汰
    /// </summary>
    [pbr::OriginalName("PLAYER_ELIMINATED")] PlayerEliminated = 6,
  }

  /// <summary>
  /// 战斗结算额外奖励机制
  /// </summary>
  public enum AdRewardType {
    [pbr::OriginalName("BATTLE_ADREWARD_NONE")] BattleAdrewardNone = 0,
    /// <summary>
    /// 对战补给
    /// </summary>
    [pbr::OriginalName("BATTLE_SUPPLY_DROP")] BattleSupplyDrop = 1,
    /// <summary>
    /// 对战庇佑
    /// </summary>
    [pbr::OriginalName("BATTLE_BLESSING")] BattleBlessing = 2,
  }

  /// <summary>
  ///======================战斗相关结束================================
  ///抽卡类型
  /// </summary>
  public enum GachaType {
    /// <summary>
    ///装备
    /// </summary>
    [pbr::OriginalName("GachaType_Equip")] Equip = 0,
    /// <summary>
    ///技能
    /// </summary>
    [pbr::OriginalName("GachaType_Skill")] Skill = 1,
    /// <summary>
    ///圣物
    /// </summary>
    [pbr::OriginalName("GachaType_Hallows")] Hallows = 2,
    /// <summary>
    ///宠物
    /// </summary>
    [pbr::OriginalName("GachaType_Pets")] Pets = 3,
  }

  /// <summary>
  ///装备类型
  /// </summary>
  public enum EquipType {
    /// <summary>
    ///占位
    /// </summary>
    [pbr::OriginalName("Equip_NONE")] EquipNone = 0,
    /// <summary>
    ///护臂
    /// </summary>
    [pbr::OriginalName("Equip_Bracer")] EquipBracer = 1,
    /// <summary>
    ///头盔
    /// </summary>
    [pbr::OriginalName("Equip_Helmet")] EquipHelmet = 2,
    /// <summary>
    ///鞋子
    /// </summary>
    [pbr::OriginalName("Equip_Shoes")] EquipShoes = 3,
    /// <summary>
    ///裤子
    /// </summary>
    [pbr::OriginalName("Equip_Pants")] EquipPants = 4,
    /// <summary>
    /// 手套
    /// </summary>
    [pbr::OriginalName("Equip_Gloves")] EquipGloves = 5,
    /// <summary>
    /// 衣服
    /// </summary>
    [pbr::OriginalName("Equip_Clothes")] EquipClothes = 6,
  }

  /// <summary>
  ///大数值公式类型
  /// </summary>
  public enum DropExpressType {
    /// <summary>
    ///普通计算公式（只返回base值）
    /// </summary>
    [pbr::OriginalName("Express_Normal")] ExpressNormal = 0,
    /// <summary>
    ///关卡计算公式
    /// </summary>
    [pbr::OriginalName("Express_Stages")] ExpressStages = 1,
  }

  /// <summary>
  ///统一品质类型
  /// </summary>
  public enum QualityType {
    /// <summary>
    ///灰
    /// </summary>
    [pbr::OriginalName("Quality_Grey")] QualityGrey = 0,
    /// <summary>
    ///绿
    /// </summary>
    [pbr::OriginalName("Quality_Green")] QualityGreen = 1,
    /// <summary>
    ///蓝
    /// </summary>
    [pbr::OriginalName("Quality_Blue")] QualityBlue = 2,
    /// <summary>
    ///紫
    /// </summary>
    [pbr::OriginalName("Quality_Purple")] QualityPurple = 3,
    /// <summary>
    ///金
    /// </summary>
    [pbr::OriginalName("Quality_Gold")] QualityGold = 4,
    /// <summary>
    ///橙
    /// </summary>
    [pbr::OriginalName("Quality_Orange")] QualityOrange = 5,
    /// <summary>
    ///红
    /// </summary>
    [pbr::OriginalName("Quality_Red")] QualityRed = 6,
    /// <summary>
    ///深红
    /// </summary>
    [pbr::OriginalName("Quality_DeepRed")] QualityDeepRed = 7,
    /// <summary>
    ///预留
    /// </summary>
    [pbr::OriginalName("Quality_Args8")] QualityArgs8 = 8,
  }

  /// <summary>
  ///邮件的类型
  /// </summary>
  public enum MailType {
    /// <summary>
    ///非奖励类型邮件
    /// </summary>
    [pbr::OriginalName("MailType_NonReward")] NonReward = 0,
    /// <summary>
    /// 有奖励邮件
    /// </summary>
    [pbr::OriginalName("MailType_Reward")] Reward = 1,
    /// <summary>
    ///通知奖励类型邮件
    /// </summary>
    [pbr::OriginalName("MailType_NotifyReward")] NotifyReward = 2,
    /// <summary>
    ///玩家权限控制刷新-全
    /// </summary>
    [pbr::OriginalName("MailType_PlayerAuth")] PlayerAuth = 3,
    /// <summary>
    ///玩家权限控制刷新-单或组
    /// </summary>
    [pbr::OriginalName("MailType_PlayerAuthInfo")] PlayerAuthInfo = 4,
    /// <summary>
    ///删除玩家身上的多个邮件
    /// </summary>
    [pbr::OriginalName("MailType_PlayerMailRemove")] PlayerMailRemove = 5,
    /// <summary>
    ///道具直购（所有付费都走邮件发放）
    /// </summary>
    [pbr::OriginalName("MailType_GiftRMB")] GiftRmb = 6,
    /// <summary>
    ///GlobalServer发送的玩家个人邮件
    /// </summary>
    [pbr::OriginalName("MailType_GlobalActivityPlayer")] GlobalActivityPlayer = 7,
    /// <summary>
    ///后台广告回调邮件
    /// </summary>
    [pbr::OriginalName("MailType_AD")] Ad = 8,
    /// <summary>
    ///公会操作时对离线玩家通知（写入离线数据）
    /// </summary>
    [pbr::OriginalName("MailType_GuildOfflineMsg")] GuildOfflineMsg = 9,
    /// <summary>
    ///问卷奖励
    /// </summary>
    [pbr::OriginalName("MailType_QuestAward")] QuestAward = 10,
    /// <summary>
    ///开服活动结束
    /// </summary>
    [pbr::OriginalName("MailType_Activity_OpenServer_End")] ActivityOpenServerEnd = 11,
  }

  /// <summary>
  ///邮件的打开类型
  /// </summary>
  public enum MailStateType {
    /// <summary>
    ///未读
    /// </summary>
    [pbr::OriginalName("MailStateType_UnRead")] UnRead = 0,
    /// <summary>
    ///已读
    /// </summary>
    [pbr::OriginalName("MailStateType_Read")] Read = 1,
    /// <summary>
    ///已领取
    /// </summary>
    [pbr::OriginalName("MailStateType_GotAward")] GotAward = 2,
  }

  /// <summary>
  ///邮件的打开类型
  /// </summary>
  public enum MailReasonType {
    /// <summary>
    /// 测试
    /// </summary>
    [pbr::OriginalName("MailReasonType_TestGM")] TestGm = 0,
    /// <summary>
    ///GM管理员
    /// </summary>
    [pbr::OriginalName("MailReasonType_GMRemote")] Gmremote = 1,
    /// <summary>
    ///系统邮件
    /// </summary>
    [pbr::OriginalName("MailReasonType_System")] System = 2,
  }

  /// <summary>
  ///旧版新手引导触发类型
  /// </summary>
  public enum GuideTriggerType {
    [pbr::OriginalName("None")] None = 0,
    [pbr::OriginalName("NewPlayer")] NewPlayer = 1,
    [pbr::OriginalName("OpenUi")] OpenUi = 2,
    [pbr::OriginalName("CloseUi")] CloseUi = 3,
    [pbr::OriginalName("UnlockFuncId")] UnlockFuncId = 4,
    [pbr::OriginalName("PassedMainLine")] PassedMainLine = 5,
    [pbr::OriginalName("FinishGuide")] FinishGuide = 6,
  }

  /// <summary>
  ///新手引导的触发类型
  /// </summary>
  public enum NewGuideTriggerType {
    /// <summary>
    ///无效的触发
    /// </summary>
    [pbr::OriginalName("NewGuideTriggerType_Normal")] Normal = 0,
    /// <summary>
    ///领取主线完成任务
    /// </summary>
    [pbr::OriginalName("NewGuideTriggerType_GetCompleteTask")] GetCompleteTask = 1,
    /// <summary>
    ///完成任务
    /// </summary>
    [pbr::OriginalName("NewGuideTriggerType_MissionFinish")] MissionFinish = 2,
    /// <summary>
    ///新手结束触发下一个新手
    /// </summary>
    [pbr::OriginalName("NewGuideTriggerType_NewGuildEnd")] NewGuildEnd = 3,
    /// <summary>
    ///金币 经验 主线 成功结束
    /// </summary>
    [pbr::OriginalName("NewGuideTriggerType_InstanceEnd")] InstanceEnd = 4,
    /// <summary>
    ///起名
    /// </summary>
    [pbr::OriginalName("NewGuideTriggerType_CreateName")] CreateName = 5,
    /// <summary>
    ///玩家首次挑战合作舞台挑战胜利触发引导
    /// </summary>
    [pbr::OriginalName("NewGuideTriggerType_CooperationStage")] CooperationStage = 6,
    /// <summary>
    ///抽卡结束
    /// </summary>
    [pbr::OriginalName("NewGuideTriggerType_LotteryEnd")] LotteryEnd = 7,
  }

  /// <summary>
  ///新手引导的功能类型
  /// </summary>
  public enum NewGuideFunType {
    /// <summary>
    ///无效的触发
    /// </summary>
    [pbr::OriginalName("NewGuideFunType_Normal")] Normal = 0,
    /// <summary>
    ///起名
    /// </summary>
    [pbr::OriginalName("NewGuideFunType_ChangeName")] ChangeName = 1,
  }

  /// <summary>
  ///签到的类型
  /// </summary>
  public enum SignInToType {
    /// <summary>
    ///未知的类型
    /// </summary>
    [pbr::OriginalName("SignInToType_None")] None = 0,
    /// <summary>
    ///七天签到
    /// </summary>
    [pbr::OriginalName("SignInToType_SevenDay")] SevenDay = 1,
    /// <summary>
    ///每天签到
    /// </summary>
    [pbr::OriginalName("SignInToType_EveryDay")] EveryDay = 2,
    /// <summary>
    ///每月阶段奖励
    /// </summary>
    [pbr::OriginalName("SignInToType_MonthStage")] MonthStage = 3,
  }

  /// <summary>
  ///奖励状态
  /// </summary>
  public enum RewardStatus {
    /// <summary>
    ///未完成
    /// </summary>
    [pbr::OriginalName("RewardStatus_Doing")] Doing = 0,
    /// <summary>
    ///已完成
    /// </summary>
    [pbr::OriginalName("RewardStatus_Finish")] Finish = 1,
    /// <summary>
    ///已领奖
    /// </summary>
    [pbr::OriginalName("RewardStatus_Received")] Received = 2,
  }

  /// <summary>
  ///奖励类型
  /// </summary>
  public enum RewardType {
    [pbr::OriginalName("REWARD_TYPE_UNKNOWN")] Unknown = 0,
    /// <summary>
    /// 普通段位奖励
    /// </summary>
    [pbr::OriginalName("REWARD_TYPE_RANK")] Rank = 1,
    /// <summary>
    /// 赛季段位奖励
    /// </summary>
    [pbr::OriginalName("REWARD_TYPE_SEASON_RANK")] SeasonRank = 2,
  }

  /// <summary>
  ///道具类型
  /// </summary>
  public enum ItemType {
    /// <summary>
    ///不可使用类
    /// </summary>
    [pbr::OriginalName("Unavailable")] Unavailable = 0,
    /// <summary>
    ///掉落类型
    /// </summary>
    [pbr::OriginalName("Drop")] Drop = 1,
    /// <summary>
    ///挂机奖励类型
    /// </summary>
    [pbr::OriginalName("HangUpReward")] HangUpReward = 2,
    /// <summary>
    ///自选宝箱
    /// </summary>
    [pbr::OriginalName("ChooseBox")] ChooseBox = 3,
    /// <summary>
    ///碎片类型
    /// </summary>
    [pbr::OriginalName("Devris")] Devris = 4,
  }

  public enum ItemSubType_ChooseBox {
    /// <summary>
    ///只能选一个
    /// </summary>
    [pbr::OriginalName("ChooseBox_Once")] ChooseBoxOnce = 0,
    /// <summary>
    ///可批量多选
    /// </summary>
    [pbr::OriginalName("ChooseBox_Multi")] ChooseBoxMulti = 1,
  }

  /// <summary>
  ///道具子类型 (掉落类型)
  /// </summary>
  public enum ItemSubType_Drop {
    /// <summary>
    ///固定类型
    /// </summary>
    [pbr::OriginalName("Drop_Fixed")] DropFixed = 0,
    /// <summary>
    ///随机类
    /// </summary>
    [pbr::OriginalName("Drop_Random")] DropRandom = 1,
  }

  /// <summary>
  ///道具子类型 (碎片类型)
  /// </summary>
  public enum ItemSubType_Devris {
    /// <summary>
    ///皮肤
    /// </summary>
    [pbr::OriginalName("Devris_Skin")] DevrisSkin = 0,
    /// <summary>
    ///枪械
    /// </summary>
    [pbr::OriginalName("Devris_Firearm")] DevrisFirearm = 1,
    /// <summary>
    ///城墙
    /// </summary>
    [pbr::OriginalName("Devris_Wall")] DevrisWall = 2,
    /// <summary>
    ///宠物
    /// </summary>
    [pbr::OriginalName("Devris_Pet")] DevrisPet = 3,
  }

  /// <summary>
  ///背包对象结构类型
  /// </summary>
  public enum BagItemType {
    /// <summary>
    ///常规物品类型
    /// </summary>
    [pbr::OriginalName("NormalItem")] NormalItem = 0,
  }

  /// <summary>
  ///系统提示
  /// </summary>
  public enum SystemShowErrorType {
    /// <summary>
    ///没有特殊需求的error
    /// </summary>
    [pbr::OriginalName("SystemShowErrorType_None")] None = 0,
    /// <summary>
    ///踢人
    /// </summary>
    [pbr::OriginalName("SystemShowErrorType_Kick")] Kick = 1,
    /// <summary>
    ///顶号
    /// </summary>
    [pbr::OriginalName("SystemShowErrorType_Replace")] Replace = 2,
    /// <summary>
    ///禁言
    /// </summary>
    [pbr::OriginalName("SystemShowErrorType_ChatBan")] ChatBan = 3,
    /// <summary>
    ///仅弹窗提示
    /// </summary>
    [pbr::OriginalName("SystemShowErrorType_Tips")] Tips = 4,
    /// <summary>
    ///登录
    /// </summary>
    [pbr::OriginalName("SystemShowErrorType_Login")] Login = 5,
  }

  /// <summary>
  ///礼包类型
  /// </summary>
  public enum GiftPacksType {
    [pbr::OriginalName("GiftPacksType_None")] None = 0,
    /// <summary>
    ///限时礼包
    /// </summary>
    [pbr::OriginalName("GiftPacksType_Limit")] Limit = 1,
    /// <summary>
    /// </summary>
    [pbr::OriginalName("GiftPacksType_Weekly")] Weekly = 2,
    /// <summary>
    ///每日特惠礼包
    /// </summary>
    [pbr::OriginalName("GiftPacksType_DailyPlus")] DailyPlus = 3,
    /// <summary>
    ///周卡礼包
    /// </summary>
    [pbr::OriginalName("GiftPacksType_WeekCard")] WeekCard = 4,
    /// <summary>
    ///推荐礼包
    /// </summary>
    [pbr::OriginalName("GiftPacksType_Recommend")] Recommend = 5,
  }

  /// <summary>
  ///礼包购买类型
  /// </summary>
  public enum GiftPacksBuyType {
    /// <summary>
    ///广告购买
    /// </summary>
    [pbr::OriginalName("GiftPacksBuyType_Adv")] Adv = 0,
    /// <summary>
    ///人民币购买
    /// </summary>
    [pbr::OriginalName("GiftPacksBuyType_Rmb")] Rmb = 1,
    /// <summary>
    ///免费领取
    /// </summary>
    [pbr::OriginalName("GiftPacksBuyType_Free")] Free = 2,
    /// <summary>
    ///钻石购买礼包
    /// </summary>
    [pbr::OriginalName("GiftPacksBuyType_Diamond")] Diamond = 3,
  }

  /// <summary>
  ///月卡类型
  /// </summary>
  public enum ChargeType {
    /// <summary>
    /// </summary>
    [pbr::OriginalName("ChargeType_None")] None = 0,
    /// <summary>
    ///钻石档位
    /// </summary>
    [pbr::OriginalName("ChargeType_Diamond")] Diamond = 1,
    /// <summary>
    ///普通
    /// </summary>
    [pbr::OriginalName("ChargeType_MonthCardNormal")] MonthCardNormal = 2,
    /// <summary>
    ///超级
    /// </summary>
    [pbr::OriginalName("ChargeType_MonthCardSuper")] MonthCardSuper = 3,
    /// <summary>
    ///道具直购
    /// </summary>
    [pbr::OriginalName("ChargeType_Item")] Item = 4,
  }

  /// <summary>
  ///通用阶段宝箱 特殊固定类别
  /// </summary>
  public enum CommonBoxRewardType {
    /// <summary>
    /// </summary>
    [pbr::OriginalName("CommonBoxReward_None")] CommonBoxRewardNone = 0,
    /// <summary>
    ///每日宝箱
    /// </summary>
    [pbr::OriginalName("CommonBoxReward_DailyMission")] CommonBoxRewardDailyMission = 1,
    /// <summary>
    ///七日任务活动宝箱
    /// </summary>
    [pbr::OriginalName("CommonBoxReward_SevenDayActivity")] CommonBoxRewardSevenDayActivity = 2,
  }

  /// <summary>
  ///=====================战令开始===================================
  /// 战令类型
  /// </summary>
  public enum BattlePassType {
    /// <summary>
    /// 未购买
    /// </summary>
    [pbr::OriginalName("BattlePassType_NONE")] None = 0,
    /// <summary>
    /// 基础战令
    /// </summary>
    [pbr::OriginalName("BattlePassType_BASE")] Base = 1,
    /// <summary>
    /// 进阶战令
    /// </summary>
    [pbr::OriginalName("BattlePassType_ADVANCE")] Advance = 2,
  }

  /// <summary>
  /// 战令任务类型
  /// </summary>
  public enum BattlePassMissionType {
    /// <summary>
    /// 非法
    /// </summary>
    [pbr::OriginalName("BattlePassMissionType_NONE")] None = 0,
    /// <summary>
    /// 每日
    /// </summary>
    [pbr::OriginalName("BattlePassMissionType_DAILY")] Daily = 1,
    /// <summary>
    /// 每周
    /// </summary>
    [pbr::OriginalName("BattlePassMissionType_WEEKLY")] Weekly = 2,
  }

  /// <summary>
  /// 战令奖励领取状态
  /// </summary>
  public enum AwardState {
    /// <summary>
    /// 未领取
    /// </summary>
    [pbr::OriginalName("AwardState_UNAVAILABLE")] Unavailable = 0,
    /// <summary>
    /// 可领取
    /// </summary>
    [pbr::OriginalName("AwardState_AVAILABLE")] Available = 1,
    /// <summary>
    /// 已领取
    /// </summary>
    [pbr::OriginalName("AwardState_RECEIVED")] Received = 2,
  }

  /// <summary>
  ///=====================战令结束===================================
  /// 活动类型
  /// </summary>
  public enum ActivityType {
    /// <summary>
    ///0.战令
    /// </summary>
    [pbr::OriginalName("ActivityType_BattlePass")] BattlePass = 0,
    /// <summary>
    ///1.七日任务（仅占位）
    /// </summary>
    [pbr::OriginalName("ActivityType_SevenTask")] SevenTask = 1,
    /// <summary>
    ///2.七日签到（仅占位）
    /// </summary>
    [pbr::OriginalName("ActivityType_SevenSign")] SevenSign = 2,
  }

  /// <summary>
  /// 活动状态
  /// </summary>
  public enum ActivityStatus {
    /// <summary>
    ///0.未开启
    /// </summary>
    [pbr::OriginalName("ActivityStatus_NO_OPEN")] NoOpen = 0,
    /// <summary>
    ///1.已开始（系统预处理阶段，服务器使用）
    /// </summary>
    [pbr::OriginalName("ActivityStatus_START")] Start = 1,
    /// <summary>
    ///3.已结束（客户端判断此状态为活动正式结束）
    /// </summary>
    [pbr::OriginalName("ActivityStatus_END")] End = 2,
  }

  /// <summary>
  ///=====================道具前往获取开始===================================
  /// </summary>
  public enum ItemAcquisitionState {
    /// <summary>
    /// 0.表示不可用
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_NONE")] None = 0,
    /// <summary>
    /// 钻石
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_Diamond")] Diamond = 1,
    /// <summary>
    /// 星星
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_Star")] Star = 2,
    /// <summary>
    /// 神之精华
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_Essence")] Essence = 3,
    /// <summary>
    /// 空气净化器
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_AirPurifier")] AirPurifier = 4,
    /// <summary>
    /// 建筑升级门票
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_UpgradeTicket")] UpgradeTicket = 5,
    /// <summary>
    /// 副本门票
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_DuplicateTicket")] DuplicateTicket = 6,
    /// <summary>
    /// 铲子
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_Shovel")] Shovel = 7,
    /// <summary>
    /// 龙蛋
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_DragonEgg")] DragonEgg = 8,
    /// <summary>
    /// 魔法布料
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_MagicCloth")] MagicCloth = 9,
    /// <summary>
    /// 凿子
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_Chisel")] Chisel = 10,
    /// <summary>
    /// 龙底座升级材料
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_DragonCompoundSlotUpgrade")] DragonCompoundSlotUpgrade = 11,
    /// <summary>
    /// 史莱姆招募道具
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_SlimeRecurit")] SlimeRecurit = 12,
    /// <summary>
    /// 悬空工厂升级道具
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_DragonFactoryLevelItemRob")] DragonFactoryLevelItemRob = 13,
    /// <summary>
    /// 其他建筑升级道具
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_DragonBuildResource")] DragonBuildResource = 14,
    /// <summary>
    /// 科技点道具获取
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_DragonFactoryTech")] DragonFactoryTech = 15,
    /// <summary>
    /// 公会科技道具（远古晶石
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_GuildFactoryItem")] GuildFactoryItem = 16,
    /// <summary>
    /// 技能突破道具获取
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_FashionBTSItem")] FashionBtsitem = 17,
  }

  /// <summary>
  ///=====================视频广告的类型开始==================================
  /// 激励视频
  /// </summary>
  public enum IronSourceADype {
    /// <summary>
    /// 0.表示不可用
    /// </summary>
    [pbr::OriginalName("IronSourceADype_NONE")] None = 0,
    /// <summary>
    /// 挂机奖励X1
    /// </summary>
    [pbr::OriginalName("IronSourceADype_OffLine")] OffLine = 1,
  }

  /// <summary>
  ///=====================公会系统开始==================================
  ///公会成员身份，保持权限递增的顺序
  /// </summary>
  public enum GuildPosition {
    /// <summary>
    ///普通成员
    /// </summary>
    [pbr::OriginalName("GuildPosition_Normal")] Normal = 0,
    /// <summary>
    ///精英成员
    /// </summary>
    [pbr::OriginalName("GuildPosition_Elite")] Elite = 1,
    /// <summary>
    ///长老（副会长）
    /// </summary>
    [pbr::OriginalName("GuildPosition_Vice")] Vice = 2,
    /// <summary>
    ///会长
    /// </summary>
    [pbr::OriginalName("GuildPosition_President")] President = 3,
  }

  /// <summary>
  ///公会操作（仅列出需要验证权限的操作）
  /// </summary>
  public enum GuildOpt {
    /// <summary>
    /// 0.无操作
    /// </summary>
    [pbr::OriginalName("GuildOpt_None")] None = 0,
    /// <summary>
    /// 1.修改旗帜
    /// </summary>
    [pbr::OriginalName("GuildOpt_EditIcon")] EditIcon = 1,
    /// <summary>
    /// 2.修改名字
    /// </summary>
    [pbr::OriginalName("GuildOpt_EditName")] EditName = 2,
    /// <summary>
    /// 3.修改宣言
    /// </summary>
    [pbr::OriginalName("GuildOpt_EditNotice")] EditNotice = 3,
    /// <summary>
    /// 4.修改申请条件
    /// </summary>
    [pbr::OriginalName("GuildOpt_EditApply")] EditApply = 4,
    /// <summary>
    /// 5.申请列表查看
    /// </summary>
    [pbr::OriginalName("GuildOpt_ApplyMgrList")] ApplyMgrList = 5,
    /// <summary>
    /// 6.同意申请
    /// </summary>
    [pbr::OriginalName("GuildOpt_ApplyMgrAgree")] ApplyMgrAgree = 6,
    /// <summary>
    /// 7.拒绝申请
    /// </summary>
    [pbr::OriginalName("GuildOpt_ApplyMgrRefuse")] ApplyMgrRefuse = 7,
    /// <summary>
    /// 8.成员管理列表查看
    /// </summary>
    [pbr::OriginalName("GuildOpt_MemberMgrList")] MemberMgrList = 8,
    /// <summary>
    /// 9.任命会长
    /// </summary>
    [pbr::OriginalName("GuildOpt_MemberMgrPresident")] MemberMgrPresident = 9,
    /// <summary>
    /// 10.任命副会长
    /// </summary>
    [pbr::OriginalName("GuildOpt_MemberMgrVice")] MemberMgrVice = 10,
    /// <summary>
    /// 11.认命精英
    /// </summary>
    [pbr::OriginalName("GuildOpt_MemberMgrElite")] MemberMgrElite = 11,
    /// <summary>
    /// 12.任命为普通成员
    /// </summary>
    [pbr::OriginalName("GuildOpt_MemberMgrNormal")] MemberMgrNormal = 12,
    /// <summary>
    /// 13.踢出公会
    /// </summary>
    [pbr::OriginalName("GuildOpt_MemberMgrKick")] MemberMgrKick = 13,
    /// <summary>
    /// 14.退出公会
    /// </summary>
    [pbr::OriginalName("GuildOpt_Quit")] Quit = 14,
    /// <summary>
    /// 15.解散公会
    /// </summary>
    [pbr::OriginalName("GuildOpt_Dismiss")] Dismiss = 15,
    /// <summary>
    /// 16.弹劾会长
    /// </summary>
    [pbr::OriginalName("GuildOpt_Impeach")] Impeach = 16,
    /// <summary>
    /// 17.发送世界邀请
    /// </summary>
    [pbr::OriginalName("GuildOpt_SendWorldInvite")] SendWorldInvite = 17,
    /// <summary>
    /// 18.发送私聊邀请
    /// </summary>
    [pbr::OriginalName("GuildOpt_SendPlayerInvite")] SendPlayerInvite = 18,
    /// <summary>
    /// 19.提醒砍价
    /// </summary>
    [pbr::OriginalName("GuildOpt_BargainingNotice")] BargainingNotice = 19,
    /// <summary>
    /// 20.修改公告
    /// </summary>
    [pbr::OriginalName("GuildOpt_EditAnnouncement")] EditAnnouncement = 20,
    /// <summary>
    /// 21.一键拒绝所有申请
    /// </summary>
    [pbr::OriginalName("GuildOpt_ApplyMgrRejectAll")] ApplyMgrRejectAll = 21,
  }

  /// <summary>
  ///公会日志类型
  /// </summary>
  public enum GuildLogType {
    /// <summary>
    ///0.离开公会
    /// </summary>
    [pbr::OriginalName("GuildLogType_Leave")] Leave = 0,
    /// <summary>
    ///1.加入公会
    /// </summary>
    [pbr::OriginalName("GuildLogType_Join")] Join = 1,
    /// <summary>
    ///2.弹劾成功
    /// </summary>
    [pbr::OriginalName("GuildLogType_Impeach1")] Impeach1 = 2,
    /// <summary>
    ///3.被弹劾
    /// </summary>
    [pbr::OriginalName("GuildLogType_Impeach2")] Impeach2 = 3,
    /// <summary>
    ///4.任命副会长
    /// </summary>
    [pbr::OriginalName("GuildLogType_MgrVice")] MgrVice = 4,
    /// <summary>
    ///5.任命为普通成员
    /// </summary>
    [pbr::OriginalName("GuildLogType_MgrNormal")] MgrNormal = 5,
    /// <summary>
    ///6.捐献增加经验
    /// </summary>
    [pbr::OriginalName("GuildLogType_Donate")] Donate = 6,
    /// <summary>
    ///7.成为会长
    /// </summary>
    [pbr::OriginalName("GuildLogType_President")] President = 7,
    /// <summary>
    ///8.转让成功为会长
    /// </summary>
    [pbr::OriginalName("GuildLogType_AutoChange1")] AutoChange1 = 8,
    /// <summary>
    ///9.被转让出会长，降为成员
    /// </summary>
    [pbr::OriginalName("GuildLogType_AutoChange2")] AutoChange2 = 9,
  }

  /// <summary>
  /// 公会通知
  /// 用于 G2PGuildGeneralUpdateNtf，告知 Player Actor 其公会相关状态需要更新的具体类型
  /// </summary>
  public enum GuildUpdateType {
    /// <summary>
    /// 未知或默认类型，不应实际使用
    /// </summary>
    [pbr::OriginalName("GuildUpdateType_UNKNOWN")] Unknown = 0,
    /// <summary>
    /// --- 玩家与公会的从属关系变化 ---
    /// </summary>
    [pbr::OriginalName("GuildUpdateType_JOINED_GUILD")] JoinedGuild = 1,
    /// <summary>
    /// 玩家主动退出了当前公会 场景: 玩家执行退出公会操作成功后。
    /// </summary>
    [pbr::OriginalName("GuildUpdateType_LEFT_GUILD")] LeftGuild = 2,
    /// <summary>
    /// 玩家被从当前公会踢出 场景: 官员执行踢人操作，目标是此玩家。
    /// </summary>
    [pbr::OriginalName("GuildUpdateType_KICKED_FROM_GUILD")] KickedFromGuild = 3,
    /// <summary>
    /// 玩家所在的公会被解散 (无论是会长主动还是系统自动) 场景: 玩家是某公会成员，该公会被解散。
    /// </summary>
    [pbr::OriginalName("GuildUpdateType_GUILD_DISMISSED")] GuildDismissed = 4,
    /// <summary>
    /// --- 玩家在公会内的状态变化 ---
    /// </summary>
    [pbr::OriginalName("GuildUpdateType_POSITION_CHANGED")] PositionChanged = 5,
    /// <summary>
    /// --- 公会本身的状态变化 (通知成员) ---
    /// </summary>
    [pbr::OriginalName("GuildUpdateType_GUILD_LEVEL_UP")] GuildLevelUp = 6,
    /// <summary>
    /// --- 玩家申请列表状态变化 ---
    /// </summary>
    [pbr::OriginalName("GuildUpdateType_APPLY_PROCESSED_OR_EXPIRED")] ApplyProcessedOrExpired = 7,
  }

  /// <summary>
  /// 公会申请记录的状态
  /// </summary>
  public enum GuildApplicationStatus {
    /// <summary>
    /// 未知或无效状态。
    /// </summary>
    [pbr::OriginalName("APPLICATION_STATUS_UNKNOWN")] ApplicationStatusUnknown = 0,
    /// <summary>
    /// 申请已提交，等待联盟官员审批。
    /// </summary>
    [pbr::OriginalName("APPLICATION_STATUS_PENDING")] ApplicationStatusPending = 1,
    /// <summary>
    /// 申请已被联盟官员批准。
    /// </summary>
    [pbr::OriginalName("APPLICATION_STATUS_APPROVED")] ApplicationStatusApproved = 2,
    /// <summary>
    /// 申请已被联盟官员拒绝。
    /// </summary>
    [pbr::OriginalName("APPLICATION_STATUS_REJECTED")] ApplicationStatusRejected = 3,
    /// <summary>
    /// 申请因超时（例如24小时未被处理）而自动失效。玩家未能加入联盟。
    /// </summary>
    [pbr::OriginalName("APPLICATION_STATUS_EXPIRED")] ApplicationStatusExpired = 4,
  }

  /// <summary>
  /// 公会内部通知类型
  /// </summary>
  public enum GuildSystemInternalActionType {
    [pbr::OriginalName("INTERNAL_ACTION_UNKNOWN")] InternalActionUnknown = 0,
    /// <summary>
    /// 公会解散，通知成员
    /// </summary>
    [pbr::OriginalName("INTERNAL_ACTION_DISMISS_GUILD_MEMBERS")] InternalActionDismissGuildMembers = 1,
    /// <summary>
    /// 联盟升级，通知成员
    /// </summary>
    [pbr::OriginalName("INTERNAL_ACTION_GUILD_LEVEL_UP_NOTIFY_MEMBERS")] InternalActionGuildLevelUpNotifyMembers = 2,
  }

  /// <summary>
  ///=====================公会系统结束===================================
  ///=====================聊天系统结束===================================
  ///聊天类型
  /// </summary>
  public enum ChatType {
    [pbr::OriginalName("ChatType_World")] World = 0,
    [pbr::OriginalName("ChatType_Guild")] Guild = 1,
    [pbr::OriginalName("ChatType_Private")] Private = 2,
  }

  /// <summary>
  ///聊天信息类型
  /// </summary>
  public enum ChatMsgType {
    /// <summary>
    ///普通文本
    /// </summary>
    [pbr::OriginalName("ChatMsgType_Text")] Text = 0,
    /// <summary>
    ///公会邀请信息
    /// </summary>
    [pbr::OriginalName("ChatMsgType_GuildInvite")] GuildInvite = 1,
    /// <summary>
    ///系统信息
    /// </summary>
    [pbr::OriginalName("ChatMsgType_System")] System = 2,
  }

  /// <summary>
  ///战斗内触发逻辑打开窗口类型
  /// </summary>
  public enum InBattleShowWindowType {
    /// <summary>
    ///三选一buff
    /// </summary>
    [pbr::OriginalName("UI_Select_NormalPop")] UiSelectNormalPop = 0,
    /// <summary>
    ///商店购买buff
    /// </summary>
    [pbr::OriginalName("UI_Select_ShopPop")] UiSelectShopPop = 1,
    /// <summary>
    ///击杀boss或精英怪物掉落buff
    /// </summary>
    [pbr::OriginalName("UI_Select_LuckyPop")] UiSelectLuckyPop = 2,
  }

  /// <summary>
  ///全局广播事件类型
  /// </summary>
  public enum GlobalBroadcastEventType {
    /// <summary>
    ///默认无处理
    /// </summary>
    [pbr::OriginalName("GlobalBroadcastEventType_None")] None = 0,
    /// <summary>
    ///全局封禁玩家所在公会的BOSS排行信息，伤害置为0
    /// </summary>
    [pbr::OriginalName("GlobalBroadcastEventType_BanPlayerGuildBossRank")] BanPlayerGuildBossRank = 1,
    /// <summary>
    ///强制修改玩家名称
    /// </summary>
    [pbr::OriginalName("GlobalBroadcastEventType_ChgPlayerName")] ChgPlayerName = 2,
    /// <summary>
    ///强制修改公会名称和公告信息
    /// </summary>
    [pbr::OriginalName("GlobalBroadcastEventType_ChgGuildNameAndNotice")] ChgGuildNameAndNotice = 3,
  }

  /// <summary>
  ///=====================七日签到开始===================================
  /// </summary>
  public enum SignInState {
    /// <summary>
    ///未签到
    /// </summary>
    [pbr::OriginalName("NoSignIn")] NoSignIn = 0,
    /// <summary>
    ///已签到未领取
    /// </summary>
    [pbr::OriginalName("SignedInNoRe")] SignedInNoRe = 1,
    /// <summary>
    ///已签到已领取
    /// </summary>
    [pbr::OriginalName("SignedInAndReed")] SignedInAndReed = 2,
  }

  /// <summary>
  ///=====================七日签到结束===================================
  ///======================支付开始=========================================
  /// </summary>
  public enum PaymentModuleType {
    /// <summary>
    /// 商店
    /// </summary>
    [pbr::OriginalName("Shop")] Shop = 0,
    /// <summary>
    /// 首充
    /// </summary>
    [pbr::OriginalName("FirstCharge")] FirstCharge = 1,
    /// <summary>
    /// 月卡
    /// </summary>
    [pbr::OriginalName("MonthlyCard")] MonthlyCard = 2,
    /// <summary>
    /// 基金
    /// </summary>
    [pbr::OriginalName("GradedFund")] GradedFund = 3,
    /// <summary>
    /// 月卡2.0
    /// </summary>
    [pbr::OriginalName("MonthlyCardNew")] MonthlyCardNew = 4,
    /// <summary>
    /// 限时商店(日、周、月礼包)
    /// </summary>
    [pbr::OriginalName("TimeShop")] TimeShop = 5,
  }

  /// <summary>
  ///======================任务开始=========================================
  ///任务状态
  /// </summary>
  public enum MissionState {
    /// <summary>
    ///未完成
    /// </summary>
    [pbr::OriginalName("MissionState_Unfinished")] Unfinished = 0,
    /// <summary>
    ///已完成
    /// </summary>
    [pbr::OriginalName("MissionState_Finished")] Finished = 1,
    /// <summary>
    ///已领取
    /// </summary>
    [pbr::OriginalName("MissionState_Received")] Received = 2,
  }

  /// <summary>
  ///任务类型
  /// </summary>
  public enum MissionType {
    /// <summary>
    ///主线任务
    /// </summary>
    [pbr::OriginalName("MissionType_Main")] Main = 0,
    /// <summary>
    ///日常任务
    /// </summary>
    [pbr::OriginalName("MissionType_Daily")] Daily = 1,
    /// <summary>
    ///七日任务
    /// </summary>
    [pbr::OriginalName("MissionType_SevenDay")] SevenDay = 2,
    /// <summary>
    ///成就任务
    /// </summary>
    [pbr::OriginalName("MissionType_Achievement")] Achievement = 3,
    /// <summary>
    ///天道修为
    /// </summary>
    [pbr::OriginalName("MissionType_HeavenlyDao")] HeavenlyDao = 4,
  }

  /// <summary>
  ///=====================千抽开始===================================
  ///千抽状态
  /// </summary>
  public enum DBGachaBonusState {
    /// <summary>
    ///未抽卡
    /// </summary>
    [pbr::OriginalName("NoPull")] NoPull = 0,
    /// <summary>
    ///已抽未领取
    /// </summary>
    [pbr::OriginalName("PulledNoRe")] PulledNoRe = 1,
    /// <summary>
    ///已抽已领取
    /// </summary>
    [pbr::OriginalName("PulledAndReed")] PulledAndReed = 2,
  }

  /// <summary>
  /// 错误码
  /// </summary>
  public enum ServerResultCode {
    /// <summary>
    /// 成功
    /// </summary>
    [pbr::OriginalName("ResultCode_SUCCES")] ResultCodeSucces = 0,
    /// <summary>
    ///  服务端报错
    /// </summary>
    [pbr::OriginalName("ResultCode_Error")] ResultCodeError = 1,
    /// <summary>
    /// 金币不足
    /// </summary>
    [pbr::OriginalName("ResultCode_Currency_Not_Enough")] ResultCodeCurrencyNotEnough = 2,
    /// <summary>
    /// 道具不足
    /// </summary>
    [pbr::OriginalName("ResultCode_Item_Not_Enough")] ResultCodeItemNotEnough = 3,
    /// <summary>
    /// 客户端参数错误
    /// </summary>
    [pbr::OriginalName("ResultCode_PARAM_ERROR")] ResultCodeParamError = 4,
    /// <summary>
    /// 配置不存在错误
    /// </summary>
    [pbr::OriginalName("ResultCode_CONFIG_NOT_CONTAINS")] ResultCodeConfigNotContains = 5,
  }

  /// <summary>
  ///=======================功能预告奖励================================
  /// </summary>
  public enum FuncPreviewstate {
    /// <summary>
    /// 未解锁
    /// </summary>
    [pbr::OriginalName("Lock")] Lock = 0,
    /// <summary>
    /// 可领奖
    /// </summary>
    [pbr::OriginalName("Award")] Award = 1,
    /// <summary>
    ///已领奖
    /// </summary>
    [pbr::OriginalName("Awarded")] Awarded = 2,
  }

  /// <summary>
  ///=================== 宝物系统开始 ========================
  /// 宝物抽卡的消耗类型
  /// </summary>
  public enum TreasureGachaCostType {
    [pbr::OriginalName("COST_TYPE_NONE")] CostTypeNone = 0,
    /// <summary>
    /// 消耗道具 (例如: 宝物抽卡券)
    /// </summary>
    [pbr::OriginalName("COST_TYPE_ITEM")] CostTypeItem = 1,
    /// <summary>
    /// 观看广告
    /// </summary>
    [pbr::OriginalName("COST_TYPE_AD")] CostTypeAd = 2,
  }

  #endregion

}

#endregion Designer generated code
