﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-4
//*********************************************************


namespace LiteFrame.Framework
{
    //组件的基类
    public class BaseComponent
    {
        public BaseComponent()
        {
            
        }
        //public virtual void OnCreate()
        //{
        //}

        //public virtual void OnEnable()
        //{
        //}
        //public virtual void OnDisable()
        //{
        //}

        //public virtual void OnUpdate(float deltaTime)
        //{
        //}

        //public virtual void OnLateUpdate(float deltaTime)
        //{
        //}

        //public virtual void OnDestroy()
        //{
        //}
    }

}